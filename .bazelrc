import %workspace%/../bazel_configs/.bazelrc
import %workspace%/../bazel_configs/hpc.bazelrc

try-import %workspace%/user.bazelrc

build:enable_ros --//:enable_ros=ON

# lock-on-road settings
build --copt='-DE<PERSON><PERSON>_NO_DEBUG'
build --copt='-DPCL_NO_PRECOMPILE'
build --copt='-DROSCPP_USE_TCP_NODELAY'
build --copt='-Wno-attributes'
build --copt='-DS2_USE_GLOG'
build --copt='-DCHURCH_BUILD'
build --copt='-DDRMETRICS_DISPOSE_OFF'

# pcl definitions
build --copt="-DDISABLE_ENSENSO"
build --copt="-DDISABLE_DAVIDSDK"
build --copt="-DDISABLE_DSSDK"
build --copt="-DDISABLE_PCAP"
build --copt="-DDISABLE_PNG"
build --copt="-DDISABLE_LIBUSB_1_0"

build --copt="-Wno-error=unused-parameter"
build --copt="-Wno-error=comment"
build --copt='-Werror=return-type' # function return-type not same with definition
build --copt='-Wno-unused-result'
build --copt='-fdiagnostics-color=always' # show compile warning color
build --copt='-fno-omit-frame-pointer'

build:nvidia --config=cuda_api
build:qualcomm --config=opencl_api
build:nvidia_x86_2004 --config=cuda_api
build:qualcomm_x86_2004 --config=opencl_api

build --config=enable_hidden_visibility
build --config=remote_cache_map_localization
build --config=werror
build --experimental_cc_implementation_deps

