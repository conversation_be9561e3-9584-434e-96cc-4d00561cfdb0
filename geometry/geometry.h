#pragma once

#include <boost/circular_buffer.hpp>
#include <boost/geometry.hpp>
#include <boost/geometry/algorithms/intersects.hpp>
#include <boost/geometry/geometries/linestring.hpp>
#include <boost/geometry/geometries/point_xy.hpp>
#include <boost/geometry/geometries/polygon.hpp>
#include <network/type.hpp>
#include <pcl/common/transforms.h>

#include "common/geometry.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "map/amap_drive_route_planning.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"

#include "common/point.h"
#include "common/types.h"
#include "lam_common/proto_adapter/blc_adapter.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

using Point2d = boost::geometry::model::d2::point_xy<double>;
using MultiPoint2d = boost::geometry::model::multi_point<Point2d>;
using Line2d = boost::geometry::model::linestring<Point2d>;
using Polygon2d = boost::geometry::model::polygon<Point2d>;

bool IsPoseValid(const ::common::Transformation3& pose);

double Distance(const Vector2d& p, const Vector2dVector& line);

double Distance(const Vector2d& point, const deeproute::common::Polyline& line);

double Distance(const deeproute::common::Polyline& line1,
                const deeproute::common::Polyline& line2);

double Distance(const Vector2d& point, const FMM::CORE::LineString& line);

double MinDistanceToCustomGraph(const Vector2d& point,
                                const FMM::NETWORK::CustomGraph& polylines);

double Length(const deeproute::common::Polyline& line);

bool ExtractLanesOfCurrentRoad(const deeproute::perception::RASMap& ras_map,
                               const ::common::Transformation3& pose,
                               deeproute::perception::RASMap* result);

void TransformPolyline(const deeproute::common::Polyline& line,
                       const ::common::Transformation3& transform,
                       deeproute::common::Polyline* transformed);

deeproute::common::Polyline TransformPolyline(
    const deeproute::common::Polyline& line,
    const ::common::Transformation3& transform);

void GetClosesPoint(const Vector3d& pos,
                    const deeproute::common::Polyline& line,
                    Vector3d* closest_pt);

bool IsLineToTheRight(const deeproute::common::Polyline& line,
                      const ::common::Transformation3& pose);

Vector2dVector CreateVerticalLine(const Vector2d& line_start,
                                  const Vector2d& line_end, double length = 30);

// note: this method can only be used for line with two pts
Vector2d ProjectPointToLine(const Vector2d& pt, const Vector2dVector& line);

struct Cluster {
  Vector2dVector pts;
  Vector2d center_pt;

  void AddPoint(const Vector2d& pt) {
    pts.push_back(pt);

    Vector2d sum(0, 0);
    for (const auto pt : pts) {
      sum += pt;
    }
    center_pt = sum / pts.size();
  }

  Vector2d GetCenter() const { return center_pt; }
};

Vector2dVector ClusterPoints(const Vector2dVector& pts,
                             const double dis_thres = 1);

bool ReformulatedLaneTopology(const deeproute::perception::RASMap& ras,
                              deeproute::perception::RASMap* result_ras_map);

bool GetEgoLaneConnectedLanes(const deeproute::perception::RASMap& ras,
                              deeproute::perception::RASMap* result_ras_map);

std::string SimplyfyRasMapLanes(const deeproute::perception::RASMap& ras);

std::string SimplyfyRasMapLanes(const deeproute::perception::RASMap& ras,
                                const ::common::Transformation3& pose);

bool InsDataValid(const drivers::gnss::Ins& ins);

bool ConvertPointCloudToPolygon(const PointCloudXYZIRT& cloud, Polygon2d* hull,
                                Vector2dVector* outer);

// bool CreateLocalMap(
//     const ::common::PoseInterpolator& pose_interpolator,
//     const boost::circular_buffer<
//         std::pair<::common::TimeMicro, PointCloudXYZIRT>>&
//         time_to_cloud_pairs,
//     PointCloudXYZIRT* combined_cloud);

bool Intersects(const deeproute::common::Polyline& line,
                const Polygon2d& convex_hull);

bool FindLaneIdGivenLaneIndex(const deeproute::perception::RASMap& ras_map,
                              const int lane_index, int32_t* lane_id);

//////////////////////////////////////////////////////////////////////

struct AmapStatus {
  bool valid = false;
  int64_t time_us;
  int curr_link_id;
  int curr_step_id;
  int amap_status;
  double distance_to_next_link;
  double distance_to_next_step;
  double lat_gcj02;
  double lon_gcj02;
  double matched_lat_gcj02;
  double matched_lon_gcj02;
  double matched_x_utm;
  double matched_y_utm;
  bool is_parallel_road;

  // not accurate
  //   bool in_tunnel ;

  std::unordered_map<int, deeproute::map::AmapStep>
      amap_link_id_to_amap_step_gcj02;

  friend std::ostream& operator<<(std::ostream& os, const AmapStatus& status) {
    os << std::fixed << std::setprecision(9) << "AmapStatus: {";
    os << "  time_us: " << status.time_us << ",";
    os << "  curr_link_id: " << status.curr_link_id << ",";
    os << "  curr_step_id: " << status.curr_step_id << ",";
    os << "  distance_to_next_step: " << status.distance_to_next_step << ",";
    os << "  distance_to_next_link: " << status.distance_to_next_link << ",";
    os << "  curr_position lat_gcj02: " << status.lat_gcj02 << ",";
    os << "  curr_position lon_gcj02: " << status.lon_gcj02 << ",";
    os << "}";
    return os;
  }
};

double distance(const Vector2d& p1, const Vector2d& p2);

// find a point on the polyline that is closest to the current
Vector2d FindPoint(const ::google::protobuf::RepeatedPtrField<
                       ::deeproute::common::PointLLH>& polyline_llh,
                   const Vector2d curr_pose);

// find a point on the polyline that is forward_movement away from the current
Vector2d FindPoint(const std::unordered_map<int, deeproute::map::AmapStep>&
                       amap_step_id_to_amap_step_utm,
                   const int curr_step_id, const Vector2d curr_pose,
                   const double forward_movement);

// Vector2d FindPoint(const deeproute::map::AmapLink& link,
//                    const Vector2d curr_pose);
}  // namespace localization
}  // namespace deeproute
