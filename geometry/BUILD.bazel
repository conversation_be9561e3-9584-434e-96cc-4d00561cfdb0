package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")

cc_library(
  name = "ras_map_process",
  hdrs = ["ras_map_process.h"],
  srcs = ["ras_map_process.cpp"],
  deps = [
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        ":geometry",
        "@common//transform:transformation",
        "@common//common:event_log_handle",
        "@common//common:geometry",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:time",
        "@common//common:time_types",
  ],
)

cc_test(
  name = "geom_test",
  srcs = ["geom_test.cpp"],
  deps = [
        ":geometry",
        ":ras_map_process",
        "//third_party/eigen_checks:gtest",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:types",
        "@common//common:types",
        "@gtest//:gtest_main",
  ],
  linkstatic = True,
)

cc_library(
  name = "geometry",
  hdrs = ["geometry.h"],
  srcs = ["geometry.cpp"],
  deps = [
        "@lam_common//lam_common/proto_adapter:blc_adapter",
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/common:geometry_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/map:amap_drive_route_planning_proto_cc",
        "@common//common:geometry",
        "@common//common:point",
        "@common//common:time_based_interpolation",
        "@common//common:types",
        "@common//transform:transformation",
        "@lam_common//lam_common:logging",
        "@fmm//:fmm",
  ],
)

