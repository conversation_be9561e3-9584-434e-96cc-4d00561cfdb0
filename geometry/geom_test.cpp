// Copyright 2019 DeepRoute.ai. All Rights Reserved.

#include <math.h>

#include <fstream>
#include <iostream>

#include <gtest/gtest.h>

#include "common/log.h"
#include "common/types.h"
#include "geometry/geometry.h"
#include "geometry/ras_map_process.h"
#include "lam_common/types.h"
#include "third_party/eigen_checks/gtest.h"

namespace deeproute {
namespace localization {

TEST(Geom, geom) {
  Vector2d pt(0, 0);
  deeproute::common::Polyline polyline;
  auto& pt1 = *polyline.add_point();
  pt1.set_x(1);
  pt1.set_y(1);
  pt1.set_z(0);

  auto& pt2 = *polyline.add_point();
  pt2.set_x(2);
  pt2.set_y(2);
  pt2.set_z(0);
  //   MLOG(INFO) << polyline.DebugString();
  const double dist = Distance(pt, polyline);

  //   MLOG(INFO) << "Dist: " << dist;
  EXPECT_EQ(dist, std::sqrt(2));
}

TEST(Geom, geom2) {
  Vector2d pt(0, 0);
  auto temp_pts = CreateVerticalLine(Vector2d(0, 0), Vector2d(10, 0));
  for (const auto& pt : temp_pts) {
    MLOG(INFO) << pt.transpose();
  }

  const Vector2d line1 = Vector2d(10, 0);
  const Vector2d line2 = temp_pts[0] - temp_pts.back();
  EXPECT_NEAR(line1.dot(line2), 0, 1e-6);

  auto temp_pts2 = CreateVerticalLine(Vector2d(5, 5), Vector2d(10, 10));
  for (const auto& pt : temp_pts2) {
    MLOG(INFO) << pt.transpose();
  }
}

TEST(Geom, geom4) {
  Vector2dVector line;
  line.push_back(Vector2d(5, 0));
  line.push_back(Vector2d(0, 5));
  Vector2d proj = ProjectPointToLine(Vector2d(5, 5), line);

  MLOG(INFO) << "PROJ: " << proj.transpose();
  EXPECT_NEAR(proj[0], 2.5, 1e-6);
  EXPECT_NEAR(proj[1], 2.5, 1e-6);

  Vector2dVector line2;
  line2.push_back(Vector2d(0, 0));
  line2.push_back(Vector2d(5, 0));
  Vector2d proj2 = ProjectPointToLine(Vector2d(2, 2), line2);

  MLOG(INFO) << "PROJ: " << proj2.transpose();
  EXPECT_NEAR(proj2[0], 2, 1e-6);
  EXPECT_NEAR(proj2[1], 0, 1e-6);

  Vector2dVector line3;
  line3.push_back(Vector2d(1, -5));
  line3.push_back(Vector2d(1, 5));
  Vector2d proj3 = ProjectPointToLine(Vector2d(2, 2), line3);

  MLOG(INFO) << "PROJ: " << proj3.transpose();
  EXPECT_NEAR(proj3[0], 1, 1e-6);
  EXPECT_NEAR(proj3[1], 2, 1e-6);
}

TEST(Geom, geom5) {
  Vector2dVector pts;
  pts.push_back(Vector2d(0, 0.1));
  pts.push_back(Vector2d(0.2, 0.1));
  // pts.push_back(Vector2d(0.5, 0.1));

  pts.push_back(Vector2d(1.4, 0.1));
  pts.push_back(Vector2d(1, 0.1));
  pts.push_back(Vector2d(1.2, 0.1));

  pts.push_back(Vector2d(2.5, 0.1));
  const auto clusters = ClusterPoints(pts);

  for (const auto& cluster : clusters) {
    MLOG(INFO) << "cluster: " << cluster.transpose();
  }

  EXPECT_NEAR(clusters[0][0], 0.1, 1e-6);
  EXPECT_NEAR(clusters[0][1], 0.1, 1e-6);
  EXPECT_NEAR(clusters[1][0], 1.2, 1e-6);
  EXPECT_NEAR(clusters[1][1], 0.1, 1e-6);
  EXPECT_NEAR(clusters[2][0], 2.5, 1e-6);
  EXPECT_NEAR(clusters[2][1], 0.1, 1e-6);
}

TEST(Geom, geom6) {
  int lane_index = -1;

  // 计算逻辑1
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 2, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 3, ClosestCurbStatus::LEFT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 2);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 2, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 3, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 1);

  // 计算逻辑1
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 3, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 2, ClosestCurbStatus::LEFT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 2);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 3, /*ob lane sum*/ 7,
                              /*ob lane idx*/ 4, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 1);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 3, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 4, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 2);

  // 计算逻辑1
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 5, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 2, ClosestCurbStatus::LEFT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 2);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 5, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 3, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 3);

  // 计算逻辑3
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 2, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 3, ClosestCurbStatus::NO_CURB,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 1);

  // 计算逻辑3
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 3, /*ob lane sum*/ 5,
                              /*ob lane idx*/ 3, ClosestCurbStatus::NO_CURB,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 2);

  // 计算逻辑1
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 5, /*ob lane sum*/ 3,
                              /*ob lane idx*/ 1, ClosestCurbStatus::LEFT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 1);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 5, /*ob lane sum*/ 3,
                              /*ob lane idx*/ 3, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 5);

  // 计算逻辑2
  EXPECT_EQ(AdjustMatchStatus(/*sd lane sum*/ 5, /*ob lane sum*/ 4,
                              /*ob lane idx*/ 3, ClosestCurbStatus::RIGHT,
                              &lane_index),
            true);
  EXPECT_EQ(lane_index, 4);

  MLOG(INFO) << "3/2:" << std::ceil(3.0 / 2.0);
}

TEST(Geom, geom7) {
  for (int i = -3 * M_PI; i < 3 * M_PI; i++) {
    MLOG(INFO) << "Angle and result: " << i << ", "
               << std::remainder(i, 2 * M_PI);
  }
}

namespace {
double ComputeEdgeHeadingInUtm(const double dx, const double dy) {
  constexpr double kDegreePerRad = 57.29577951308232;
  const double degree = std::atan2(dy, dx) * kDegreePerRad;
  const double result = ((int(degree) + 360) % 360) / kDegreePerRad;
  return result;
}
}  // namespace
TEST(Geom, geom8) {
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(1, 0);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(1, 1);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(0, 1);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(-1, 1);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(-1, 0);

  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(-1, -1);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(0, -1);
  MLOG(INFO) << "Heading: " << ComputeEdgeHeadingInUtm(1, -1);

  // MLOG(INFO) << "Heading: " << ComputeHeading(-1, 1);

  // MLOG(INFO) << "Heading: " << ComputeHeading(-1, 0);
  // MLOG(INFO) << "Heading: " << ComputeHeading(0, 1);
  // MLOG(INFO) << "Heading: " << ComputeHeading(1, 0);
  // MLOG(INFO) << "Heading: " << ComputeHeading(1, 0);
  // MLOG(INFO) << "Heading: " << ComputeHeading(1, 0);
}

}  // namespace localization
}  // namespace deeproute
