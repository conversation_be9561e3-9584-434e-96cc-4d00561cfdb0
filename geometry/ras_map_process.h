#pragma once

#include "perception/deeproute_perception_ras_map.pb.h"

#include "transform/transformation.h"

namespace deeproute {
namespace localization {

constexpr double kTypicalLaneWidth = 3.5;

enum ClosestCurbStatus { LEFT = 0, RIGHT = 1, NO_CURB = -1 };

bool LaneDirectionDiffersFromOnLaneVehicle(
    const perception::Lane& lane,
    const perception::PerceptionObstacles& obstacles,
    const ::common::Transformation3& ego_car_pose,
    const double heading_diff_threshold = 1);

bool AdjustMatchStatus(const int sd_lane_sum, const int ob_lane_sum,
                       const int ob_lane_idx,
                       const ClosestCurbStatus& curb_match_status,
                       int* adjusted_lane_index);

bool ProcessRasMapByRefLine(
    const ::common::Transformation3& pose,
    const std::shared_ptr<const deeproute::perception::RASMap>& ras_map,
    const ClosestCurbStatus& prev_curb_status, int* lane_index, int* lane_sum,
    int32_t* lane_id, int64_t* ras_map_time, double* dist_to_egolane,
    ClosestCurbStatus* closest_curb_result, double* closest_curb_distance,
    std::vector<int32_t>* valid_left_to_right_lane_id,
    deeproute::perception::RASMap* debug_ras_map);

bool ProcessRasMapByTopology(
    const ::common::Transformation3& pose,
    const std::shared_ptr<const deeproute::perception::RASMap>& ras_map,
    int* lane_index, int* lane_sum, int32_t* lane_id, int64_t* ras_map_time,
    double* dist_to_egolane, ClosestCurbStatus* closest_curb_result,
    double* closest_curb_distance,
    deeproute::perception::RASMap* debug_ras_map);

bool ProcessRasMap(const ::common::Transformation3& pose,
                   const deeproute::perception::RASMap& ras_map,
                   const bool use_raw_topology, int* lane_index, int* lane_sum,
                   int32_t* lane_id, int64_t* ras_map_time,
                   double* dist_to_egolane,
                   ClosestCurbStatus* closest_curb_result,
                   deeproute::perception::RASMap* debug_ras_map);

}  // namespace localization
}  // namespace deeproute
