#include "geometry/geometry.h"

#include <cstddef>

#include <boost/geometry.hpp>
#include <boost/geometry/algorithms/length.hpp>

#include "common/log.h"

namespace deeproute {
namespace localization {

namespace {

// double Distance(const Vector2dVector& line_1,
//                 const Vector2dVector& line_2) {
//   Line2d line_seg_1;
//   for (const auto& point : line_1) {
//     line_seg_1.push_back(Point2d(point[0], point[1]));
//   }

//   Line2d line_seg_2;
//   for (const auto& point : line_2) {
//     line_seg_2.push_back(Point2d(point[0], point[1]));
//   }

//   return boost::geometry::distance(line_seg_1, line_seg_2);
// }

void CreatePolygon(const std::vector<common::Polyline>& lines,
                   Polygon2d* polygon) {
  //   std::vector<Point2d> points;
  for (const auto& line : lines) {
    for (const auto& pt : line.point()) {
      //   points += Point2d(pt.x(), pt.y());
      polygon->outer().push_back(Point2d(pt.x(), pt.y()));
    }
  }

  //   boost::geometry::assign_points(*polygon, points);
}

bool LineWithinPolygon(const common::Polyline& polyline,
                       const Polygon2d& polygon) {
  for (const auto& pt : polyline.point()) {
    Point2d point(pt.x(), pt.y());
    if (boost::geometry::within(point, polygon)) return true;
  }
  return false;
}

Line2d PolyLineToBoostLine(const deeproute::common::Polyline& line) {
  Line2d line_seg;
  for (const auto& point : line.point()) {
    line_seg.push_back(Point2d(point.x(), point.y()));
  }

  return line_seg;
}

}  // namespace

bool IsPoseValid(const ::common::Transformation3& pose) {
  if (!std::isfinite(pose.GetTranslation()[0]) ||
      !std::isfinite(pose.GetTranslation()[1]) ||
      !std::isfinite(pose.GetTranslation()[2]) ||
      !std::isfinite(pose.GetRollPitchYaw()[0]) ||
      !std::isfinite(pose.GetRollPitchYaw()[1]) ||
      !std::isfinite(pose.GetRollPitchYaw()[2])) {
    MLOG(WARN) << "Invalid pose: \n" << pose.GetTransformationMatrix();
    return false;
  }

  return true;
}

double Distance(const Vector2d& p, const Vector2dVector& line) {
  Point2d pt(p[0], p[1]);

  Line2d line_seg;
  line_seg.reserve(line.size());
  for (const auto& point : line) {
    line_seg.push_back(Point2d(point[0], point[1]));
  }

  return boost::geometry::distance(pt, line_seg);
}

double Distance(const deeproute::common::Polyline& line1,
                const deeproute::common::Polyline& line2) {
  const auto line_seg_1 = PolyLineToBoostLine(line1);
  const auto line_seg_2 = PolyLineToBoostLine(line2);

  return boost::geometry::distance(line_seg_1, line_seg_2);
}

double Distance(const Vector2d& point, const common::Polyline& line) {
  Vector2dVector line_seg;

  for (const auto& pt : line.point()) {
    line_seg.push_back(Vector2d(pt.x(), pt.y()));
  }

  return Distance(point, line_seg);
}

double Distance(const Vector2d& point, const FMM::CORE::LineString& line) {
  Vector2dVector line_seg;

  for (int i = 0; i < line.get_num_points(); i++) {
    line_seg.push_back(Vector2d(line.get_x(i), line.get_y(i)));
  }

  return Distance(point, line_seg);
}

double MinDistanceToCustomGraph(const Vector2d& point,
                                const FMM::NETWORK::CustomGraph& graph) {
  double min_distance = std::numeric_limits<double>::max();

  for (const auto& edge : graph) {
    const double distance = Distance(point, edge.geom);

    if (distance < min_distance) {
      min_distance = distance;
    }
  }

  return min_distance;
}

// find the most likely two edges and the lanes fall within
bool ExtractLanesOfCurrentRoad(const deeproute::perception::RASMap& ras_map,
                               const ::common::Transformation3& pose,
                               deeproute::perception::RASMap* result) {
  if (ras_map.edge_size() < 2) {
    MLOG(INFO) << "Current ras map has invalid number of edges: "
               << ras_map.edge_size();
    return false;
  }

  std::vector<deeproute::perception::RoadEdge> edges;
  for (const auto& edge : ras_map.edge()) {
    edges.push_back(edge);
  }

  Vector2d position_xy(pose.GetTranslation().x(), pose.GetTranslation().y());
  struct PtToLineDistance {
   public:
    PtToLineDistance(const Vector2d& position) : position_(position) {}
    bool operator()(const deeproute::perception::RoadEdge& edge1,
                    const deeproute::perception::RoadEdge& edge2) const {
      return Distance(position_, edge1.polyline()) <
             Distance(position_, edge2.polyline());
    }
    Vector2d position_;
  };
  std::sort(edges.begin(), edges.end(), PtToLineDistance(position_xy));

  const auto e1 = edges[0];
  const auto e2 = edges[1];

  std::vector<common::Polyline> lines = {e1.polyline(), e2.polyline()};
  Polygon2d polygon;
  CreatePolygon(lines, &polygon);

  for (const auto& lane : ras_map.lanes()) {
    if (LineWithinPolygon(lane.centerline(), polygon)) {
      *result->add_lanes() = lane;
    }
  }

  return result->lanes_size();
}

double Length(const deeproute::common::Polyline& line) {
  Line2d line_seg;
  for (const auto& point : line.point()) {
    line_seg.push_back(Point2d(point.x(), point.y()));
  }

  return boost::geometry::length(line_seg);
}

void TransformPolyline(const deeproute::common::Polyline& line,
                       const ::common::Transformation3& transform,
                       deeproute::common::Polyline* transformed) {
  for (const auto& pt : line.point()) {
    const Vector3d transformed_pt =
        transform * Vector3d(pt.x(), pt.y(), pt.z());
    deeproute::common::Point3D new_pt;
    new_pt.set_x(transformed_pt[0]);
    new_pt.set_y(transformed_pt[1]);
    new_pt.set_z(transformed_pt[2]);

    *transformed->add_point() = new_pt;
  }
}

deeproute::common::Polyline TransformPolyline(
    const deeproute::common::Polyline& line,
    const ::common::Transformation3& transform) {
  deeproute::common::Polyline transformed;
  for (const auto& pt : line.point()) {
    const Vector3d transformed_pt =
        transform * Vector3d(pt.x(), pt.y(), pt.z());
    deeproute::common::Point3D new_pt;
    new_pt.set_x(transformed_pt[0]);
    new_pt.set_y(transformed_pt[1]);
    new_pt.set_z(transformed_pt[2]);

    *transformed.add_point() = new_pt;
  }

  return transformed;
}

void GetClosesPoint(const Vector3d& pos,
                    const deeproute::common::Polyline& line,
                    Vector3d* closest_pt) {
  double min_distance = 1e6;
  for (const auto& pt : line.point()) {
    const double distance = (Vector3d(pt.x(), pt.y(), pt.z()) - pos).norm();
    if (distance < min_distance) {
      min_distance = distance;
      *closest_pt = Vector3d(pt.x(), pt.y(), pt.z());
    }
  }
}

bool IsLineToTheRight(const deeproute::common::Polyline& line,
                      const ::common::Transformation3& pose) {
  Vector3d closest_pt;
  GetClosesPoint(pose.GetTranslation(), line, &closest_pt);

  closest_pt = pose.Inverse() * closest_pt;

  return closest_pt[1] < 0;
}

Vector2dVector CreateVerticalLine(const Vector2d& line_start,
                                  const Vector2d& line_end, double length) {
  const double x2 = line_start[0];
  const double y2 = line_start[1];
  const double x1 = line_end[0];
  const double y1 = line_end[1];
  const double xdiff = x2 - x1;
  const double ydiff = y2 - y1;

  const Vector2d vertical_line_start(x2 - ydiff / 2, y2 + xdiff / 2);
  const Vector2d vertical_line_end(x2 + ydiff / 2, y2 - xdiff / 2);
  const Vector2d line = (vertical_line_end - vertical_line_start).normalized();
  Vector2d new_line_start = line_start + line * length / 2;
  Vector2d new_line_end = line_start - line * length / 2;

  Vector2dVector vertical_line_pts;
  vertical_line_pts.push_back(new_line_start);
  vertical_line_pts.push_back(new_line_end);
  return vertical_line_pts;
}

// note: this method can only be used for line with two pts
Vector2d ProjectPointToLine(const Vector2d& pt, const Vector2dVector& line) {
  assert(line.size() == 2);

  const Vector2d ab = line[1] - line[0];
  const Vector2d ap = pt - line[0];
  double t = ap.dot(ab) / (ab.norm() * ab.norm());
  if (t < 0) t = 0;
  if (t > 1) t = 1;

  return line[0] + t * ab;
}

namespace {
double Distance(const Vector2d& pt, const Cluster& cluster) {
  return (pt - cluster.center_pt).norm();
}

void FindClosestCluster(const Vector2d& pt,
                        const std::vector<Cluster>& clusters,
                        int* closest_cluster_id, double* dist) {
  Cluster closest_cluster;
  double min_dist = std::numeric_limits<double>::max();
  for (size_t i = 0; i < clusters.size(); i++) {
    const double dist = Distance(pt, clusters[i]);
    if (dist < min_dist) {
      min_dist = dist;
      *closest_cluster_id = i;
    }
  }

  *dist = min_dist;
}

}  // namespace

Vector2dVector ClusterPoints(const Vector2dVector& pts,
                             const double dis_thres) {
  std::vector<Cluster> clusters, new_clusters;
  Cluster cluster;
  cluster.AddPoint(pts[0]);
  clusters.push_back(cluster);

  for (size_t i = 1; i < pts.size(); i++) {
    int closest_cluster_id = -1;
    double dist = -1;
    FindClosestCluster(pts[i], clusters, &closest_cluster_id, &dist);

    if (dist < dis_thres) {
      clusters[closest_cluster_id].AddPoint(pts[i]);
      MLOG(INFO) << "Pt , dist and center " << pts[i] << ", " << dist << ", "
                 << clusters[closest_cluster_id].GetCenter().transpose();
    } else {
      Cluster new_cluster;
      new_cluster.AddPoint(pts[i]);
      clusters.push_back(new_cluster);
      MLOG(INFO) << "New cluster!";
    }
  }

  Vector2dVector cluster_centers;
  for (const auto& cluster : clusters) {
    cluster_centers.push_back(cluster.GetCenter());
  }
  return cluster_centers;
}

bool ReformulatedLaneTopology(const deeproute::perception::RASMap& ras,
                              deeproute::perception::RASMap* result_ras_map) {
  std::unordered_map<int32_t, perception::Lane> id_to_lanes;
  perception::Lane ego_lane;
  ego_lane.set_id(-1);
  for (const auto& lane : ras.lanes()) {
    id_to_lanes[lane.id()] = lane;
    if (lane.is_ego_lane()) {
      ego_lane = lane;
    }
  }
  if (ego_lane.id() == -1) {
    MLOG(WARN) << "No ego lane information found in this ras map.";
    return false;
  }

  std::vector<perception::Lane> left_lanes, right_lanes, sorted_lanes;
  auto temp_left_lane = ego_lane;
  while (temp_left_lane.has_left_neighbor_id() &&
         !temp_left_lane.is_left_neighbor_reverse()) {
    left_lanes.push_back(id_to_lanes[temp_left_lane.left_neighbor_id()]);
    temp_left_lane = id_to_lanes[temp_left_lane.left_neighbor_id()];
  }
  auto temp_right_lane = ego_lane;
  while (temp_right_lane.has_right_neighbor_id() &&
         !temp_right_lane.is_right_neighbor_reverse()) {
    right_lanes.push_back(id_to_lanes[temp_right_lane.right_neighbor_id()]);
    temp_right_lane = id_to_lanes[temp_right_lane.right_neighbor_id()];
  }

  sorted_lanes.insert(sorted_lanes.end(), right_lanes.begin(),
                      right_lanes.end());
  sorted_lanes.push_back(ego_lane);
  sorted_lanes.insert(sorted_lanes.end(), left_lanes.begin(), left_lanes.end());

  for (const auto& lane : sorted_lanes) {
    MLOG(INFO) << lane.id();
  }

  for (size_t i = 0; i < sorted_lanes.size(); i++) {
    if (!sorted_lanes[i].virtual_()) {
      for (size_t j = i; j < sorted_lanes.size(); j++) {
        if (j == i) {
          continue;
        }

        if (!sorted_lanes[j].virtual_()) {
          // do not set left neighor for left most lane
          if (i != sorted_lanes.size() - 1) {
            sorted_lanes[i].set_left_neighbor_id(sorted_lanes[j].id());
          }

          // do not set right neighor for right most lane
          if (j != 0) {
            sorted_lanes[j].set_right_neighbor_id(sorted_lanes[i].id());
          }

          i = j;
          break;
        }
      }
    }
  }

  *result_ras_map = ras;
  result_ras_map->clear_lanes();
  for (const auto& lane : sorted_lanes) {
    if (lane.virtual_()) {
      continue;
    }
    *result_ras_map->add_lanes() = lane;
  }

  return true;
}

bool GetEgoLaneConnectedLanes(const deeproute::perception::RASMap& ras,
                              deeproute::perception::RASMap* result_ras_map) {
  std::unordered_map<int32_t, perception::Lane> id_to_lanes;
  perception::Lane ego_lane;
  ego_lane.set_id(-1);
  for (const auto& lane : ras.lanes()) {
    id_to_lanes[lane.id()] = lane;
    if (lane.is_ego_lane()) {
      ego_lane = lane;
    }
  }
  if (ego_lane.id() == -1) {
    MLOG(WARN) << "No ego lane information found in this ras map.";
    return false;
  }

  std::vector<perception::Lane> left_lanes, right_lanes, all_lanes;
  auto temp_left_lane = ego_lane;
  while (temp_left_lane.has_left_neighbor_id() &&
         !temp_left_lane.is_left_neighbor_reverse()) {
    left_lanes.push_back(id_to_lanes[temp_left_lane.left_neighbor_id()]);
    temp_left_lane = id_to_lanes[temp_left_lane.left_neighbor_id()];

    *result_ras_map->add_lanes() = temp_left_lane;
  }
  auto temp_right_lane = ego_lane;
  while (temp_right_lane.has_right_neighbor_id() &&
         !temp_right_lane.is_right_neighbor_reverse()) {
    right_lanes.push_back(id_to_lanes[temp_right_lane.right_neighbor_id()]);
    temp_right_lane = id_to_lanes[temp_right_lane.right_neighbor_id()];
    *result_ras_map->add_lanes() = temp_right_lane;
  }
  *result_ras_map->add_lanes() = ego_lane;
  return true;
}

std::string SimplyfyRasMapLanes(const deeproute::perception::RASMap& ras) {
  deeproute::perception::RASMap result;
  for (const auto& lane : ras.lanes()) {
    deeproute::perception::Lane temp_lane = lane;
    temp_lane.mutable_centerline()->clear_point();
    *temp_lane.mutable_centerline()->add_point() = lane.centerline().point()[0];
    const int pt_size = lane.centerline().point_size();
    *temp_lane.mutable_centerline()->add_point() =
        lane.centerline().point()[pt_size - 1];

    *result.add_lanes() = temp_lane;
  }

  for (const auto& edge : ras.edge()) {
    deeproute::perception::RoadEdge temp_edge = edge;
    temp_edge.mutable_polyline()->clear_point();
    *temp_edge.mutable_polyline()->add_point() = edge.polyline().point()[0];
    const int pt_size = edge.polyline().point_size();
    *temp_edge.mutable_polyline()->add_point() =
        edge.polyline().point()[pt_size - 1];

    *result.add_edge() = temp_edge;
  }

  return result.ShortDebugString();
}

std::string SimplyfyRasMapLanes(const deeproute::perception::RASMap& ras,
                                const ::common::Transformation3& pose) {
  deeproute::perception::RASMap result;
  for (const auto& lane : ras.lanes()) {
    const int pt_size = lane.centerline().point_size();
    // if (pt_size <= 2 && lane.id() != -1) {
    //   continue;
    // }

    deeproute::perception::Lane temp_lane = lane;
    temp_lane.mutable_centerline()->clear_point();
    *temp_lane.mutable_centerline()->add_point() = lane.centerline().point()[0];
    *temp_lane.mutable_centerline()->add_point() =
        lane.centerline().point()[pt_size - 1];

    *result.add_lanes() = temp_lane;
  }

  for (const auto& edge : ras.edge()) {
    const int pt_size = edge.polyline().point_size();
    if (pt_size <= 2) {
      continue;
    }

    deeproute::perception::RoadEdge temp_edge = edge;
    temp_edge.mutable_polyline()->clear_point();
    *temp_edge.mutable_polyline()->add_point() = edge.polyline().point()[0];
    *temp_edge.mutable_polyline()->add_point() =
        edge.polyline().point()[pt_size - 1];

    *result.add_edge() = temp_edge;
  }

  if (IsPoseValid(pose)) {
    deeproute::perception::MapElement position;
    position.set_id(-1);
    position.mutable_position()->set_x(pose.GetTranslation()[0]);
    position.mutable_position()->set_y(pose.GetTranslation()[1]);
    position.mutable_position()->set_z(pose.GetTranslation()[2]);
    *result.add_map_element() = position;
  } else {
    MLOG(INFO) << "odom pose invalid: \n" << pose.GetTransformationMatrix();
  }

  // for convex hull perimeters
  for (const auto& map_element : ras.map_element()) {
    *result.add_map_element() = map_element;
  }

  return result.ShortDebugString();
}

bool InsDataValid(const drivers::gnss::Ins& ins) {
  const bool pose_valid = std::isfinite(ins.position().x()) &&
                          std::isfinite(ins.position().y()) &&
                          std::isfinite(ins.position().z());

  if (pose_valid) {
    return true;
  } else {
    MLOG(INFO) << "gnss pose invalid: " << ins.ShortDebugString();
    return false;
  }
}

bool ConvertPointCloudToPolygon(const PointCloudXYZIRT& cloud, Polygon2d* hull,
                                Vector2dVector* outer) {
  if (cloud.points.empty()) {
    MLOG(WARN) << "Converting pts to hull failed, pts empty with size: "
               << cloud.points.size();
    return false;
  }

  MultiPoint2d multi_pts;
  for (const auto& pt : cloud.points) {
    multi_pts.emplace_back(pt.x, pt.y);
  }
  boost::geometry::convex_hull(multi_pts, *hull);

  if (hull->outer().empty()) {
    MLOG(WARN) << "Converted convex hull empty, Converting cloud failed.";
    return false;
  }

  for (auto it = hull->outer().begin(); it != hull->outer().end(); ++it) {
    outer->push_back(Vector2d(it->x(), it->y()));
  }
  MLOG(INFO) << "Convereted perimeter point size: " << outer->size();
  return true;
}

// bool CreateLocalMap(
//     const ::common::PoseInterpolator& pose_interpolator,
//     const boost::circular_buffer<
//         std::pair<::common::TimeMicro, PointCloudXYZIRT>>&
//         time_to_cloud_pairs,
//     PointCloudXYZIRT* combined_cloud) {
//   if (time_to_cloud_pairs.empty()) {
//     return false;
//   }

//   for (const auto& time_to_cloud : time_to_cloud_pairs) {
//     ::common::Transformation3 pose;
//     if (!pose_interpolator.Evaluate(time_to_cloud.first, &pose)) {
//       MLOG(WARN) << "Evaluating pose failed.";
//       continue;
//     }

//     PointCloudXYZIRT transformed_cloud;
//     pcl::transformPointCloud(time_to_cloud.second, transformed_cloud,
//                              pose.GetTransformationMatrix());
//     *combined_cloud += transformed_cloud;
//   }
//   if (combined_cloud->points.empty()) {
//     return false;
//   }

//   return true;
// }

bool Intersects(const deeproute::common::Polyline& line,
                const Polygon2d& convex_hull) {
  return boost::geometry::intersects(PolyLineToBoostLine(line), convex_hull);
}

bool FindLaneIdGivenLaneIndex(const deeproute::perception::RASMap& ras_map,
                              const int lane_index, int32_t* lane_id) {
  if (ras_map.lanes_size() == 0) {
    MLOG(INFO) << "No lanes found in ras map!";
    return false;
  }

  std::unordered_map<int32_t, deeproute::perception::Lane> id_to_lanes;
  deeproute::perception::Lane ego_lane;
  int ego_lane_num = 0;
  for (const auto& lane : ras_map.lanes()) {
    id_to_lanes[lane.id()] = lane;

    if (lane.is_ego_lane()) {
      ego_lane = lane;
      ego_lane_num += 1;
    }
  }

  if (ego_lane_num != 1) {
    MLOG(INFO) << "Ego lane num not 1: " << ego_lane_num;
    return false;
  }

  std::vector<int32_t> visited_left_lanes, visited_right_lanes;
  std::vector<deeproute::perception::Lane> left_lanes;
  std::vector<deeproute::perception::Lane> right_lanes;
  deeproute::perception::Lane left_temp_lane = ego_lane;
  while (left_temp_lane.has_left_neighbor_id() &&
         !left_temp_lane.is_left_neighbor_reverse()) {
    left_temp_lane = id_to_lanes[left_temp_lane.left_neighbor_id()];

    if (std::find(visited_left_lanes.begin(), visited_left_lanes.end(),
                  left_temp_lane.id()) != visited_left_lanes.end()) {
      MLOG(INFO) << "Left lane loop detected!";
      return false;
    }

    left_lanes.push_back(left_temp_lane);
    visited_left_lanes.push_back(left_temp_lane.id());
  }
  std::reverse(left_lanes.begin(), left_lanes.end());

  // count right non reversed lanes
  deeproute::perception::Lane right_temp_lane = ego_lane;
  while (right_temp_lane.has_right_neighbor_id() &&
         !right_temp_lane.is_right_neighbor_reverse()) {
    right_temp_lane = id_to_lanes[right_temp_lane.right_neighbor_id()];

    if (std::find(visited_right_lanes.begin(), visited_right_lanes.end(),
                  right_temp_lane.id()) != visited_right_lanes.end()) {
      MLOG(INFO) << "Right lane loop detected!";
      return false;
    }

    right_lanes.push_back(right_temp_lane);
    visited_right_lanes.push_back(right_temp_lane.id());
  }

  std::vector<deeproute::perception::Lane> ordered_lanes;
  ordered_lanes.insert(ordered_lanes.end(), left_lanes.begin(),
                       left_lanes.end());
  ordered_lanes.push_back(ego_lane);
  ordered_lanes.insert(ordered_lanes.end(), right_lanes.begin(),
                       right_lanes.end());

  if (static_cast<size_t>(lane_index) > ordered_lanes.size()) {
    MLOG(INFO) << "Cannot find lane id from lane index because lane index > "
               << ordered_lanes.size();
    return false;
  }

  *lane_id = ordered_lanes[lane_index - 1].id();
  return true;
}

double distance(const Vector2d& p1, const Vector2d& p2) {
  return std::sqrt(std::pow(p1.x() - p2.x(), 2) + std::pow(p1.y() - p2.y(), 2));
}

Vector2d FindPoint(const ::google::protobuf::RepeatedPtrField<
                       ::deeproute::common::PointLLH>& polyline_llh,
                   const Vector2d curr_pose) {
  std::vector<Vector2d> polyline_latlons;
  for (const auto& point : polyline_llh) {
    polyline_latlons.emplace_back(point.lat(), point.lon());
  }

  // Find the closest point on the polyline
  double min_dist = std::numeric_limits<double>::max();
  Vector2d closest_point;
  // int closest_index = -1;

  for (size_t i = 0; i + 1 < polyline_latlons.size(); i++) {
    const auto& p1 = polyline_latlons[i];
    const auto& p2 = polyline_latlons[i + 1];

    // Calculate the projection of the current pose onto the line segment
    double px = curr_pose.x();
    double py = curr_pose.y();
    double x1 = p1.x();
    double y1 = p1.y();
    double x2 = p2.x();
    double y2 = p2.y();
    double t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) /
               ((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
    t = std::max(0.0, std::min(1.0, t));
    double cx = x1 + t * (x2 - x1);
    double cy = y1 + t * (y2 - y1);

    double dist = distance(curr_pose, Vector2d(cx, cy));
    if (dist < min_dist) {
      min_dist = dist;
      closest_point = Vector2d(cx, cy);
      // closest_index = static_cast<int>(i);
    }
  }
  // MLOG(INFO) << "closest_point: " << closest_point.transpose()
  //            << ", closest_index: " << closest_index;

  return closest_point;
}

// find a point on the polyline that is forward_movement away from the current
Vector2d FindPoint(const std::unordered_map<int, deeproute::map::AmapStep>&
                       amap_step_id_to_amap_step_utm,
                   const int curr_step_id, const Vector2d curr_pose,
                   const double forward_movement) {
  std::vector<Vector2d> polyline_latlons;

  for (const auto& point :
       amap_step_id_to_amap_step_utm.at(curr_step_id).polyline_llh()) {
    polyline_latlons.emplace_back(point.lat(), point.lon());
  }
  if (amap_step_id_to_amap_step_utm.find(curr_step_id + 1) !=
      amap_step_id_to_amap_step_utm.end()) {
    const auto& next_step = amap_step_id_to_amap_step_utm.at(curr_step_id + 1);
    const auto& first_link_of_next_step = next_step.links(0);
    for (const auto& point : first_link_of_next_step.polyline_llh()) {
      polyline_latlons.emplace_back(point.lat(), point.lon());
    }
  }

  // Find the closest point on the polyline
  double min_dist = std::numeric_limits<double>::max();
  Vector2d closest_point;
  int closest_index = -1;

  for (size_t i = 0; i + 1 < polyline_latlons.size(); i++) {
    const auto& p1 = polyline_latlons[i];
    const auto& p2 = polyline_latlons[i + 1];

    // Calculate the projection of the current pose onto the line segment
    double px = curr_pose.x();
    double py = curr_pose.y();
    double x1 = p1.x();
    double y1 = p1.y();
    double x2 = p2.x();
    double y2 = p2.y();
    double t = ((px - x1) * (x2 - x1) + (py - y1) * (y2 - y1)) /
               ((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));

    t = std::max(0.0, std::min(1.0, t));

    double cx = x1 + t * (x2 - x1);
    double cy = y1 + t * (y2 - y1);

    double dist = distance(curr_pose, Vector2d(cx, cy));
    if (dist < min_dist) {
      min_dist = dist;
      closest_point = Vector2d(cx, cy);
      closest_index = static_cast<int>(i);
    }
  }

  // Now, find the point that is forward_movement away from the closest point
  double total_distance = 0.0;
  Vector2d target_point = closest_point;
  int target_index = closest_index;

  for (size_t i = closest_index; i + 1 < polyline_latlons.size(); i++) {
    const auto& p1 = polyline_latlons[i];
    const auto& p2 = polyline_latlons[i + 1];

    double segment_distance = distance(p1, p2);
    total_distance += segment_distance;

    if (total_distance >= forward_movement) {
      double remaining_distance = total_distance - forward_movement;
      double t = remaining_distance / segment_distance;
      target_point.x() = p1.x() + t * (p2.x() - p1.x());
      target_point.y() = p1.y() + t * (p2.y() - p1.y());
      target_index = i;
      break;
    }
  }

  MLOG(INFO) << "closest_point: " << closest_point.transpose()
             << ", closest_index: " << closest_index
             << ", forward moved target_point: " << target_point.transpose()
             << ", target_index: " << target_index;

  return target_point;
}
}  // namespace localization
}  // namespace deeproute
