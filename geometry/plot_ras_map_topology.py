import matplotlib.pyplot as plt
from google.protobuf import text_format

import numpy as np
import sys

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from perception.deeproute_perception_ras_map_pb2 import RASMap
# fmt: on


def load_ras_map_txt(path):
    f = open(path, "r")
    ras = RASMap()
    ras_map = text_format.Parse(f.read(), ras)
    # print(ras_map)

    return ras_map


def plot_ras_map(ras_map):

    lane_id_to_lane_mid = {}

    convex_hull_pts = []
    if hasattr(ras_map, 'map_element') and len(ras_map.map_element):
        for map_element in ras_map.map_element:          
            if map_element.id >0:
                plt.scatter([map_element.position.x],
                            [map_element.position.y], s=50, c='g')
            else:
                plt.scatter([map_element.position.x],
                            [map_element.position.y], s=50, c='b')
            print(map_element)
    
    for edge in ras_map.edge:

        start = edge.polyline.point[0]
        end = edge.polyline.point[-1]
        mid_x = (float(start.x) + float(end.x)) / 2
        mid_y = (float(start.y) + float(end.y)) / 2

        plt.plot([start.x, end.x], [start.y, end.y], c='r', ls='dotted')

        dy = end.y - start.y
        dx = end.x - start.x
        angle = np.rad2deg(np.arctan2(dy, dx))
        plt.text(mid_x, mid_y, str(edge.id), ha='left', va='bottom',
                 transform_rotates_text=True, rotation=angle, rotation_mode='anchor')

    for lane in ras_map.lanes:

        start = lane.centerline.point[0]
        end = lane.centerline.point[-1]
        mid_x = (float(start.x) + float(end.x)) / 2
        mid_y = (float(start.y) + float(end.y)) / 2

        lane_id_to_lane_mid[lane.id] = [mid_x, mid_y]

        plt.scatter([start.x],
                    [start.y], s=50, c='r')

        if lane.is_ego_lane:
            plt.plot([start.x, end.x], [start.y, end.y], c='r')
        elif lane.virtual:
            plt.plot([start.x, end.x], [start.y, end.y], c='r', ls='dotted')
        else:
            plt.plot([start.x, end.x], [start.y, end.y], c='g')

        dy = end.y - start.y
        dx = end.x - start.x
        angle = np.rad2deg(np.arctan2(dy, dx))
        plt.text(mid_x, mid_y, str(lane.id), ha='left', va='bottom',
                 transform_rotates_text=True, rotation=angle, rotation_mode='anchor')

    for lane in ras_map.lanes:
        left_lane_mid = None
        right_lane_mid = None

        print(lane.left_neighbor_id, lane.right_neighbor_id)
        if hasattr(lane, 'left_neighbor_id') and lane.left_neighbor_id != 0 and lane.left_neighbor_id in lane_id_to_lane_mid:
            left_lane_mid = lane_id_to_lane_mid[lane.left_neighbor_id]
        if hasattr(lane, 'right_neighbor_id') and lane.right_neighbor_id != 0 and lane.right_neighbor_id in lane_id_to_lane_mid:
            right_lane_mid = lane_id_to_lane_mid[lane.right_neighbor_id]

        lane_mid = lane_id_to_lane_mid[lane.id]

        # print(left_lane_mid, right_lane_mid)

        if left_lane_mid != None:
            plt.arrow(lane_mid[0],
                      lane_mid[1],
                      left_lane_mid[0] - lane_mid[0],
                      left_lane_mid[1] - lane_mid[1],
                      length_includes_head=True,
                      head_width=1)
        if right_lane_mid != None:
            plt.arrow(lane_mid[0],
                      lane_mid[1],
                      right_lane_mid[0] - lane_mid[0],
                      right_lane_mid[1] - lane_mid[1],
                      length_includes_head=True,
                      head_width=1)

    plt.show()


if __name__ == '__main__':
    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "in")
        sys.exit()

    ras_map = load_ras_map_txt(sys.argv[1])
    plot_ras_map(ras_map)
