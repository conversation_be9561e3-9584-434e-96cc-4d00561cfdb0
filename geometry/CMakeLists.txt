add_library(localization_geometry
        geometry.cpp
        ras_map_process.cpp
)
target_link_libraries(localization_geometry
        ${Boost_LIBRARIES}
        common_math
        common_transform
        ${GTEST_BOTH_LIBRARIES}
        lam_common_base
        common_time
        common_event_log
)

install(TARGETS
        localization_geometry
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})

# add_executable(geom_test geom_test.cpp)
# target_link_libraries(geom_test
#         localization_hmm
#         ${GTEST_BOTH_LIBRARIES}
#         lam_common_base
#         localization_geometry
#         boost_filesystem
# )

# add_test(geom_test ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/geom_test)