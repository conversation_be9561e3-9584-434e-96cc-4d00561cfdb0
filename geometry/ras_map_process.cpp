#include "geometry/ras_map_process.h"

#include <math.h>

#include <algorithm>
#include <cstdint>
#include <iostream>
#include <memory>
#include <vector>

#include <Eigen/Dense>
#include <boost/geometry/algorithms/distance.hpp>

#include "common/event_log_handle.h"
#include "common/log.h"
#include "common/time.h"
#include "geometry/geometry.h"

namespace deeproute {
namespace localization {

constexpr double kEdgeLongitudinalMaxDistance = 10;

namespace {

void FindClosestPointAndDistance(const Vector2d& pos,
                                 const deeproute::common::Polyline& polyline,
                                 Vector2d* closest_pt, double* min_distance) {
  double min_dist = std::numeric_limits<double>::max();
  for (const auto& pt : polyline.point()) {
    const double dist = (pos - Vector2d(pt.x(), pt.y())).norm();
    if (dist < min_dist) {
      min_dist = dist;
      *closest_pt = Vector2d(pt.x(), pt.y());
    }
  }
  *min_distance = min_dist;
}

void FindClosestPointAndDistance(const Vector2dVector& ref_line,
                                 const deeproute::common::Polyline& polyline,
                                 Vector2d* closest_pt, double* min_distance) {
  double min_dist = std::numeric_limits<double>::max();
  // Vector2d closest_pt;
  for (const auto& pt : polyline.point()) {
    const double dist = Distance(Vector2d(pt.x(), pt.y()), ref_line);
    if (dist < min_dist) {
      min_dist = dist;
      *closest_pt = Vector2d(pt.x(), pt.y());
    }
  }
  *min_distance = min_dist;
}

Vector2d FindClosestCluster(const Vector2d& pt, Vector2dVector& clusters) {
  double min_dist = std::numeric_limits<double>::max();
  Vector2d closest_cluster;

  for (const Vector2d& cluster : clusters) {
    const double dist = (cluster - pt).norm();

    if (dist < min_dist) {
      min_dist = dist;
      closest_cluster = cluster;
    }

    // MLOG(INFO) << "ego lane projected pt: " << pt.transpose()
    //            << ", closest_cluster: " << cluster.transpose()
    //            << ", dist: " << dist;
  }
  // MLOG(INFO) << "Closest cluster: " << closest_cluster.transpose()
  //  << ", min dist: " << min_dist;
  return closest_cluster;
}

deeproute::perception::Lane FindClosestLane(
    const Vector2d& pt, std::vector<deeproute::perception::Lane>& lanes) {
  double min_dist = std::numeric_limits<double>::max();
  Vector2d closest_cluster;

  deeproute::perception::Lane closest_lane;
  for (const auto& lane : lanes) {
    const double dist = Distance(pt, lane.centerline());

    if (dist < min_dist) {
      min_dist = dist;
      closest_lane = lane;
    }
  }

  return closest_lane;
}

// int FindClosestLaneId(const deeproute::common::Polyline& polyline,
//                       const std::vector<deeproute::perception::Lane>& lanes)
//                       {
//   double min_dist = std::numeric_limits<double>::max();

//   int lane_id = -1;
//   for (size_t i = 0; i < lanes.size(); i++) {
//     const auto lane = lanes[i];
//     const double dist = Distance(polyline, lane.centerline());
//     if (dist < min_dist) {
//       min_dist = dist;
//       lane_id = i;
//     }
//   }
//   return lane_id;
// }

double FindClosestLanAndDistance(
    const deeproute::common::Polyline& polyline,
    const std::vector<deeproute::perception::Lane>& lanes) {
  double min_dist = std::numeric_limits<double>::max();

  for (size_t i = 0; i < lanes.size(); i++) {
    const auto lane = lanes[i];
    const double dist = Distance(polyline, lane.centerline());
    if (dist < min_dist) {
      min_dist = dist;
    }
  }
  return min_dist;
}

int FindClosestLaneId(
    const Vector2d& pt,
    const std::unordered_map<int32_t, Vector2d>& lane_id_to_projected_pts) {
  double min_dist = std::numeric_limits<double>::max();
  int lane_id = -1;

  for (const auto& lane_id_to_projected_pt : lane_id_to_projected_pts) {
    const double dist = (lane_id_to_projected_pt.second - pt).norm();

    if (dist < min_dist) {
      min_dist = dist;
      lane_id = lane_id_to_projected_pt.first;
    }
  }

  return lane_id;
}

bool IsRasMapGeometricallyValid(const perception::RASMap& ras_map) {
  if (ras_map.lanes_size() == 0) {
    MLOG(WARN)
        << "IsRasMapGeometricallyValid, No lanes found in current ras map!";
    return false;
  }

  std::set<int32_t> lane_ids, edge_ids;
  for (const auto& lane : ras_map.lanes()) {
    if (lane_ids.find(lane.id()) == lane_ids.end()) {
      lane_ids.insert(lane.id());
    } else {
      MLOG(WARN) << "IsRasMapGeometricallyValid, duplicated lane id in ras "
                    "map! Lane id: "
                 << lane.id();
      return false;
    }

    // Note, according to perception, centerline points are usually spaced at
    // roughly 50cm interval, a lane with 2 pts is highly likely to be
    // incorrect.
    if (lane.centerline().point_size() <= 2) {
      MLOG(WARN)
          << "IsRasMapGeometricallyValid, Lane id: " << lane.id()
          << " point size invalid, Current ras map might be problemsome.";
      return false;
    }
  }

  for (const auto& edge : ras_map.edge()) {
    if (edge_ids.find(edge.id()) == edge_ids.end()) {
      edge_ids.insert(edge.id());
    } else {
      MLOG(WARN) << "IsRasMapGeometricallyValid, duplicated edge id in ras "
                    "map! Edge id: "
                 << edge.id();
      return false;
    }

    if (edge.polyline().point_size() <= 2) {
      MLOG(WARN)
          << "IsRasMapGeometricallyValid, Polyline id: " << edge.id()
          << " point size invalid, Current ras map might be problemsome.";
      return false;
    }
  }

  // for (const auto& lane : ras_map.lanes()) {
  //   MLOG(INFO) << "lane id: " << lane.id() << ", virtual: " <<
  //   lane.virtual_()
  //              << ", length: " << Length(lane.centerline());
  // }
  // for (const auto& edge : ras_map.edge()) {
  //   MLOG(INFO) << "edge id: " << edge.id()
  //              << ", length: " << Length(edge.polyline());
  // }

  return true;
}

}  // namespace

bool AdjustMatchStatus(const int sd_lane_sum, const int ob_lane_sum,
                       const int ob_lane_idx,
                       const ClosestCurbStatus& curb_match_status,
                       int* adjusted_lane_index) {
  // this should not happend
  if (ob_lane_sum < ob_lane_idx) {
    MLOG(WARN) << "Observed lane sum < observed lane index, adjusting lane "
                  "index failed.";
    return false;
  }

  if (curb_match_status == LEFT) {
    *adjusted_lane_index = std::min(sd_lane_sum, ob_lane_idx);
  } else if (curb_match_status == RIGHT) {
    *adjusted_lane_index =
        std::max(1, sd_lane_sum - (ob_lane_sum - ob_lane_idx));
  } else if (curb_match_status == NO_CURB) {
    *adjusted_lane_index = std::ceil(sd_lane_sum / 2.0);
  }

  return true;
}

bool LaneDirectionDiffersFromOnLaneVehicle(
    const perception::Lane& lane,
    const perception::PerceptionObstacles& obstacles,
    const ::common::Transformation3& ego_car_pose,
    const double heading_diff_threshold) {
  // MLOG(INFO) << "Lane id: " << lane.id()
  //  << ", ego yaw: " << ego_car_pose.GetRollPitchYaw()[2];
  // step 1, find on lane obstacles
  std::vector<perception::PerceptionObstacle> on_lane_vehicles;
  for (const auto& obstacle : obstacles.perception_obstacle()) {
    const double distance =
        Distance(Vector2d(obstacle.position().x(), obstacle.position().y()),
                 lane.centerline());
    const bool is_moving =
        Vector2d(obstacle.velocity().x(), obstacle.velocity().y()).norm() > 0.5;

    if (distance > kTypicalLaneWidth / 2 || !is_moving) {
      continue;
    } else {
      MLOG(INFO) << "On lane moving vehicle found, distance: " << distance
                 << ", heading: " << obstacle.theta()
                 << ", object id: " << obstacle.id();
    }

    on_lane_vehicles.push_back(obstacle);
  }

  if (on_lane_vehicles.empty()) {
    return true;
  }

  for (const auto& on_lane_vehicle : on_lane_vehicles) {
    double heading_diff =
        on_lane_vehicle.theta() - ego_car_pose.GetRollPitchYaw()[2];
    heading_diff = std::abs(std::remainder(heading_diff, 2.0 * M_PI));

    // const auto vehicle_frame_position =
    //     ego_car_pose.Inverse() * Vector3d(on_lane_vehicle.position().x(),
    //                                       on_lane_vehicle.position().y(),
    //                                       on_lane_vehicle.position().z());
    // MLOG(INFO) << "vehicle frame position: "
    //            << vehicle_frame_position.transpose();

    if (heading_diff > heading_diff_threshold) {
      MLOG(INFO) << "heading diff too big: " << heading_diff
                 << " different direction with ego car."
                 << ", on_lane_vehicle.theta():  " << on_lane_vehicle.theta()
                 << ", ego_car_pose.GetRollPitchYaw()[2]: "
                 << ego_car_pose.GetRollPitchYaw()[2];
      return false;
    } else {
      MLOG(INFO) << "heading diff: " << heading_diff
                 << ", obstacle id: " << on_lane_vehicle.id()
                 << ", on_lane_vehicle.theta():  " << on_lane_vehicle.theta()
                 << ", ego_car_pose.GetRollPitchYaw()[2]: "
                 << ego_car_pose.GetRollPitchYaw()[2];
    }
  }

  return true;
}

bool ProcessRasMapByRefLine(
    const ::common::Transformation3& pose,
    const std::shared_ptr<const deeproute::perception::RASMap>& ras_map,
    const ClosestCurbStatus& prev_curb_status, int* lane_index, int* lane_sum,
    int32_t* lane_id, int64_t* ras_map_time, double* dist_to_egolane,
    ClosestCurbStatus* closest_curb_result, double* closest_curb_distance,
    std::vector<int32_t>* valid_left_to_right_lane_id,
    deeproute::perception::RASMap* debug_ras_map) {
  if (!ras_map->has_time_measurement()) {
    MLOG(WARN) << "ras map has no measurement time.";
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_RAS_MAP_INVALID);
    return false;
  }

  *ras_map_time = ras_map->time_measurement();
  debug_ras_map->set_time_measurement(*ras_map_time);

  if (!IsPoseValid(pose)) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_GLOBAL_POSE_INVALID);
    return false;
  }

  if (!IsRasMapGeometricallyValid(*ras_map)) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_RAS_MAP_INVALID);
    return false;
  }

  Vector2d pos_2d(pose.GetTranslation()[0], pose.GetTranslation()[1]);
  const Eigen::Rotation2Dd vehicle_to_world_R =
      Eigen::Rotation2Dd(pose.Inverse().GetRollPitchYaw()[2]);
  const Vector2d vehicle_to_world_t = pose.Inverse().GetTranslation().head(2);

  // step 1. find ego lane
  std::unordered_map<int32_t, deeproute::perception::Lane> id_to_lanes;
  int32_t ego_lane_id = -1e6;
  deeproute::perception::Lane ego_lane;

  constexpr bool kBelieveRawEgoLaneID = true;
  if (kBelieveRawEgoLaneID) {
    std::vector<deeproute::perception::Lane> valid_ego_lanes;
    for (const auto& lane : ras_map->lanes()) {
      id_to_lanes[lane.id()] = lane;

      if (lane.is_ego_lane()) {
        ego_lane_id = lane.id();
        ego_lane = lane;
        valid_ego_lanes.push_back(lane);
      }
    }

    if (valid_ego_lanes.size() > 1) {
      MLOG(WARN) << "Found more than 1 ego lanes: " << valid_ego_lanes.size();

      // if there are multiple valid ego lane, use the 1st
      *lane_id = valid_ego_lanes[0].id();
      ego_lane = valid_ego_lanes[0];
    } else if (valid_ego_lanes.empty()) {
      MLOG(WARN) << "No ego lane given.";
      return false;
    } else {
      *lane_id = ego_lane_id;
    }

  } else {
    MLOG(INFO) << "Not using any provided ego lane id infomation, lane size: "
               << ras_map->lanes_size() << ", pos 2d: " << pos_2d.transpose();
    deeproute::perception::Lane selected_ego_lane;

    double min_dist = std::numeric_limits<double>::max();
    for (const auto& lane : ras_map->lanes()) {
      // MLOG(INFO) << "Lane point size: " << lane.centerline().point_size();
      id_to_lanes[lane.id()] = lane;

      const double distance = Distance(pos_2d, lane.centerline());
      // MLOG(INFO) << "Car to lane Distance: " << distance;
      if (distance < min_dist) {
        min_dist = distance;
        selected_ego_lane = lane;
      }
    }
    *lane_id = selected_ego_lane.id();
    ego_lane = selected_ego_lane;
  }

  ego_lane_id = *lane_id;

  *dist_to_egolane = Distance(pos_2d, ego_lane.centerline());
  // MLOG(INFO) << "distance to ego_lane.centerline(): " << *dist_to_egolane;

  if (*dist_to_egolane > 4) {
    MLOG(WARN) << "Distance to ego lane centerline too far, current ras map "
                  "not reliable, skip.";
    return false;
  }

  const auto ego_lane_start = ego_lane.centerline().point()[0];
  const auto ego_lane_end =
      ego_lane.centerline().point()[ego_lane.centerline().point_size() - 1];
  const double ego_lane_len = Length(ego_lane.centerline());
  // MLOG(INFO) << "ego lane_id: " << *lane_id;

  ////////////////////////////////////////////////////////////////////////////////

  // step 2. find closest/next closest point of ego lane centerline p and p1,
  // construct a line vertical to p_p1, and use the vertical line to find
  // intersection points with valid lanes.
  Vector2d closest_pt;
  int closest_pt_id = -1;
  double min_dist_to_ego_lane_pt = std::numeric_limits<double>::max();
  for (int i = 0; i < ego_lane.centerline().point_size(); i++) {
    const auto pt = ego_lane.centerline().point()[i];
    const double dist = (Vector2d(pt.x(), pt.y()) - pos_2d).norm();
    if (dist < min_dist_to_ego_lane_pt) {
      min_dist_to_ego_lane_pt = dist;
      closest_pt = Vector2d(pt.x(), pt.y());
      closest_pt_id = i;
    }
  }
  Vector2d next_closest_pt;
  if (closest_pt_id < ego_lane.centerline().point_size() - 1) {
    next_closest_pt =
        Vector2d(ego_lane.centerline().point()[closest_pt_id + 1].x(),
                 ego_lane.centerline().point()[closest_pt_id + 1].y());
  } else {
    next_closest_pt =
        Vector2d(ego_lane.centerline().point()[closest_pt_id - 1].x(),
                 ego_lane.centerline().point()[closest_pt_id - 1].y());
  }
  // 2.1 create a line centerted on p and vertical to p_p1 with specified
  // length.
  const Vector2dVector vertical_ref_line =
      CreateVerticalLine(closest_pt, next_closest_pt, 50);
  deeproute::perception::Lane vertical_ref_lane;
  common::Polyline vertical_ref_polyline;
  for (const auto ref_pt : vertical_ref_line) {
    common::Point3D pt;
    pt.set_x(ref_pt[0]);
    pt.set_y(ref_pt[1]);
    *vertical_ref_polyline.add_point() = pt;
  }
  *vertical_ref_lane.mutable_centerline() = vertical_ref_polyline;
  vertical_ref_lane.set_id(-1);
  *debug_ras_map->add_lanes() = vertical_ref_lane;
  ////////////////////////////////////////////////////////////////////////////

  // step 3. find left and right edges
  std::vector<deeproute::perception::RoadEdge> left_edges, right_edges;
  for (const auto& edge : ras_map->edge()) {
    if (IsLineToTheRight(edge.polyline(), pose)) {
      right_edges.push_back(edge);
    } else {
      left_edges.push_back(edge);
    }
  }

  // step 4. find closest left and right edge and their ids using point to
  // segment distance. NOTE, it is recommended to use point to segment distance
  // rather than point to line distance.
  deeproute::perception::RoadEdge closest_left_edge, closest_right_edge;
  double min_dist_left_edge = std::numeric_limits<double>::max();
  double min_dist_right_edge = std::numeric_limits<double>::max();
  for (const auto& left_edge : left_edges) {
    double distance;
    Vector2d closest_pt;
    FindClosestPointAndDistance(pos_2d, left_edge.polyline(), &closest_pt,
                                &distance);
    const Vector2d vehicle_frame_closest_pt =
        vehicle_to_world_R * closest_pt + vehicle_to_world_t;
    if (std::abs(vehicle_frame_closest_pt[0]) > kEdgeLongitudinalMaxDistance) {
      continue;
    }

    if (distance < min_dist_left_edge) {
      min_dist_left_edge = distance;
      closest_left_edge = left_edge;
    }
  }
  for (const auto& right_edge : right_edges) {
    double distance;
    Vector2d closest_pt;
    FindClosestPointAndDistance(pos_2d, right_edge.polyline(), &closest_pt,
                                &distance);
    const Vector2d vehicle_frame_closest_pt =
        vehicle_to_world_R * closest_pt + vehicle_to_world_t;
    if (std::abs(vehicle_frame_closest_pt[0]) > kEdgeLongitudinalMaxDistance) {
      continue;
    }

    if (distance < min_dist_right_edge) {
      min_dist_right_edge = distance;
      closest_right_edge = right_edge;
    }
  }
  if (min_dist_right_edge != std::numeric_limits<double>::max()) {
    *debug_ras_map->add_edge() = closest_right_edge;
  }
  if (min_dist_left_edge != std::numeric_limits<double>::max()) {
    *debug_ras_map->add_edge() = closest_left_edge;
  }
  // *debug_ras_map->add_edge() = closest_right_edge;
  // *debug_ras_map->add_edge() = closest_left_edge;

  // step 5. find suitable lanes
  const Vector2d ego_lane_vector =
      Vector2d(ego_lane_start.x(), ego_lane_start.y()) -
      Vector2d(ego_lane_end.x(), ego_lane_end.y());
  Vector2dVector valid_intersection_with_ref_line_pts,
      projected_to_ref_line_pts;
  std::unordered_map<int32_t, Vector2d> lane_id_to_projected_pt;
  std::vector<perception::Lane> left_lanes, right_lanes, all_valid_lanes;
  for (const auto& lane : ras_map->lanes()) {
    // step 5.1, remove all virtual lanes
    if (lane.virtual_()) {
      MLOG(INFO) << "Lane id: " << lane.id() << " virtual, neglect.";
      continue;
    }

    // step 5.2, find closest point from centerline to vertical ref line(the one
    // perpendular to ego lane centerline)
    Vector2d closest_pt;
    double min_dist = -1;
    FindClosestPointAndDistance(vertical_ref_line, lane.centerline(),
                                &closest_pt, &min_dist);

    // step 5.3, is lane left or right to ego car.
    const bool is_lane_right = IsLineToTheRight(lane.centerline(), pose);

    // step 5.4, remove lanes that are not intersected with or close enougth to
    // ref line
    if (min_dist > 10) {
      // MLOG(INFO) << lane.id()
      //            << " min dist from vertical ref line to lane center line "
      //            << min_dist << " > 10, continue.";
      continue;
    }

    // step 5.5, remove lanes that are significantly shorter than ego lane.
    const double kLaneLenThreshold = 0.4 * ego_lane_len;
    const double lane_len = Length(lane.centerline());
    if (lane_len < kLaneLenThreshold) {
      // MLOG(INFO) << lane.id() << " length shorter than ego lane " << lane_len
      //            << " < " << kLaneLenThreshold;
      continue;
    }

    *debug_ras_map->add_lanes() = lane;

    // step 5.5, keep lanes that fall into left and right edge distance.
    const double vehicle_dist_to_centerline =
        Distance(pos_2d, lane.centerline());
    if (is_lane_right && vehicle_dist_to_centerline < min_dist_right_edge) {
      right_lanes.push_back(lane);
    } else if (!is_lane_right &&
               vehicle_dist_to_centerline < min_dist_left_edge) {
      left_lanes.push_back(lane);
    } else {
      // MLOG(WARN) << "lane " << lane.id()
      //            << " is left/right to ego car but its distance: "
      //            << vehicle_dist_to_centerline
      //            << " exceeds corresponding edge. Neglect.";
      continue;
    }

    // step 5.6, remove reversed lanes.
    const Vector2d lane_start(lane.centerline().point()[0].x(),
                              lane.centerline().point()[0].y());
    const int pt_num = lane.centerline().point_size();
    const Vector2d lane_end(lane.centerline().point()[pt_num - 1].x(),
                            lane.centerline().point()[pt_num - 1].y());
    const Vector2d lane_vec = lane_start - lane_end;
    if (lane_vec.dot(ego_lane_vector) < 0) {
      MLOG(INFO) << "lane id " << lane.id()
                 << " reverse with ego lane. Neglect.";
      continue;
    }

    // step 5.7, lane and on lane vehicle should have the same direction.
    // NOTE: as on 2023/02/21, this part has been too dangerous to use, neglect
    // for now.
    // -----------------------------------------------------------------------
    // if (!LaneDirectionDiffersFromOnLaneVehicle(lane, *perception, pose)) {
    //   MLOG(INFO) << "Lane with id " << lane.id()
    //              << " has vehicle of heading that is different from ego "
    //                 "car. Neglect.";
    //   continue;
    // }
    // -----------------------------------------------------------------------

    // step 5.8, if convex hull size not empty, check if each lane intersects
    // with physical ground convex hull
    // if (!convex_hull.outer().empty()) {
    //   if (!Intersects(lane.centerline(), convex_hull)) {
    //     MLOG(INFO) << "Lane with id " << lane.id()
    //                << " does not intersects with physical ground convex hull.
    //                "
    //                   "Neglect.";
    //     continue;
    //   }
    // }

    // step 5.9, project point to ref line
    const Vector2d projected_pt =
        ProjectPointToLine(closest_pt, vertical_ref_line);

    projected_to_ref_line_pts.push_back(projected_pt);
    lane_id_to_projected_pt[lane.id()] = projected_pt;
    all_valid_lanes.push_back(lane);
  }

  // MLOG(INFO) << "Projected to ref line points num: "
  //            << projected_to_ref_line_pts.size();
  if (projected_to_ref_line_pts.empty()) {
    MLOG(INFO) << ras_map->ShortDebugString();
    MLOG(INFO) << "no points are projected to ref line, neglect current "
                  "frame.";
    return false;
  }

  // step 6. cluster points with some radius
  const bool kEnableCluster = false;
  Vector2dVector cluster_centers;
  if (kEnableCluster) {
    cluster_centers = ClusterPoints(projected_to_ref_line_pts, 1);
    MLOG(INFO) << "Before cluster size: " << projected_to_ref_line_pts.size()
               << ", after cluster" << cluster_centers.size();
  } else {
    cluster_centers = projected_to_ref_line_pts;
  }
  for (const auto& cluster_center : cluster_centers) {
    deeproute::perception::MapElement center_pt;
    center_pt.mutable_position()->set_x(cluster_center.x());
    center_pt.mutable_position()->set_y(cluster_center.y());
    *debug_ras_map->add_map_element() = center_pt;
  }

  // try to find projected point of ego line
  if (lane_id_to_projected_pt.find(*lane_id) == lane_id_to_projected_pt.end()) {
    MLOG(WARN) << "Cannot find ego lane id in projected point dict, "
                  "return.";
    return false;
  }
  // find cluster closest to ego lane
  const Vector2d closest_cluster_to_ego_lane =
      FindClosestCluster(lane_id_to_projected_pt[*lane_id], cluster_centers);

  // finally, at this step, update lane index and lane sum.
  *lane_sum = 0;
  *lane_index = 0;
  Vector2d closest_cluster_to_ego_lane_vehicle_frame =
      vehicle_to_world_R * closest_cluster_to_ego_lane + vehicle_to_world_t;
  std::string distances = "";

  std::vector<std::pair<int32_t, double>> lane_id_to_y;
  for (const auto cluster_center : cluster_centers) {
    const Vector2d vehicle_frame_cluster_center =
        vehicle_to_world_R * Vector2d(cluster_center[0], cluster_center[1]) +
        vehicle_to_world_t;

    *lane_sum += 1;

    if (vehicle_frame_cluster_center[1] -
            closest_cluster_to_ego_lane_vehicle_frame[1] >
        -1e-6) {
      *lane_index += 1;
    } else {
      // MLOG(INFO) << "Candidate cluster Y - Ego lane cluster Y: "
      //            << vehicle_frame_cluster_center[1] -
      //                   closest_cluster_to_ego_lane_vehicle_frame[1]
      //            << ", Candidate: " <<
      //            vehicle_frame_cluster_center.transpose()
      //            << ", Ego lane Cluster: "
      //            << closest_cluster_to_ego_lane_vehicle_frame.transpose();
    }

    // this should be 1 to 1 mapping if clustering is disabled.
    const auto& closest_lane = FindClosestLane(cluster_center, all_valid_lanes);
    lane_id_to_y.push_back(
        std::make_pair(closest_lane.id(), vehicle_frame_cluster_center[1]));
    distances += std::to_string(vehicle_frame_cluster_center[1]) + " ";
  }

  std::sort(
      lane_id_to_y.begin(), lane_id_to_y.end(),
      [](std::pair<int32_t, double> const& f,
         std::pair<int32_t, double> const& s) { return f.second > s.second; });
  for (const auto& temp_lane_id_to_y : lane_id_to_y) {
    (*valid_left_to_right_lane_id).push_back(temp_lane_id_to_y.first);
  }

  // this should not happen, but if it happens, since we have successfully found
  // ego lane, lane index should at least be 1.
  if (*lane_index == 0) {
    MLOG(WARN) << "No lanes found left to vehicle with Y > "
               << closest_cluster_to_ego_lane_vehicle_frame[1]
               << ", this could be wrong lane width scale. We need to add at "
                  "least ego lane to id.";
    *lane_index = 1;
  }

  // set closest curb status
  *closest_curb_distance = std::numeric_limits<double>::max();
  if (min_dist_left_edge == std::numeric_limits<double>::max() &&
      min_dist_right_edge == std::numeric_limits<double>::max()) {
    *closest_curb_result = NO_CURB;
  } else if (min_dist_left_edge < min_dist_right_edge &&
             min_dist_left_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = LEFT;

    if (!left_lanes.empty()) {
      *closest_curb_distance =
          FindClosestLanAndDistance(closest_left_edge.polyline(), left_lanes);
    }
  } else if (min_dist_right_edge < min_dist_left_edge &&
             min_dist_right_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = RIGHT;

    if (!right_lanes.empty()) {
      *closest_curb_distance =
          FindClosestLanAndDistance(closest_right_edge.polyline(), right_lanes);
    }
  } else {
    MLOG(WARN) << "Closest curb result logic failure, should not happen. Set "
                  "to LEFT by "
                  "default.";
    *closest_curb_result = NO_CURB;
  }

  // try to smooth fluctuations: if distance between left and right curb is
  // neglectable, set closest curb to right curb.
  if (std::abs(min_dist_left_edge - min_dist_right_edge) < 1 &&
      prev_curb_status != ClosestCurbStatus::NO_CURB) {
    *closest_curb_result = prev_curb_status;
  }

  // MLOG(INFO) << "Time: " << *ras_map_time << " Lane index: " << *lane_index
  //            << ", lane sum: " << *lane_sum
  //            << ", left edge size: " << left_edges.size()
  //            << ", right edge size: " << right_edges.size()
  //            << ", dist to left edge: " << min_dist_left_edge
  //            << ", dist to right edge: " << min_dist_right_edge
  //            << ", y positions of lanes: " << distances
  //            << ", before cluster size: " << projected_to_ref_line_pts.size()
  //            << ", after cluster size: " << cluster_centers.size()
  //            << ", min_dist_left_edge : " << min_dist_left_edge
  //            << ", min_dist_right_edge: " << min_dist_right_edge
  //            << ", left curb dist - right curb dit: "
  //            << std::abs(min_dist_left_edge - min_dist_right_edge)
  //            << ", closest curb: " << *closest_curb_result;

  return true;
}

bool ProcessRasMap(const ::common::Transformation3& pose,
                   const deeproute::perception::RASMap& ras_map,
                   const bool use_raw_topology, int* lane_index, int* lane_sum,
                   int32_t* lane_id, int64_t* ras_map_time,
                   double* dist_to_egolane,
                   ClosestCurbStatus* closest_curb_result,
                   deeproute::perception::RASMap* debug_ras_map) {
  *ras_map_time = ras_map.time_measurement();

  debug_ras_map->set_time_measurement(*ras_map_time);

  if (ras_map.lanes_size() == 0) {
    MLOG(WARN) << "No lanes or edges found in current ras map!";
    return false;
  }

  // 2D vehicle pose
  Vector2d pos_2d(pose.GetTranslation()[0], pose.GetTranslation()[1]);
  const Eigen::Rotation2Dd vehicle_to_world_R =
      Eigen::Rotation2Dd(pose.Inverse().GetRollPitchYaw()[2]);
  const Vector2d vehicle_to_world_t = pose.Inverse().GetTranslation().head(2);

  // step 1. find ego lane
  std::unordered_map<int32_t, deeproute::perception::Lane> id_to_lanes;
  int32_t ego_lane_id = -1e6;
  deeproute::perception::Lane ego_lane;

  std::vector<deeproute::perception::Lane> valid_ego_lanes;
  for (const auto& lane : ras_map.lanes()) {
    id_to_lanes[lane.id()] = lane;

    if (lane.is_ego_lane() &&
        (lane.has_left_neighbor_id() || lane.has_right_neighbor_id())) {
      ego_lane_id = lane.id();
      ego_lane = lane;
      valid_ego_lanes.push_back(lane);
    }
  }

  if (valid_ego_lanes.size() > 1) {
    MLOG(WARN)
        << "Found more than 1 ego lanes that contain topologic information: "
        << valid_ego_lanes.size();

    // if there are multiple valid ego lane, use the 1st
    *lane_id = valid_ego_lanes[0].id();
    ego_lane = valid_ego_lanes[0];
  } else if (valid_ego_lanes.empty()) {
    deeproute::perception::Lane selected_ego_lane;
    double min_dist = std::numeric_limits<double>::max();
    for (const auto& lane : ras_map.lanes()) {
      const double distance = Distance(pos_2d, lane.centerline());
      if (distance < min_dist) {
        min_dist = distance;
        selected_ego_lane = lane;
      }
    }
    *lane_id = selected_ego_lane.id();
    ego_lane = selected_ego_lane;
  } else {
    *lane_id = ego_lane_id;
  }
  *dist_to_egolane = Distance(pos_2d, ego_lane.centerline());

  if (*dist_to_egolane > 5) {
    MLOG(WARN) << "Distance to ego lane too far, current ras map not reliable, "
                  "skipped.";
    return false;
  }

  const auto ego_lane_start = ego_lane.centerline().point()[0];
  const auto ego_lane_end =
      ego_lane.centerline().point()[ego_lane.centerline().point_size() - 1];

  MLOG(INFO) << "ego lane_id: " << *lane_id;
  for (const auto& lane : ras_map.lanes()) {
    MLOG(INFO) << "virtual: " << lane.virtual_();
  }

  // step 2. find left and right edges
  std::vector<deeproute::perception::RoadEdge> left_edges, right_edges;
  for (const auto& edge : ras_map.edge()) {
    if (IsLineToTheRight(edge.polyline(), pose)) {
      right_edges.push_back(edge);
    } else {
      left_edges.push_back(edge);
    }
  }
  MLOG(INFO) << "step 2";

  // step 3. find closest left and right edge and their ids using point to
  // segment distance. NOTE, it is recommended to use point to segment distance
  // rather than point to line distance.

  deeproute::perception::RoadEdge closest_left_edge, closest_right_edge;
  double min_dist_left_edge = std::numeric_limits<double>::max();
  double min_dist_right_edge = std::numeric_limits<double>::max();
  for (const auto& left_edge : left_edges) {
    // const double distance = Distance(pos_2d, left_edge.polyline());
    double distance;
    Vector2d closest_pt;
    FindClosestPointAndDistance(pos_2d, left_edge.polyline(), &closest_pt,
                                &distance);
    const Vector2d vehicle_frame_closest_pt =
        vehicle_to_world_R * closest_pt + vehicle_to_world_t;
    if (std::abs(vehicle_frame_closest_pt[0]) > kEdgeLongitudinalMaxDistance) {
      continue;
    }

    if (distance < min_dist_left_edge) {
      min_dist_left_edge = distance;
      closest_left_edge = left_edge;
    }
  }
  for (const auto& right_edge : right_edges) {
    // const double distance = Distance(pos_2d, right_edge.polyline());
    double distance;
    Vector2d closest_pt;
    FindClosestPointAndDistance(pos_2d, right_edge.polyline(), &closest_pt,
                                &distance);
    const Vector2d vehicle_frame_closest_pt =
        vehicle_to_world_R * closest_pt + vehicle_to_world_t;
    if (std::abs(vehicle_frame_closest_pt[0]) > kEdgeLongitudinalMaxDistance) {
      continue;
    }

    if (distance < min_dist_right_edge) {
      min_dist_right_edge = distance;
      closest_right_edge = right_edge;
    }
  }
  MLOG(INFO) << "step 3";

  // step 4, if use raw topology, clear all virtual lanes, set edge to virtual
  // lane, and append virtual edges as lanes to existing ras map.
  if (use_raw_topology) {
    MLOG(INFO) << "use raw topology";
    // including ego lane
    *lane_index = 1;
    *lane_sum = 1;

    perception::RASMap temp_ras_map;
    temp_ras_map.set_time_measurement(*ras_map_time);

    deeproute::perception::Lane left_temp_lane = ego_lane;
    std::vector<perception::Lane> left_lanes, right_lanes;
    while (left_temp_lane.has_left_neighbor_id() &&
           !left_temp_lane.is_left_neighbor_reverse()) {
      left_temp_lane = id_to_lanes[left_temp_lane.left_neighbor_id()];

      if (!left_temp_lane.virtual_()) {
        *lane_index += 1;
        *lane_sum += 1;
        left_lanes.push_back(left_temp_lane);
        *temp_ras_map.add_lanes() = left_temp_lane;
      }
    }
    // count right non reversed lanes
    deeproute::perception::Lane right_temp_lane = ego_lane;
    while (right_temp_lane.has_right_neighbor_id() &&
           !right_temp_lane.is_right_neighbor_reverse()) {
      right_temp_lane = id_to_lanes[right_temp_lane.right_neighbor_id()];

      if (!right_temp_lane.virtual_()) {
        *lane_sum += 1;
        right_lanes.push_back(right_temp_lane);
        *temp_ras_map.add_lanes() = right_temp_lane;
      }
    }

    *temp_ras_map.add_lanes() = ego_lane;

    // add left edge as virutal lane
    perception::Lane left_edge_as_lane;
    if (min_dist_left_edge != std::numeric_limits<double>::max()) {
      left_edge_as_lane.set_id(closest_left_edge.id());
      left_edge_as_lane.set_virtual_(true);
      *left_edge_as_lane.mutable_centerline() = closest_left_edge.polyline();
      left_edge_as_lane.set_is_right_neighbor_reverse(false);

      if (!left_lanes.empty()) {
        left_edge_as_lane.set_right_neighbor_id(left_lanes.back().id());
      } else {
        left_edge_as_lane.set_right_neighbor_id(ego_lane.id());
      }
      *temp_ras_map.add_lanes() = left_edge_as_lane;

      for (auto& lane : *temp_ras_map.mutable_lanes()) {
        if (lane.id() == left_edge_as_lane.right_neighbor_id()) {
          lane.set_left_neighbor_id(left_edge_as_lane.id());
        }
      }
    }

    // add right edge as virtual lane
    perception::Lane right_edge_as_lane;
    if (min_dist_right_edge != std::numeric_limits<double>::max()) {
      right_edge_as_lane.set_id(closest_right_edge.id());
      right_edge_as_lane.set_virtual_(true);
      *right_edge_as_lane.mutable_centerline() = closest_right_edge.polyline();
      right_edge_as_lane.set_is_left_neighbor_reverse(false);

      if (!right_lanes.empty()) {
        right_edge_as_lane.set_left_neighbor_id(right_lanes.back().id());
      } else {
        right_edge_as_lane.set_left_neighbor_id(ego_lane.id());
      }
      *temp_ras_map.add_lanes() = right_edge_as_lane;

      for (auto& lane : *temp_ras_map.mutable_lanes()) {
        if (lane.id() == right_edge_as_lane.left_neighbor_id()) {
          lane.set_right_neighbor_id(right_edge_as_lane.id());
        }
      }
    }

    *debug_ras_map = temp_ras_map;

    return true;
  }
  MLOG(INFO) << "step 4";

  // step 5. find closest/next closest point of ego lane centerline p and p1,
  // construct a line vertical to p_p1, and use the vertical line to find
  // intersection points with valid lanes.
  Vector2d closest_pt;
  int closest_pt_id = -1;
  double min_dist_to_ego_lane_pt = std::numeric_limits<double>::max();
  for (int i = 0; i < ego_lane.centerline().point_size(); i++) {
    const auto pt = ego_lane.centerline().point()[i];
    const double dist = (Vector2d(pt.x(), pt.y()) - pos_2d).norm();
    if (dist < min_dist_to_ego_lane_pt) {
      min_dist_to_ego_lane_pt = dist;
      closest_pt = Vector2d(pt.x(), pt.y());
      closest_pt_id = i;
    }
  }
  Vector2d next_closest_pt;
  if (closest_pt_id < ego_lane.centerline().point_size() - 1) {
    next_closest_pt =
        Vector2d(ego_lane.centerline().point()[closest_pt_id + 1].x(),
                 ego_lane.centerline().point()[closest_pt_id + 1].y());
  } else {
    next_closest_pt =
        Vector2d(ego_lane.centerline().point()[closest_pt_id - 1].x(),
                 ego_lane.centerline().point()[closest_pt_id - 1].y());
  }
  // 5.1 create a line centerted on p and vertical to p_p1 with specified
  // length.
  const Vector2dVector vertical_ref_line =
      CreateVerticalLine(closest_pt, next_closest_pt, 40);
  MLOG(INFO) << "step 5";

  // step 6. find suitable lanes
  const Vector2d ego_lane_vector =
      Vector2d(ego_lane_start.x(), ego_lane_start.y()) -
      Vector2d(ego_lane_end.x(), ego_lane_end.y());
  Vector2dVector projected_to_ref_line_pts;
  //   std::vector<Lane> left_lanes, right_lanes;
  std::unordered_map<int32_t, Vector2d> lane_id_to_projected_pt;
  for (const auto& lane : ras_map.lanes()) {
    // step 6.1, remove all virtual lanes
    if (lane.virtual_()) {
      MLOG(INFO) << "Lane id: " << lane.id() << " virtual, neglect.";
      continue;
    }

    // step 6.2, find closest point from centerline to vertical ref line(the one
    // perpendular to ego lane centerline)
    Vector2d closest_pt;
    double min_dist = -1;
    FindClosestPointAndDistance(vertical_ref_line, lane.centerline(),
                                &closest_pt, &min_dist);

    // step 6.3, is lane left or right to ego car.
    const bool is_lane_right = IsLineToTheRight(lane.centerline(), pose);

    // step 6.4, remove lanes that are not intersected with or close to ref line
    if (min_dist > 10) {
      continue;
    }

    // *debug_ras_map->add_lanes() = lane;

    // step 6.5, remove reversed lanes.
    const Vector2d lane_start(lane.centerline().point()[0].x(),
                              lane.centerline().point()[0].y());
    const int pt_num = lane.centerline().point_size();
    const Vector2d lane_end(lane.centerline().point()[pt_num - 1].x(),
                            lane.centerline().point()[pt_num - 1].y());
    const Vector2d lane_vec = lane_start - lane_end;
    if (lane_vec.dot(ego_lane_vector) < 0) {
      continue;
    }

    // step 6.6, keep lanes that fall into left and right edge distance.
    const double vehicle_dist_to_centerline =
        Distance(pos_2d, lane.centerline());
    if (is_lane_right && vehicle_dist_to_centerline < min_dist_right_edge) {
      //   valid_intersection_with_ref_line_pts.push_back(closest_pt);
      //   right_lanes.push_back(lane);
    } else if (!is_lane_right &&
               vehicle_dist_to_centerline < min_dist_left_edge) {
      //   valid_intersection_with_ref_line_pts.push_back(closest_pt);
      //   left_lanes.push_back(lane);
    } else {
      MLOG(WARN) << "This lane is left/right to ego car but its distance "
                    "exceeds corresponding edge. This should not happen.";
      continue;
    }

    // step 6.7, project point to ref line
    const Vector2d projected_pt =
        ProjectPointToLine(closest_pt, vertical_ref_line);
    projected_to_ref_line_pts.push_back(projected_pt);
    lane_id_to_projected_pt[lane.id()] = projected_pt;
  }

  MLOG(INFO) << "Projected to ref line points num: "
             << projected_to_ref_line_pts.size();
  if (projected_to_ref_line_pts.empty()) {
    MLOG(INFO) << ras_map.ShortDebugString();
    MLOG(WARN) << "Failed to project pts to ref line.";
    return false;
  }

  // step 7. cluster points with some radius
  Vector2dVector cluster_centers = ClusterPoints(projected_to_ref_line_pts, 2);
  for (const auto& cluster_center : cluster_centers) {
    deeproute::perception::MapElement center_pt;
    center_pt.mutable_position()->set_x(cluster_center.x());
    center_pt.mutable_position()->set_y(cluster_center.y());
    *debug_ras_map->add_map_element() = center_pt;
  }

  MLOG(INFO) << "step 7, clustered: " << cluster_centers.size();

  // step 8, select one single lane that are closest to each cluster center
  // using each lanes's projected point

  // 8.1 find closest cluster to ego lane, avoid finding failure in the next
  // step
  if (lane_id_to_projected_pt.find(*lane_id) == lane_id_to_projected_pt.end()) {
    MLOG(WARN) << "Cannot find ego lane id in projected point dict, "
                  "return.";
    return false;
  }
  const Vector2d closest_cluster_to_ego_lane =
      FindClosestCluster(lane_id_to_projected_pt[*lane_id], cluster_centers);

  std::unordered_map<int32_t, Vector2d> lane_id_to_cluster_centers;
  for (const Vector2d& cluster : cluster_centers) {
    // current cluster has been assigned to ego lane
    if (cluster == closest_cluster_to_ego_lane) {
      lane_id_to_cluster_centers[*lane_id] =
          vehicle_to_world_R * cluster + vehicle_to_world_t;
      continue;
    }

    // for other clusters
    const int lane_id = FindClosestLaneId(cluster, lane_id_to_projected_pt);
    lane_id_to_cluster_centers[lane_id] =
        vehicle_to_world_R * cluster + vehicle_to_world_t;
  }
  MLOG(INFO) << "step 8";

  std::vector<std::pair<int32_t, Vector2d>>
      lane_id_to_cluster_center_vehicle_frame(
          lane_id_to_cluster_centers.begin(), lane_id_to_cluster_centers.end());
  std::sort(lane_id_to_cluster_center_vehicle_frame.begin(),
            lane_id_to_cluster_center_vehicle_frame.end(),
            [](std::pair<int32_t, Vector2d> const& f,
               std::pair<int32_t, Vector2d> const& s) {
              return f.second[1] > s.second[1];
            });

  MLOG(INFO) << "step 8, sorted size: "
             << lane_id_to_cluster_center_vehicle_frame.size();

  // step 9, reformulate all lanes + edges as virtual lane.
  for (int32_t i = 0;
       i < static_cast<int32_t>(lane_id_to_cluster_center_vehicle_frame.size());
       i++) {
    const int32_t current_lane_id =
        lane_id_to_cluster_center_vehicle_frame[i].first;
    auto current_lane = id_to_lanes[current_lane_id];

    // add left edge as virtual lane
    if (i == 0) {
      const int32_t right_lane_id =
          lane_id_to_cluster_center_vehicle_frame[i + 1].first;
      current_lane.set_right_neighbor_id(right_lane_id);
      // auto& right_lane = id_to_lanes[right_lane_id];
      // right_lane.set_left_neighbor_id(current_lane_id);

      perception::Lane left_edge_as_lane;
      if (min_dist_left_edge != std::numeric_limits<double>::max()) {
        left_edge_as_lane.set_id(closest_left_edge.id());
        left_edge_as_lane.set_virtual_(true);
        *left_edge_as_lane.mutable_centerline() = closest_left_edge.polyline();
        left_edge_as_lane.set_right_neighbor_id(current_lane_id);
        current_lane.set_left_neighbor_id(left_edge_as_lane.id());

        *debug_ras_map->add_lanes() = left_edge_as_lane;
      }
    } else if (i + 1 == static_cast<int>(lane_id_to_cluster_center_vehicle_frame.size())) {
      perception::Lane right_edge_as_lane;
      if (min_dist_right_edge != std::numeric_limits<double>::max()) {
        right_edge_as_lane.set_id(closest_right_edge.id());
        right_edge_as_lane.set_virtual_(true);
        *right_edge_as_lane.mutable_centerline() =
            closest_right_edge.polyline();
        right_edge_as_lane.set_left_neighbor_id(current_lane_id);
        current_lane.set_right_neighbor_id(right_edge_as_lane.id());

        *debug_ras_map->add_lanes() = right_edge_as_lane;
      }
    } else {
      const int32_t left_lane_id =
          lane_id_to_cluster_center_vehicle_frame[i - 1].first;
      current_lane.set_left_neighbor_id(left_lane_id);
      const int32_t right_lane_id =
          lane_id_to_cluster_center_vehicle_frame[i + 1].first;
      current_lane.set_right_neighbor_id(right_lane_id);
    }

    if (current_lane.id() == *lane_id) {
      current_lane.set_is_ego_lane(true);
    } else {
      MLOG(INFO) << "Setting ego lane failed, this should not happen.";
    }
    *debug_ras_map->add_lanes() = current_lane;
  }

  MLOG(INFO) << "step 9";

  // finally
  *lane_sum = 0;
  *lane_index = 0;

  Vector2d closest_cluster_to_ego_lane_vehicle_frame =
      vehicle_to_world_R * closest_cluster_to_ego_lane + vehicle_to_world_t;
  for (const auto& cluster_center : cluster_centers) {
    const Vector2d vehicle_frame_cluster_center =
        vehicle_to_world_R * Vector2d(cluster_center[0], cluster_center[1]) +
        vehicle_to_world_t;

    *lane_sum += 1;
    if (vehicle_frame_cluster_center[1] >=
        closest_cluster_to_ego_lane_vehicle_frame[1]) {
      *lane_index += 1;
    }
  }

  if (*lane_index == 0) {
    MLOG(WARN) << "No lanes found left to vehicle with Y > "
               << closest_cluster_to_ego_lane_vehicle_frame[1]
               << ", this could be wrong lane scale. We need to add at "
                  "least ego lane to it.";
    *lane_index = 1;
  }

  // set closest curb status
  if (min_dist_left_edge == std::numeric_limits<double>::max() &&
      min_dist_right_edge == std::numeric_limits<double>::max()) {
    *closest_curb_result = NO_CURB;
  } else if (min_dist_left_edge < min_dist_right_edge &&
             min_dist_left_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = LEFT;
  } else if (min_dist_right_edge < min_dist_left_edge &&
             min_dist_right_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = RIGHT;
  } else {
    MLOG(WARN) << "Closest curb result logic failure, should not happen. Set "
                  "to LEFT by "
                  "default.";
    *closest_curb_result = LEFT;
  }

  MLOG(INFO) << "Time: " << *ras_map_time << " Lane index: " << *lane_index
             << ", lane sum: " << *lane_sum
             << ", dist to right edge: " << min_dist_right_edge
             << ", dist to left edge: " << min_dist_left_edge
             << ", Closest curb status: " << *closest_curb_result;
  return true;
}

bool ProcessRasMapByTopology(
    const ::common::Transformation3& pose,
    const std::shared_ptr<const deeproute::perception::RASMap>& ras_map,
    int* lane_index, int* lane_sum, int32_t* lane_id, int64_t* ras_map_time,
    double* dist_to_egolane, ClosestCurbStatus* closest_curb_result,
    double* closest_curb_distance,
    deeproute::perception::RASMap* debug_ras_map) {
  *ras_map_time = ras_map->time_measurement();
  ::common::Timer timer;
  if (!IsPoseValid(pose)) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_GLOBAL_POSE_INVALID);
    return false;
  }

  if (!IsRasMapGeometricallyValid(*ras_map)) {
    MLOG(INFO) << "Rasmap not geometrically valid, skip.";
    // 截止2024年2月7日, @Haoxiang Li 认为VIS上上报太多此类报警,
    // @Zicheng Zhao 和 @Hengjia Li 无法单独diable这个信息展示,
    // 所以我决定注释掉这个报警 common::ReportEvent(dr::common::LOCK_ON_ROAD,
    //                     dr::common::LOCK_ON_ROAD_INPUT_RAS_MAP_INVALID);
    return false;
  }

  if (ras_map->lanes_size() == 0) {
    MLOG(INFO) << "No lanes found in ras map!";
    return false;
  }

  Vector2d pos_2d(pose.GetTranslation()[0], pose.GetTranslation()[1]);

  std::unordered_map<int32_t, deeproute::perception::Lane> id_to_lanes;
  deeproute::perception::Lane ego_lane;

  // check ego lane
  std::vector<deeproute::perception::Lane> valid_ego_lanes;
  for (const auto& lane : ras_map->lanes()) {
    id_to_lanes[lane.id()] = lane;

    if (lane.is_ego_lane()) {
      *lane_id = lane.id();
      ego_lane = lane;
      valid_ego_lanes.push_back(lane);
    }
  }
  if (valid_ego_lanes.size() != 1) {
    MLOG(INFO) << "No or more than 1 ego lane found in ras map!";
    return false;
  }

  *dist_to_egolane = Distance(pos_2d, ego_lane.centerline());
  // if (*dist_to_egolane > 4) {
  //   MLOG(WARN) << "Distance to ego lane centerline too far, current ras map "
  //                 "not reliable, skip.";
  //   return false;
  // }

  *debug_ras_map->add_lanes() = ego_lane;

  // find left edges and right edges
  std::vector<deeproute::perception::RoadEdge> left_edges, right_edges;
  for (const auto& edge : ras_map->edge()) {
    if (IsLineToTheRight(edge.polyline(), pose)) {
      right_edges.push_back(edge);
    } else {
      left_edges.push_back(edge);
    }
  }

  // find left and right lanes
  std::vector<perception::Lane> left_lanes, right_lanes;
  for (const auto& lane : ras_map->lanes()) {
    if (IsLineToTheRight(lane.centerline(), pose)) {
      left_lanes.push_back(lane);
    } else {
      right_lanes.push_back(lane);
    }
  }

  // find closest left edge and closest right edge
  deeproute::perception::RoadEdge closest_left_edge, closest_right_edge;
  double min_dist_left_edge = std::numeric_limits<double>::max();
  double min_dist_right_edge = std::numeric_limits<double>::max();
  for (const auto& left_edge : left_edges) {
    const double distance = Distance(pos_2d, left_edge.polyline());
    if (distance < min_dist_left_edge) {
      min_dist_left_edge = distance;
      closest_left_edge = left_edge;
    }
  }
  for (const auto& right_edge : right_edges) {
    const double distance = Distance(pos_2d, right_edge.polyline());
    if (distance < min_dist_right_edge) {
      min_dist_right_edge = distance;
      closest_right_edge = right_edge;
    }
  }

  //////////////////////////////////////////////////////////////////////////////////////
  *debug_ras_map->add_edge() = closest_left_edge;
  *debug_ras_map->add_edge() = closest_right_edge;

  // cound left lanes and right lanes, including itself
  // becase we at least have a ego lane, so the index is at least 1 and lane sum
  // is at least 1
  *lane_index = 1;
  *lane_sum = 1;
  std::vector<deeproute::perception::Lane> left_to_right_lanes;

  // count left non-reversed lanes
  deeproute::perception::Lane left_temp_lane = ego_lane;
  std::vector<deeproute::perception::Lane> left_lanes_to_ego;
  int left_topo_depth = 0;
  while (left_temp_lane.has_left_neighbor_id() &&
         !left_temp_lane.is_left_neighbor_reverse()) {
    if (id_to_lanes.find(left_temp_lane.left_neighbor_id()) ==
        id_to_lanes.end()) {
      MLOG(INFO) << "lane not found, pass.";
      return false;
    }

    left_temp_lane = id_to_lanes[left_temp_lane.left_neighbor_id()];

    if (left_temp_lane.virtual_()) {
      MLOG(INFO) << "lane virtual, pass.";
      // continue;
    }

    if (left_topo_depth >= 20) {
      MLOG(WARN) << "ras map topo invalid.";
      return false;
    }
    left_topo_depth += 1;

    *lane_index += 1;
    *lane_sum += 1;
    *debug_ras_map->add_lanes() = left_temp_lane;
    left_lanes_to_ego.push_back(left_temp_lane);
  }
  std::reverse(left_lanes_to_ego.begin(), left_lanes_to_ego.end());
  left_to_right_lanes.insert(left_to_right_lanes.end(),
                             left_lanes_to_ego.begin(),
                             left_lanes_to_ego.end());
  left_to_right_lanes.push_back(ego_lane);

  // count right non reversed lanes
  deeproute::perception::Lane right_temp_lane = ego_lane;
  std::vector<deeproute::perception::Lane> ego_to_right_lanes;
  int right_topo_depth = 0;
  while (right_temp_lane.has_right_neighbor_id() &&
         !right_temp_lane.is_right_neighbor_reverse()) {
    if (id_to_lanes.find(right_temp_lane.right_neighbor_id()) ==
        id_to_lanes.end()) {
      MLOG(INFO) << "lane not found, pass.";
      return false;
    }

    right_temp_lane = id_to_lanes[right_temp_lane.right_neighbor_id()];

    if (right_temp_lane.virtual_()) {
      MLOG(INFO) << "lane virtual, pass.";
    }

    if (right_topo_depth >= 20) {
      MLOG(WARN) << "ras map topo invalid.";
      return false;
    }
    right_topo_depth += 1;

    *lane_sum += 1;
    *debug_ras_map->add_lanes() = right_temp_lane;
    ego_to_right_lanes.push_back(right_temp_lane);
  }
  left_to_right_lanes.insert(left_to_right_lanes.end(),
                             ego_to_right_lanes.begin(),
                             ego_to_right_lanes.end());

  // set closest curb status
  *closest_curb_distance = std::numeric_limits<double>::max();
  if (min_dist_left_edge == std::numeric_limits<double>::max() &&
      min_dist_right_edge == std::numeric_limits<double>::max()) {
    *closest_curb_result = NO_CURB;
  } else if (min_dist_left_edge < min_dist_right_edge &&
             min_dist_left_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = LEFT;

    if (!left_lanes.empty()) {
      *closest_curb_distance =
          FindClosestLanAndDistance(closest_left_edge.polyline(), left_lanes);
    }
  } else if (min_dist_right_edge < min_dist_left_edge &&
             min_dist_right_edge != std::numeric_limits<double>::max()) {
    *closest_curb_result = RIGHT;

    if (!right_lanes.empty()) {
      *closest_curb_distance =
          FindClosestLanAndDistance(closest_right_edge.polyline(), right_lanes);
    }
  } else {
    MLOG(WARN) << "Closest curb result logic failure, should not happen. Set "
                  "to LEFT by "
                  "default.";
    *closest_curb_result = NO_CURB;
  }
  MLOG_TRACE("ProcessRasMapByTopology", timer.EndMilli());
  return true;
}

}  // namespace localization
}  // namespace deeproute
