#pragma once

// #include <float.h>

#include <fstream>
#include <iostream>
#include <vector>

#include <Eigen/Core>

#include "common/types.h"
// #include "lane_estimator/hmm.h"
#include <boost/circular_buffer.hpp>

#include "lane_estimator/lane_estimator.h"

namespace deeproute {

namespace localization {

struct Node {
  int layer_index;
  int lane_index;
  Node* prev; /**< previous optimal candidate */
  double ep;  /**< emission probability */
  double tp;  /**< transition probability from previous optimal candidate */
  double cumu_prob; /**< current node's accumulative probability */
};

using Layer = std::vector<Node>;

using NodePath = std::vector<const Node*>;

class TransitionGraph {
 public:
  TransitionGraph(const int state_num, const int max_lane_num);
  ~TransitionGraph() { fout_.close(); };

  static double CalTp(const int source, const int target, const double std = 1);

  static double CalEp(const int state, const int obs, const double std = 1);

  void ResetLayer(Layer* layer);

  const Node* FindOptimalCandidateOfLayer(const Layer& layer);

  NodePath BackTrack();

  std::vector<Layer>& GetLayersMutable();

  void PrintOptimalInfo();

  int StateNum() const;

  void UpdateLayer(Layer& cur_layer, Layer& next_layer, const int prev_layer_ob,
                   const int next_layer_ob, const int ras_lane_sum,
                   const int sd_lane_sum, const double& signed_dist_to_ego_lane,
                   bool* connected);

  void UpdateTransitionGraph(
      const boost::circular_buffer<int>& obs,
      const boost::circular_buffer<int>& ras_lane_sum,
      const boost::circular_buffer<int>& sd_lane_sum,
      const boost::circular_buffer<OdomMeasurement>& distances_to_egolane,
      const int prev_lane_index);

 private:
  std::vector<Layer> layers_;

  std::ofstream fout_;

  int graph_node_idx = 0;
  int from_index_ = 0;
};

}  // namespace localization
}  // namespace deeproute
