package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")

cc_library(
  name = "hmm",
  hdrs = ["hmm.h"],
  srcs = ["hmm.cpp"],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//common:types",
        "//proto:lock_on_road_config_cc_proto"
  ],
)

cc_library(
  name = "lane_estimator",
  hdrs = [
        "lane_estimator.h",
        "transition_graph.h",
  ],
  srcs = [
        "lane_estimator.cpp",
        "transition_graph.cpp",
  ],
  deps = [
        ":hmm",
        "//proto:lock_on_road_config_cc_proto",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:time",
        "@common//common:time_based_interpolation",
        "@common//common:time_types",
        "@common//common:types",
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//transform:transformation",
        "@lam_common//lam_common:logging",
  ],
)

cc_test(
  name = "lane_estimator_test",
  srcs = ["lane_estimator_test.cpp"],
  deps = [
        ":lane_estimator",
        "//proto:lock_on_road_config_cc_proto",
        "//third_party/eigen_checks:gtest",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:types",
        "@common//transform:transformation",
        "@boost_dynamic//:boost",
        "@gtest//:gtest_main",
  ],
  linkstatic=True,
)

