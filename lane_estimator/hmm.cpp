// #include "lane_estimator/hmm.h"
// #include <lam_common/logging.h>

// namespace deeproute {
// namespace localization {
// namespace {
// class TrackingStatus {
//  public:
//   double prob;
//   std::vector<int> v_path;
//   double v_prob;

//   TrackingStatus() {
//     prob = 0.0;
//     v_prob = 0.0;
//   }

//   TrackingStatus(double p, std::vector<int>& v_pth, double v_p) {
//     prob = p;
//     v_path = v_pth;
//     v_prob = v_p;
//   }
// };
// }  // namespace

// Hmm::Hmm(const int max_lane_num, const int max_states_num)
//     : max_lane_num_(max_lane_num), max_states_num_(max_states_num) {
//   // initialize initial state probs
//   init_p_.resize(max_lane_num_);
//   init_p_ = init_p_.setOnes() / max_lane_num_;

//   // resize tp and ep
//   trans_p_.resize(max_lane_num_, max_lane_num_);
//   trans_p_.setOnes();
//   trans_p_.normalize();

//   Eigen::MatrixXd I;
//   I.resize(max_lane_num_, max_lane_num_);
//   I.setIdentity();
//   emit_p_.resize(max_lane_num_, max_lane_num_);
//   emit_p_.setOnes();
//   emit_p_ = emit_p_ + I * 3;
//   emit_p_.normalize();

//   // initialize state space
//   for (int i = 0; i < max_lane_num_; i++) {
//     state_space_.push_back(i);
//   }
// }

// void Hmm::DecodeStatesByViterbi(const std::vector<int>& obs,
//                                 std::vector<int>* result_states) {
//   MLOG(INFO) << "obs: " << obs.size();

//   std::map<int, TrackingStatus> T;

//   for (auto state = state_space_.begin(); state != state_space_.end();
//        state++) {
//     std::vector<int> v_pth;
//     v_pth.push_back(*state);

//     T[*state] = TrackingStatus(init_p_[*state], v_pth, init_p_[*state]);
//   }

//   for (auto output = obs.begin(); output != obs.end(); output++) {
//     std::map<int, TrackingStatus> U;

//     for (auto next_state = state_space_.begin();
//          next_state != state_space_.end(); next_state++) {
//       TrackingStatus next_tracker;

//       for (auto source_state = state_space_.begin();
//            source_state != state_space_.end(); source_state++) {
//         TrackingStatus source_tracker = T[*source_state];

//         const double p = emit_p_.coeff(*source_state, *output) *
//                          trans_p_.coeff(*source_state, *next_state);

//         source_tracker.prob *= p;
//         source_tracker.v_prob *= p;

//         next_tracker.prob += source_tracker.prob;

//         if (source_tracker.v_prob > next_tracker.v_prob) {
//           next_tracker.v_path = source_tracker.v_path;
//           next_tracker.v_path.push_back(*next_state);
//           next_tracker.v_prob = source_tracker.v_prob;
//         }
//       }

//       U[*next_state] = next_tracker;
//     }

//     T = U;
//   }

//   TrackingStatus final_tracker;
//   for (auto state = state_space_.begin(); state != state_space_.end();
//        state++) {
//     TrackingStatus tracker = T[*state];

//     final_tracker.prob += tracker.prob;

//     if (tracker.v_prob > final_tracker.v_prob) {
//       final_tracker.v_path = tracker.v_path;
//       final_tracker.v_prob = tracker.v_prob;
//     }
//   }

//   MLOG(INFO) << "Total probability of the observation sequence: "
//              << final_tracker.prob;
//   MLOG(INFO) << "Probability of the Viterbi path: " << final_tracker.v_prob;
//   MLOG(INFO) << "The Viterbi path: ";

//   for (auto state = final_tracker.v_path.begin();
//        state != final_tracker.v_path.end(); state++) {
//     MLOG(INFO) << "VState: " << *state;
//     result_states->push_back(*state);
//   }

//   state_sequences_ = *result_states;
// }

// void Hmm::UpdateTransitionMatrix(const int current_lane,
//                                  const double body_yaw_change,
//                                  const double body_lateral_change) {
//   int next_lane = 0;
//   int nnext_lane = 0;
//   if (body_yaw_change >= 0.25 /*rad*/ && body_lateral_change >= 0.5) {
//     next_lane = current_lane - 1 >= 0 ? current_lane - 1 : 0;
//     nnext_lane = next_lane - 1 >= 0 ? next_lane - 1 : 0;
//   } else if (body_yaw_change <= -0.25 && body_lateral_change <= -0.5) {
//     next_lane = current_lane + 1;
//     nnext_lane = next_lane + 1;
//   } else {
//     return;
//   }

//   std::cout << "Before update: \n" << trans_p_ << std::endl;
//   std::cout << "current_lane: " << current_lane << std::endl;
//   std::cout << "next_lane: " << next_lane << std::endl;
//   std::cout << "nnext_lane: " << nnext_lane << std::endl;
//   std::cout << "trans_p_: " << trans_p_.size() << std::endl;

//   // MLOG(INFO) << "Before update: \n" << trans_p_;
//   // MLOG(INFO) << "current_lane: " << current_lane;
//   // MLOG(INFO) << "next_lane: " << next_lane;
//   // MLOG(INFO) << "nnext_lane: " << nnext_lane;
//   // MLOG(INFO) << "trans_p_: " << trans_p_.size();
//   CHECK(current_lane >= 0);
//   CHECK(next_lane >= 0);
//   CHECK(nnext_lane >= 0);

//   trans_p_(current_lane, next_lane) += 2;
//   // std::cout << "After update: \n" << trans_p_ << std::endl;
//   trans_p_(current_lane, nnext_lane) += 1;
//   // std::cout << "After update: \n" << trans_p_ << std::endl;
//   trans_p_.row(current_lane).normalize();
//   std::cout << "After update: \n" << trans_p_ << std::endl;
// }

// }  // namespace localization
// }  // namespace deeproute