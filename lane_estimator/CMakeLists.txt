add_library(localization_hmm
        transition_graph.cpp
        lane_estimator.cpp
)
target_link_libraries(localization_hmm
        ${GTEST_BOTH_LIBRARIES}
        lam_common_base
        lane_localization_proto
        boost_filesystem
        boost_system
        ${catkin_LIBRARIES} 
        # Common::os_interface
        common_time
)

add_executable(lane_estimator_test lane_estimator_test.cpp)
target_link_libraries(lane_estimator_test
        localization_hmm
        ${GTEST_BOTH_LIBRARIES}
        lam_common_base
        lane_localization_proto
        boost_filesystem
)
gtest_add_tests(TARGET lane_estimator_test)

install(TARGETS
        localization_hmm
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})
