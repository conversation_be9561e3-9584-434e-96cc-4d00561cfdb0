import os
import matplotlib.pyplot as plt
from google.protobuf import text_format

import numpy as np
import sys
import rosbag

from sensor_msgs.msg import PointCloud2, PointField
import sensor_msgs.point_cloud2 as pc2
import ctypes
import struct
import ros_numpy

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from perception.deeproute_perception_ras_map_pb2 import RASMap
from perception.deeproute_perception_obstacle_pb2 import PerceptionObstacles
# fmt: on

global perception_obstacles
global point_cloud

def read_results_from_bags(dir):
    # debugs = []

    folders = os.listdir(dir)
    folders.sort()

    for _, folder in enumerate(folders):
        bag_path = os.path.join(dir, folder)

        bag = rosbag.Bag(bag_path)
        for topic, msg, t in bag.read_messages(topics=['/perception/objects', '/sensors/lidar/combined_point_cloud']):
            # data = msg[1][4:]
            # if topic == '/perception/objects':
            #     obstacles = PerceptionObstacles()
            #     obstacles.ParseFromString(data)
            #     perception_obstacles = obstacles
            #     # print(perception_obstacles)
            #     # print(obstacles)
            if topic == '/sensors/lidar/combined_point_cloud':
                print(type(msg))
                point_cloud = ros_numpy.point_cloud2.pointcloud2_to_xyz_array(msg)
                print(np.shape(point_cloud))
                
        bag.close()



if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "folder path")
        sys.exit()

    read_results_from_bags(sys.argv[1])
