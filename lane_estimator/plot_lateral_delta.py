import copy
from scipy.signal import savgol_filter
import matplotlib.pyplot as plt
import numpy as np


import numpy as np
import matplotlib.pyplot as plt


# yhat = savgol_filter(y, 51, 3)  # window size 51, polynomial order 3

# plt.plot(x, y)
# plt.plot(x, yhat, color='red')
# plt.show()

file = open(
    "/home/<USER>/lane-based-localization-benchmark/build/test.log").readlines()

ts = []
delta_y = []
lane_indices = []
for line in file[10:-10]:
    line = line.split()
    # print(line)
    index = line[-3]
    t = line[-2]
    line = line[-1]

    lane_indices.append(float(index))
    delta_y.append(float(line))
    ts.append(float(t))

delta_y = np.array(delta_y)


def convolve_1d(input, kernel):
    kernel_size = len(kernel)
    kernel /= kernel_size
    result = copy.deepcopy(input)
    for index, ele in enumerate(input[:-kernel_size]):
        sliced = input[index:index+kernel_size]
        ret = np.dot(sliced, kernel)
        result[index] = ret
    return result


yhat = convolve_1d(delta_y, kernel=np.ones((500, 1)))
# yhat = convolve_1d(delta_y, kernel=np.array((0.4, 0.08, 0.04, 0.08, 0.4)))

color = 'tab:red'
fig, ax1 = plt.subplots()
ax1.set_xlabel('time')
ax1.set_ylabel('delta Y', color=color)
ax1.plot(ts, yhat, color=color)
ax1.plot(ts, delta_y, color='tab:green')
ax1.tick_params(axis='y', labelcolor=color)

color = 'tab:blue'
ax2 = ax1.twinx()
ax2.set_ylabel('lane index', color=color)
ax2.plot(ts, lane_indices, color=color)
ax2.tick_params(axis='y', color=color)

fig.tight_layout()
plt.show()
