#include "lane_estimator/lane_estimator.h"

#include <memory>
#include <numeric>
#include <queue>
#include <vector>

#include <Eigen/Dense>
#include <boost/circular_buffer.hpp>

#include "common/log.h"
#include "common/macros.h"
#include "common/time.h"
#include "common/time_based_interpolation.h"
#include "common/time_types.h"
#include "lane_estimator/transition_graph.h"
#include "transform/transformation.h"
namespace deeproute {
namespace localization {
namespace {

// void Pattern(const boost::circular_buffer<double>& values, double* mean,
//              double* std, double* dist_change) {
//   std::vector<double> v;
//   for (const auto value : values) {
//     v.push_back(value);
//   }

//   double sum = std::accumulate(v.begin(), v.end(), 0.0);
//   *mean = sum / v.size();

//   double sq_sum = std::inner_product(v.begin(), v.end(), v.begin(), 0.0);
//   *std = std::sqrt(sq_sum / v.size() - *mean * *mean);

//   const double max = *std::max_element(values.begin(), values.end());
//   const double min = *std::min_element(values.begin(), values.end());

//   *dist_change = max - min;
// }

int FindMostFrequentlyOccuredValue(const boost::circular_buffer<int>& indices) {
  std::map<int, int> index_counters;
  for (auto index : indices) {
    // MLOG(INFO) << "index and counter: " << index
    //            << ",coutner: " << index_counters[index];
    index_counters[index] += 1;
  }

  int most_occurred_lane_index = -1;
  int most_occured_lane_index_num = 0;
  for (const auto& index_counter : index_counters) {
    if (index_counter.second > most_occured_lane_index_num) {
      most_occured_lane_index_num = index_counter.second;
      most_occurred_lane_index = index_counter.first;
    }
  }

  // MLOG(INFO) << "most_occurred_lane_index: " << most_occurred_lane_index
  //            << ", num: " << most_occured_lane_index_num;
  return most_occurred_lane_index; 
}

}  // namespace

constexpr double kHalfTypicalLaneWidth = 2;

class LaneEstimator : public LaneEstimatorBase {
 public:
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW

  explicit LaneEstimator(const LaneIndexEstimatorConfig& config);

  void AddDistToCenterline(const int64_t odom_time,
                           const double dist_to_ego_lane_centerline) override;

  void AddPerceptionMeasurement(const int64_t ras_map_time,
                                const int lane_index, const int ras_lane_sum,
                                const int32_t ego_lane_id) override;

  Type GetStatus() override;

  bool SystemReady() override;

  int GetLaneIndex() override;

  int GetEgoLaneId() override;

  int GetLaneNum() override;

 protected:
  int hmm_window_size_;

  RasMeasurement ras_measurement_;

  boost::circular_buffer<int> lane_index_obs_;
  boost::circular_buffer<int> ras_lane_sums_;
  boost::circular_buffer<int> sd_lane_sums_;

  boost::circular_buffer<int> lane_number_obs_;

  // boost::circular_buffer<double> p_to_ref_distances_history_;

  boost::circular_buffer<OdomMeasurement> p_to_ego_lane_distances_history_;

  std::vector<double> ts_;

  double accumulated_body_yaw_;
  double accumulated_body_lateral_;

  int prev_lane_index_ = -1;
  int current_lane_index_ = -1;
  int32_t current_ras_lane_id_;
  int current_lane_sum_;

  std::shared_ptr<const drivers::gnss::Ins> prev_ins_ = nullptr;

  LaneIndexEstimatorConfig config_;

  Vector3d prev_position_;

  double start_time_ = -1;

  int64_t ras_map_time_ = -1;
  int64_t odom_time_ = -1;

  int window_size_ = 2;
  double lateral_change_threshold_ = 0.6;
};

LaneEstimator::LaneEstimator(const LaneIndexEstimatorConfig& config)
    : config_(config) {
  window_size_ = config.window_size();

  p_to_ego_lane_distances_history_.set_capacity(window_size_ * 10);

  lane_index_obs_.set_capacity(window_size_);
  ras_lane_sums_.set_capacity(window_size_);
  sd_lane_sums_.set_capacity(window_size_);
  lane_number_obs_.set_capacity(window_size_);

  lateral_change_threshold_ = config.lateral_change_threshold();
}

void LaneEstimator::AddDistToCenterline(
    const int64_t odom_time, const double dist_to_ego_lane_centerline) {
  if (ras_map_time_ != 0 && odom_time >= ras_map_time_) {
    odom_time_ = odom_time;
    p_to_ego_lane_distances_history_.push_back(
        {odom_time, dist_to_ego_lane_centerline});

    if (lane_index_obs_.full() &&
        p_to_ego_lane_distances_history_.size() >= lane_index_obs_.size()) {
      constexpr int kMaxLaneNum = 10;
      TransitionGraph tg(lane_index_obs_.size(), kMaxLaneNum);

      tg.UpdateTransitionGraph(lane_index_obs_, ras_lane_sums_, sd_lane_sums_,
                               p_to_ego_lane_distances_history_,
                               prev_lane_index_);
      const NodePath& node_path = tg.BackTrack();

      current_lane_index_ = node_path.back()->lane_index;
    }

  } else {
    p_to_ego_lane_distances_history_.clear();
  }
}

void LaneEstimator::AddPerceptionMeasurement(const int64_t ras_map_time,
                                             const int lane_index,
                                             const int ras_lane_sum,
                                             const int32_t ego_lane_id) {
  ras_map_time_ = ras_map_time;
  current_lane_sum_ = ras_lane_sum;
  current_ras_lane_id_ = ego_lane_id;

  lane_index_obs_.push_back(lane_index);
  ras_lane_sums_.push_back(ras_lane_sum);

  if (!config_.use_odom_measurement() && lane_index_obs_.full()) {
    current_lane_index_ = FindMostFrequentlyOccuredValue(lane_index_obs_);
  }
}

// clang-format off
/**
 * @brief
 *   INVALID: when no observations have been given for any sensor up till now
 *   INITIALIZING: when not enough observations have been received for any sensor 
 *   UNRELIABLE: when lane change signal's been  detected from odometry and estimated result differs from last one.
 *   GOOD: when RAS result has been stable for the last N frames and no lane change signal's been detected from odometry.
 *
 * @return Type
 */
// clang-format on
Type LaneEstimator::GetStatus() {
  const double sensor_time_diff = std::abs(ras_map_time_ - odom_time_) / 1e6;

  if (!config_.use_odom_measurement()) {
    /**
     * @brief
     *
     *    this is perception only mode,
     *    when no peception received, set type to invalid
     *    when perception meaurements not enougth, set to initializting
     *    when perception measurements full, set to good
     *
     */
    // MLOG(INFO) << "not using odom measurement.";
    if (lane_index_obs_.empty()) {
      return Type::LaneIndexEstimationResult_Type_INVALID;
    } else if (!lane_index_obs_.full()) {
      return Type::LaneIndexEstimationResult_Type_INITIALIZING;
    } else if (current_lane_index_ != prev_lane_index_) {
      return Type::LaneIndexEstimationResult_Type_UNRELIABLE;
    } else {
      return Type::LaneIndexEstimationResult_Type_GOOD;
    }
  } else {
    /*  this is perception + odom mode.
     *  1. when no odom info or ras map info, or sensor time diff > 0.5 sec,
     * invalid
     *  2. if lane index obs not full or odom info not full, initializing
     *  3. is lane change signal detected; if estimated current result != prev
     * result; if ras lane num differ too much from sd lane num; unreliable
     *  4. other cases: good
     */
    if (lane_index_obs_.empty() || p_to_ego_lane_distances_history_.empty() ||
        sensor_time_diff > 0.5) {
      return Type::LaneIndexEstimationResult_Type_INVALID;
    } else if (!lane_index_obs_.full() ||
               !p_to_ego_lane_distances_history_.full()) {
      return Type::LaneIndexEstimationResult_Type_INITIALIZING;
    } else if (p_to_ego_lane_distances_history_.back().dist_to_ego_lane >
                   kHalfTypicalLaneWidth ||
               current_lane_index_ != prev_lane_index_) {
      return Type::LaneIndexEstimationResult_Type_UNRELIABLE;
    } else {
      return Type::LaneIndexEstimationResult_Type_GOOD;
    }
  }
}

int LaneEstimator::GetLaneIndex() {
  prev_lane_index_ = current_lane_index_;

  // although index==0 or index>lane_sum are not likely to happen, still need to
  // protect the output.
  if (current_lane_index_ == 0) {
    return 1;
  } else if (current_lane_index_ > current_lane_sum_) {
    return current_lane_sum_;
  } else {
    return current_lane_index_;
  };
}

int32_t LaneEstimator::GetEgoLaneId() { return current_ras_lane_id_; }

int LaneEstimator::GetLaneNum() {
  // lane index is estimated, while lane sum is taken directly from ras map with
  // some post processing,
  return current_lane_sum_;
}

// check gnss quality
// check mean obs distance
bool LaneEstimator::SystemReady() {
  if (!config_.use_odom_measurement()) {
    return lane_index_obs_.full();
  } else {
    return lane_index_obs_.full() && !p_to_ego_lane_distances_history_.empty();
  }
}

std::unique_ptr<LaneEstimatorBase> CreateLaneEstimator(
    const LaneIndexEstimatorConfig& lane_config) {
  return std::make_unique<LaneEstimator>(lane_config);
}

}  // namespace localization
}  // namespace deeproute