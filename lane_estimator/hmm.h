// #pragma once

// #include <lam_common/logging.h>
// #include "common/types.h"
// #include "drivers/gnss/gnss_raw.pb.h"
// #include "drivers/gnss/ins.pb.h"
// #include "proto/lock_on_road_config.pb.h"

// namespace deeproute {
// namespace localization {
// constexpr int kMaxLaneNum = 5;
// constexpr int kMaxHiddenStatesHum = 10;

// struct State {
//   int index;
//   int lane_num;
// };

// // OdomMeasurement& odom, const RasMeasurement&
// struct OdomMeasurement {
//   double body_yaw_delta;
//   double body_lateral_delta;

//   OdomMeasurement(const double yaw_delta, const double body_lateral_delta)
//       : body_yaw_delta(yaw_delta), body_lateral_delta(body_lateral_delta) {}
// };

// struct RasMeasurement {
//   double lane_index;
//   double lane_num;
//   double score;  // belief score

//   RasMeasurement(const double index, const double num)
//       : lane_index(index), lane_num(num) {}
// };

// class Hmm {
//  public:
//   Hmm(const int max_lane_num, const int max_states_num);

//   bool AddMeasurement(const OdomMeasurement& odom, const RasMeasurement& ras) {
//     UpdateTransitionMatrix(ras.lane_index, odom.body_yaw_delta,
//                            odom.body_lateral_delta);

//     lane_num_ = ras.lane_num;
    
//     obs_sequences_.push_back(ras.lane_index);
//     if (obs_sequences_.size() < 10) {
//       return false;
//     }
//     for (const auto obs : obs_sequences_) {
//       std::cout << obs << std::endl;
//     }

//     DecodeStatesByViterbi(obs_sequences_, &state_sequences_);
//     MLOG(INFO) << "init_p_:\n" << init_p_;
//     MLOG(INFO) << "trans_p_:\n" << trans_p_;
//     MLOG(INFO) << "emit_p_:\n" << emit_p_;

//     return true;
//   }

//   std::vector<int> GetStateSequence() const { return state_sequences_; }

//   int GetLastestState() const { return *state_sequences_.end(); }
//   int GetLaneSumSameDirection() const { return lane_num_; }

//   Eigen::VectorXd GetInitP() const { return init_p_; }
//   Eigen::MatrixXd GetTransP() const { return trans_p_; }
//   Eigen::MatrixXd GetEmitP() const { return emit_p_; }

//  private:
//   void LoadModel() {}
//   void WriteModel() {}

//  private:
//   void Init(){};

//   bool SystemReady(){};

//   void DecodeStatesByViterbi(const std::vector<int>& obs,
//                              std::vector<int>* result_states);

//   void DecodeStateByViterbiFixedTp(const double body_yaw_change,
//                                    const double body_lateral_change,
//                                    const int ras_ob, int* result_state);

//   void UpdateTransitionMatrix(const int current_lane_index,
//                               const double body_yaw_change,
//                               const double body_lateral_change);

//  private:
//   std::vector<int> obs_sequences_;
//   std::vector<int> state_space_;

//   std::vector<int> state_sequences_;

//   int lane_num_ = 0;

//   int max_lane_num_;
//   int max_states_num_;

//   Eigen::VectorXd init_p_;
//   Eigen::MatrixXd trans_p_;
//   Eigen::MatrixXd emit_p_;
// };

// }  // namespace localization
// }  // namespace deeproute