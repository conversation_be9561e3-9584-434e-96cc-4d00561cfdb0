from __future__ import division  # Only for how I'm writing the transition matrix
import networkx as nx  # For the magic
import matplotlib.pyplot as plt  # For plotting
import networkx as nx
import pylab as plt
from networkx.drawing.nx_agraph import graphviz_layout, to_agraph
import pygraphviz as pgv


def load_graph(path):
    file = open(path).readlines()
    nodes = []

    node_dict = {}
    for line in file[1:]:
        line = line.split()

        nodes.append(line)

        print(line)
        node_dict[line[0]] = line

    return node_dict


# def draw_graph(node_dict):

#     G = nx.MultiDiGraph()
#     labels = {}
#     edge_labels = {}

#     for key in node_dict:
#         print(key)
#         node = node_dict[key]

#         G.add_node(key)

#         if(node[3] == '-1'):
#             continue
#         else:
#             G.add_edge(node[3], key)


#     pos = nx.spring_layout(G)
#     nx.draw_networkx_nodes(G, pos)
#     nx.draw_networkx_labels(G, pos)
#     nx.draw_networkx_edges(G, pos, edge_color='r', arrows = True)

#     plt.show()


def draw_graph(node_dict):

    print(len(node_dict))

    G = nx.DiGraph()

    layers = {}

    for key in node_dict:
        print(key)
        node = node_dict[key]

        if node[1] in layers:
            layers[node[1]].append(node_dict[key])
        else:
            layers[node[1]] = []
            layers[node[1]].append(node_dict[key])
    
    print("found layers num: ", len(layers))
    print(layers)
    
    for layer in layers:
        layer_nodes = [node[0] for node in layer]
        print("layer nodes: ", layer_nodes)
        G.add_nodes_from(layer_nodes,
                         style='filled', fillcolor='red')

    for key in node_dict:
        node = node_dict[key]
        G.add_edge(node[3], node[0], arrowsize=2.0)

    # G.add_nodes_from(['B', 'C', 'D'], style='filled', fillcolor='red')
    # G.add_nodes_from(['D', 'F', 'G'])
    # G.add_nodes_from(['H'], label='target')
    # G.add_edge('A', 'B', arrowsize=2.0)
    # G.add_edge('A', 'C', penwidth=2.0)
    # G.add_edge('A', 'D')
    # G.add_edges_from([('B', 'E'), ('B', 'F')], color='blue')
    # G.add_edges_from([('C', 'E'), ('C', 'G')])
    # G.add_edges_from([('D', 'F'), ('D', 'G')])
    # G.add_edges_from([('E', 'H'), ('F', 'H'), ('G', 'H')])

    # set defaults
    G.graph['graph'] = {'rankdir': 'TD'}
    G.graph['node'] = {'shape': 'circle'}
    G.graph['edges'] = {'arrowsize': '4.0'}

    A = to_agraph(G)
    print(A)
    A.layout('dot')
    A.draw('abcd.png')


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "path to graph")
        sys.exit()

    graph = load_graph(sys.argv[1])
    draw_graph(graph)
