// Copyright 2019 DeepRoute.ai. All Rights Reserved.

#include "lane_estimator/lane_estimator.h"

#include <math.h>

#include <fstream>
#include <iostream>

#include <gtest/gtest.h>

#include "proto/lock_on_road_config.pb.h"

#include "common/log.h"
#include "lam_common/types.h"
#include "third_party/eigen_checks/gtest.h"
#include "transform/transformation.h"
// #include "transition_graph.h"

namespace deeproute {
namespace localization {

namespace {

auto CreateLaneEstimationConfig(const bool use_odom = true,
                                const int window_size = 2) {
  LaneIndexEstimatorConfig config;
  config.set_window_size(window_size);
  config.set_use_odom_measurement(use_odom);

  return config;
}

}  // namespace

// TYPE 1, perception only mode.
TEST(LaneTest, Test10) {
  std::unique_ptr<LaneEstimatorBase> lane_estimator_ = CreateLaneEstimator(
      CreateLaneEstimationConfig(/*use_odom*/ false, /*window_size*/ 3));

  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 2, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_TRUE(!lane_estimator_->SystemReady());
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 2, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetStatus(),
            Type::LaneIndexEstimationResult_Type_INITIALIZING);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 2, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_TRUE(lane_estimator_->SystemReady());
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);

  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 1, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 1, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), 1);
}

TEST(LaneTest, Test11) {
  const auto lane_estimator_ = CreateLaneEstimator(
      CreateLaneEstimationConfig(/*use_odom*/ false, /*window_size*/ 3));

  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 0, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), -1);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 0, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), -1);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 0, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), 1);
}

TEST(LaneTest, Test12) {
  const auto lane_estimator_ = CreateLaneEstimator(
      CreateLaneEstimationConfig(/*use_odom*/ false, /*window_size*/ 3));

  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 6, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), -1);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 6, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), -1);
  lane_estimator_->AddPerceptionMeasurement(/*time*/ 0, /*index*/ 6, /*sum*/ 5,
                                            /*ego_lane_id*/ 11111);
  EXPECT_EQ(lane_estimator_->GetLaneIndex(), 5);
}

// //     Ras_map_1                  Est state
// // ________|__________________________*

// // index == 2, moves to the left
// // odom moves to the left
// TEST(LaneTest, Test1) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, 0.2 * i);
//   }

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 1);
// }

// // index == 2, moves to the right
// // odom moves to the right
// TEST(LaneTest, Test2) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, -0.2 * i);
//   }

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 3);
// }

// // going stratight
// TEST(LaneTest, Test3) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 5; i++) {
//     lane_estimator_->AddDistToCenterline(i, 0.1 * i);
//   }
//   for (size_t i = 5; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, -0.1 * i);
//   }

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);
// }

// // index == 1, make a move to the left
// // odom moves to the left
// TEST(LaneTest, Test4) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 1, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, 0.2 * i);
//   }

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 1);
// }

// // index == 3, make a move to the right
// // odom moves to the right
// TEST(LaneTest, Test5) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 3, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, -0.2 * i);
//   }

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 3);
// }

// /////////////////////////////////////////////////////////////////
// //     Ras_map_1                   Ras_map_2     Est state
// // ________|__________________________|___________*

// // index == 2, make a move to the left, then perception gives 2, index
// shouln't
// // change
// TEST(LaneTest, Test6) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, 0.2 * i);
//   }
//   lane_estimator_->AddPerceptionMeasurement(10, 2, 3);

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);
// }

// // index == 2, make a move to the right, then perception gives 2, index
// shouln't
// // change
// TEST(LaneTest, Test7) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, -0.2 * i);
//   }
//   lane_estimator_->AddPerceptionMeasurement(10, 2, 3);

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);
// }

// /////////////////////////////////////////////////////////////////

// // ras index == 2, odom doesn't make lateral move, index should remain the
// same TEST(LaneTest, Test8) {
//   const auto lane_estimator_ =
//       CreateLaneEstimator(CreateLaneEstimationConfig());

//   lane_estimator_->AddPerceptionMeasurement(0, 2, 3);
//   for (size_t i = 0; i < 10; i++) {
//     lane_estimator_->AddDistToCenterline(i, 0.5);
//   }
//   lane_estimator_->AddPerceptionMeasurement(10, 3, 3);

//   EXPECT_TRUE(lane_estimator_->SystemReady());
//   EXPECT_EQ(lane_estimator_->GetLaneIndex(), 2);
// }

}  // namespace localization
}  // namespace deeproute
