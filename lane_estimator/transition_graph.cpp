
#include "lane_estimator/transition_graph.h"

#include <limits>

#include <boost/filesystem.hpp>

#include "common/log.h"

namespace deeproute {
namespace localization {

const double kLaneTypicalWidth = 3.5;

TransitionGraph::TransitionGraph(const int state_num, const int max_lane_num) {
  for (int layer_index = 0; layer_index < state_num; layer_index++) {
    Layer layer;
    for (int lane_index = 0; lane_index < max_lane_num; lane_index++) {
      layer.push_back(Node{
          layer_index, lane_index, /*Prev Optimal*/ nullptr, /*ep*/ 0, /*tp*/ 0,
          /*cumu_prob*/ -std::numeric_limits<double>::infinity()});
    }
    layers_.push_back(layer);
  }

  ResetLayer(&layers_[0]);
}

double TransitionGraph::CalTp(const int source, const int target,
                              const double std) {
  const double a = std::abs(source - target) / std;
  return std::exp(-0.5 * a * a);
}

double TransitionGraph::CalEp(const int state, const int obs,
                              const double std) {
  const double a = std::abs(obs - state) / std;
  return std::exp(-0.5 * a * a);
}

void TransitionGraph::ResetLayer(Layer* layer) {
  for (auto iter = layer->begin(); iter != layer->end(); ++iter) {
    iter->cumu_prob = 0;
    iter->prev = nullptr;
  }
}

const Node* TransitionGraph::FindOptimalCandidateOfLayer(const Layer& layer) {
  const Node* opt_c = nullptr;
  double final_prob = -std::numeric_limits<double>::infinity();
  for (auto iter = layer.begin(); iter != layer.end(); ++iter) {
    if (final_prob < iter->cumu_prob) {
      final_prob = iter->cumu_prob;
      opt_c = &(*iter);
    }
  }
  return opt_c;
}

NodePath TransitionGraph::BackTrack() {
  Node* track_cand = nullptr;
  double final_prob = -std::numeric_limits<double>::infinity();
  Layer& last_layer = layers_.back();
  for (auto iter = last_layer.begin(); iter != last_layer.end(); ++iter) {
    if (final_prob < iter->cumu_prob) {
      final_prob = iter->cumu_prob;
      track_cand = &(*iter);
    }
  }

  NodePath opath;
  if (final_prob > -std::numeric_limits<double>::infinity()) {
    opath.push_back(track_cand);

    while ((track_cand = track_cand->prev) != nullptr) {
      opath.push_back(track_cand);
    }
    std::reverse(opath.begin(), opath.end());
  }
  return opath;
}

void TransitionGraph::PrintOptimalInfo() {
  const int N = layers_.size();
  if (N < 1) return;
  const Node* global_opt_node = nullptr;
  for (int i = N - 1; i >= 0; --i) {
    const Node* layer_opt_node = FindOptimalCandidateOfLayer(layers_[i]);
    if (global_opt_node != nullptr) {
      global_opt_node = global_opt_node->prev;
    } else {
      global_opt_node = layer_opt_node;
    }
    MLOG(INFO) << "Layer id: " << i
               << ", layer opt node: " << layer_opt_node->lane_index;
  }
  MLOG(INFO) << "Global opt layer index:  " << global_opt_node->layer_index
             << ", lane index: " << global_opt_node->lane_index;
};

int TransitionGraph::StateNum() const { return layers_.size(); }

std::vector<Layer>& TransitionGraph::GetLayersMutable() { return layers_; }

void TransitionGraph::UpdateLayer(Layer& cur_layer, Layer& next_layer,
                                  const int prev_layer_ob,
                                  const int next_layer_ob,
                                  const int ras_lane_sum, const int sd_lane_sum,
                                  const double& signed_dist_to_ego_lane,
                                  bool* connected) {
  // MLOG(INFO) << "prev layer ob: " << prev_layer_ob;
  bool layer_connected = false;

  for (auto iter_a = cur_layer.begin(); iter_a != cur_layer.end(); ++iter_a) {
    const int cur_lane_index = iter_a->lane_index;

    for (auto iter_b = next_layer.begin(); iter_b != next_layer.end();
         ++iter_b) {
      const int next_lane_index = iter_b->lane_index;

      double tp_std = 1;
      double ep_std = 1;

      // when no lateral movement, less likely to change lane, relys less on
      // observation.
      // if (std::abs(odom_measurement.windowed_signed_dist_to_geo_ref_point_) <
      //         kLaneTypicalWidth / 2.0 ||
      //     std::abs(dist_to_ego_lane) < kLaneTypicalWidth / 2.0) {
      //   tp_std = std::min(
      //       std::abs(odom_measurement.windowed_signed_dist_to_geo_ref_point_),
      //       std::abs(dist_to_ego_lane));
      //   tp_std = std::max(tp_std, 0.5);
      //   ep_std = 3;
      // } else {
      //   tp_std = 3;
      //   ep_std = 1;
      // }

      // when ras lane sum == sd lane sum
      if (ras_lane_sum == sd_lane_sum) {
        ep_std = 1;
        tp_std = 10;
      }

      const double tp =
          TransitionGraph::CalTp(cur_lane_index, next_lane_index, tp_std);
      const double ep =
          TransitionGraph::CalEp(next_lane_index, next_layer_ob, ep_std);
      const double cumu_prob = iter_a->cumu_prob + tp + ep;

      if (cumu_prob >= iter_b->cumu_prob) {
        if (cumu_prob > -std::numeric_limits<double>::infinity()) {
          layer_connected = true;
        }

        iter_b->cumu_prob = cumu_prob;
        iter_b->prev = &(*iter_a);
        iter_b->tp = tp;
      }

      graph_node_idx += 1;
    }

    from_index_ += 1;
  }

  if (connected != nullptr) {
    *connected = layer_connected;
  }
}

void TransitionGraph::UpdateTransitionGraph(
    const boost::circular_buffer<int>& obs,
    const boost::circular_buffer<int>& ras_lane_sum,
    const boost::circular_buffer<int>& sd_lane_sum,
    const boost::circular_buffer<OdomMeasurement>& distances_to_egolane,
    const int prev_lane_index) {
  auto temp_obs = obs;

  // update prev result
  const int obs_size = obs.size();
  if (prev_lane_index != -1) {
    temp_obs[obs_size - 2] = prev_lane_index;
  }

  std::vector<Layer>& layers = GetLayersMutable();

  // init first layer eps based on observation
  for (auto iter = layers[0].begin(); iter != layers[0].end(); ++iter) {
    const double ep = TransitionGraph::CalEp(iter->lane_index, temp_obs[0]);
    const double tp = 0;
    iter->ep = ep;
    iter->cumu_prob = 0 + ep + tp;

    graph_node_idx += 1;
  }

  for (size_t i = 0; i + 1 < layers.size(); ++i) {
    bool connected = false;

    UpdateLayer(layers[i], layers[i + 1], temp_obs[i], temp_obs[i + 1],
                ras_lane_sum[i], sd_lane_sum[i],
                distances_to_egolane[i].dist_to_ego_lane, &connected);

    if (!connected) {
      break;
    }
  }
}

}  // namespace localization
}  // namespace deeproute
