import matplotlib.pyplot as plt
from google.protobuf import text_format

import numpy as np
import sys
import rosbag

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from perception.deeproute_perception_ras_map_pb2 import RASMap
from lock_on_road.lock_on_road_pb2 import LaneIndexEstimationResult
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadDebugInfo, LockOnRoadSinglePoseDebugInfo
# fmt: on

import os

def read_results_from_bags(dir):
    debugs = []

    folders = os.listdir(dir)
    folders.sort()

    for _, folder in enumerate(folders):
        bag_path = os.path.join(dir, folder)
        
        bag = rosbag.Bag(bag_path)
        for topic, msg, t in bag.read_messages(topics=['/localization/lane_index'], raw=True):
            data = msg[1][4:]
            lane_index = LaneIndexEstimationResult()
            lane_index.ParseFromString(data)
            debugs.append(lane_index)
        bag.close()
        
    print("Loaded: ", len(debugs), " messages.")
    return debugs


def draw(debugs):
    if(len(debugs) == 0):
        return

    lane_indics = []
    lane_sums = []
    statuses = []
    for debug in debugs:
        lane_indics.append(int(debug.sd_lane_index))
        lane_sums.append(int(debug.sd_lane_sum))
        statuses.append(int(debug.type))

    xs = [x for x in range(len(lane_indics))]
    colors = []
    for status in statuses:
        if(status == 0):
            colors.append('red')
        elif(status == 1):
            colors.append('red')
        elif(status == 2):
            colors.append('green')
        elif(status == 3):
            colors.append('green')

    plt.scatter(xs, lane_indics, color=colors)
    plt.show()


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "folder path")
        sys.exit()

    debugs = read_results_from_bags(sys.argv[1])
    draw(debugs)
