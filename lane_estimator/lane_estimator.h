#pragma once

#include "drivers/gnss/gnss_raw.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "common/types.h"

namespace deeproute {
namespace localization {

struct OdomMeasurement {
  int64_t time;
  double dist_to_ego_lane;
};

struct RasMeasurement {
  double lane_index;
  double lane_num;
};

using Type = LaneIndexEstimationResult::Type;

class LaneEstimatorBase {
 public:
  virtual ~LaneEstimatorBase() = default;

  virtual void AddDistToCenterline(
      const int64_t odom_time, const double dist_to_ego_lane_centerline) = 0;

  virtual void AddPerceptionMeasurement(const int64_t ras_map_time,
                                        const int lane_index,
                                        const int ras_lane_sum,
                                        const int32_t ego_lane_id) = 0;

  virtual Type GetStatus() = 0;

  virtual bool SystemReady() = 0;

  virtual int GetLaneIndex() = 0;

  virtual int32_t GetEgoLaneId() = 0;

  virtual int GetLaneNum() = 0;
};

std::unique_ptr<LaneEstimatorBase> CreateLaneEstimator(
    const LaneIndexEstimatorConfig& lane_config);

}  // namespace localization
}  // namespace deeproute