from matplotlib.pyplot import legend
import numpy as np
from yaml import load
import re
        
def load_file(path):

    file = open(path).readlines()

    obs = []
    est = []

    for line in file:
        
        line = re.split(" |,|\n", line)
        print(line)
         
        if len(line) <=4 or len(line[-4]) >= 2:
            continue
         
        est.append(int(line[-4]))
        obs.append(int(line[-2]))

    # return obs, est
    return obs[20:], est[20:]


def plot(file_path):

    import matplotlib.pyplot as plt
    obs, ests = load_file(file_path)
    ts = [x for x in range(len(obs))]

    wrong_obs = 0
    for ob in obs:
        if ob != 2:
            wrong_obs += 1
        
    wrong_est = 0
    for est in ests:
        if est != 2:
            wrong_est += 1
    
    print("Failed obs case num: ", wrong_obs)
    print("Failed est case num: ", wrong_est)
    print("Obs fail ratio: ", float(wrong_obs)/len(obs))
    print("Est fail ratio: ", float(wrong_est)/len(obs))
    
    plt.title('Lane Index Estimation')
    plt.xlabel('Time stamp')
    plt.ylabel('Lane Index')
    plt.scatter(ts, obs)
    plt.scatter(ts, [float(e) + 0.1 for e in ests])
    plt.legend(['Observations' , 'Estimations'])
    plt.ylim([0, 5])
    
    plt.text(0.0, 4.0, "Failed obs case num: " + str(wrong_obs))
    plt.text(0.0, 3.9, "Failed est case num: " + str(wrong_est))
    plt.text(0.0, 3.8, "Obs fail ratio: " + str(float(wrong_obs)/len(obs)))
    plt.text(0.0, 3.7, "Est fail ratio: " + str(float(wrong_est)/len(obs)))
    
    plt.show()
    

    
if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "path to file with obs and estimations")
        sys.exit()

    plot(sys.argv[1])
