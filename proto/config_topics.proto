syntax = "proto2";

package deeproute.localization.config;

message ConfigTopics {
  required string pose_topic = 1;
  required string odometry_topic = 2;
  required string ras_map_topic = 3;
  required string routing_response_topic = 4;
  required string gnss_topic = 5;
  required string publish_lock_on_road_topic = 6;
  required string publish_lane_index_estimation_topic = 7;
  required string publish_lock_on_road_debug_topic = 8;
  optional string internal_state_topic = 9;
  optional string perception_topic = 10;
  optional string debug_ras_map_topic = 11;
  optional string lidar_topic = 12;
}