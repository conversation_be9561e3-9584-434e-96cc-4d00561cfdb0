package(default_visibility = ["//visibility:public"], features=["-hidden_visibility_feature"])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

STRIP_IMPORT_PREFIX = "/proto"
proto_library(
    name = "config_topics_proto",
    srcs = [
        "config_topics.proto",
    ],
    strip_import_prefix = STRIP_IMPORT_PREFIX,
)

cc_proto_library(
    name = "config_topics_cc_proto",
    deps = [":config_topics_proto"],
)

#struct_proto_library(
#   name = "config_topics_struct_proto",
#    srcs = [
#        "config_topics.proto",
#    ],
#)

proto_library(
    name = "benchmark_proto",
    srcs = [
        "benchmark.proto",
    ],
    strip_import_prefix = STRIP_IMPORT_PREFIX,
    deps = [
        "@common//proto/lock_on_road:lock_on_road_proto",
        "@common//proto/common:geometry_proto",
    ],
)

cc_proto_library(
    name = "benchmark_cc_proto",
    deps = [":benchmark_proto"],
)

#struct_proto_library(
#   name = "benchmark_struct_proto",
#    srcs = [
#        "benchmark.proto",
#    ],
#    deps = [
#        "@common//common:geometry_struct_proto",
#        "//lock_on_road:lock_on_road_struct_proto",
#    ],
#)

proto_library(
    name = "lock_on_road_config_proto",
    srcs = [
        "lock_on_road_config.proto",
    ],
    strip_import_prefix = STRIP_IMPORT_PREFIX,
    import_prefix = "proto",
    deps = [
        ":config_topics_proto",
    ],
)

cc_proto_library(
    name = "lock_on_road_config_cc_proto",
    deps = [":lock_on_road_config_proto"],
)

#struct_proto_library(
#   name = "lock_on_road_config_struct_proto",
#    srcs = [
#        "lock_on_road_config.proto",
#    ],
#    deps = [
#        "//:config_topics_struct_proto",
#    ],
#)

proto_library(
    name = "ground_truth_proto",
    srcs = [
        "ground_truth.proto",
    ],
    strip_import_prefix = STRIP_IMPORT_PREFIX,
    deps = [
        "@common//proto/common:geometry_proto",
    ],
)

cc_proto_library(
    name = "ground_truth_cc_proto",
    deps = [":ground_truth_proto"],
)

#struct_proto_library(
#   name = "ground_truth_struct_proto",
#    srcs = [
#        "ground_truth.proto",
#    ],
#    deps = [
#        "@common//common:geometry_struct_proto",
#    ],
#)

