syntax = "proto2";

package deeproute.localization;

import "common/geometry.proto";

message GroundTruthMeasurement {
  optional int64 time_us = 1;
  optional common.Point3D position = 2;
  optional common.Point3D euler_angles = 3;
  optional int32 lane_index = 4;
  optional int32 lane_sum = 5;
  optional int32 link_id = 6;
  optional float distance_to_link_start = 7;
  optional float distance_to_link_end = 8;
  repeated int32 left_lane_ids = 9;
  repeated int32 right_lane_ids = 10;
  optional int32 current_lane_id = 11;
}

message GroundTruthMeasurements {
  repeated GroundTruthMeasurement ground_truth = 1;
}
