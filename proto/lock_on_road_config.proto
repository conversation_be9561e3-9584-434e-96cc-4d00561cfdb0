syntax = "proto2";

package deeproute.localization;

import "config_topics.proto";

message LaneIndexEstimatorConfig {
  enum Mode {
    VISION = 1;
    VISION_AND_ODOM = 2;
  }
  optional Mode mode = 1;
  // optional int32 body_yaw_rate_length = 2;
  // optional int32 body_lateral_acc_length = 3;
  optional int32 window_size = 2;
  optional double lateral_change_threshold = 3;

  optional bool enable_lane_estimation = 4;
  optional bool process_with_topology = 5;
  optional bool use_odom_measurement = 6;
}

message MapMatchingConfig {
  optional string map_path = 1;
  optional float window_size = 2;
  optional float obs_noise = 3;
  optional float transition_noise = 4;
  optional float heading_noise = 5;
  optional float search_radius = 6;
  optional string fmm_log_level =
      7;  // info; debug: viterbi detail; trace: network detail;
  optional string ubodt_path = 8;
  optional int32 neigbhor_num = 9;
  optional int32 history_length = 10;
  optional bool enable_rtk_quality_control = 11;

  message NoiseInjection {
    optional bool enable = 1;
    optional double mean = 2;
    optional double std = 3;
  }

  optional NoiseInjection noise_injection = 12;

  message LogConfig {
    optional string log_path = 1;
    optional bool enable_write_log = 2;
    /// 0-online log; 1-local log
    optional int32 log_type = 3;
  }
  optional LogConfig log_config = 13;

  optional bool enable_amap = 14;

  optional bool enable_routing_mask_yaw_judgement = 15;
}

message DdmmConfig {
  optional bool enable_ddmm = 1;
  optional bool enable_ddmm_debug_output = 2;
  optional bool use_dla = 3;
  optional bool use_calib_file = 4;
}

message LockOnRoadConfig {
  optional LaneIndexEstimatorConfig lane_estimator_config = 1;
  optional MapMatchingConfig map_matching_config = 2;
  optional config.ConfigTopics topics = 3;
  optional bool enable_visualizer = 4;
  optional bool pub_sd_map = 5;
  optional bool pub_semantic_map = 6;
  repeated string debug_edge = 7;
  optional bool enable_ras_map_post_processing = 8;
  optional DdmmConfig ddmm_config = 9;
}
