function(PROTOBUF_GENERATE_PY SRCS)
  if(NOT ARGN)
    message(SEND_ERROR "Error: PROTOBUF_GENERATE_PY() called without any proto files")
    return()
  endif()

  set(_protobuf_include_path -I ${CMAKE_CURRENT_SOURCE_DIR}:${PROJECT_SOURCE_DIR}/external/common/proto:${PROJECT_SOURCE_DIR}/external/lam_common/lam_common/proto)

  set(${SRCS})

  foreach(FIL ${ARGN})
    get_filename_component(ABS_FIL ${FIL} ABSOLUTE)
    get_filename_component(FIL_WE ${FIL} NAME_WE)

    if(NOT PROTOBUF_GENERATE_CPP_APPEND_PATH)
      get_filename_component(FIL_DIR ${FIL} DIRECTORY)

      if(FIL_DIR)
        set(FIL_WE "${FIL_DIR}/${FIL_WE}")
      endif()
    endif()

    list(APPEND ${SRCS} "${CMAKE_CURRENT_BINARY_DIR}/${FIL_WE}_pb2.py")
    exec_program(protoc ARGS --python_out ${CMAKE_CURRENT_BINARY_DIR} ${_protobuf_include_path} ${ABS_FIL})
  endforeach()

  execute_process(COMMAND touch ${CMAKE_CURRENT_BINARY_DIR}/__init__.py)
  set_source_files_properties(${${SRCS}} PROPERTIES GENERATED TRUE)
  set(${SRCS} ${${SRCS}} PARENT_SCOPE)
endfunction()

file(GLOB ProtoFiles *.proto)

include_directories(${CMAKE_CURRENT_BINARY_DIR}/joint/proto)
include_directories(${CMAKE_CURRENT_BINARY_DIR})

set(PROTOBUF_GENERATE_CPP_APPEND_PATH FALSE)
set(Protobuf_IMPORT_DIRS 
  ${CMAKE_CURRENT_SOURCE_DIR}
  ${CMAKE_SOURCE_DIR}/external/common/proto
  ${CMAKE_SOURCE_DIR}/external/lam_common/lam_common/proto)

PROTOBUF_GENERATE_CPP(ProtoSources ProtoHeaders ${ProtoFiles})
add_library(lane_localization_proto ${ProtoSources})
target_link_libraries(lane_localization_proto ${PROTOBUF_LIBRARIES} Common::proto lam_common::proto)

PROTOBUF_GENERATE_PY(ProtoPys ${ProtoFiles})
install(TARGETS lane_localization_proto
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
install(FILES ${ProtoHeaders}
        DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION}/proto)