syntax = "proto2";

package deeproute.localization;

import "common/geometry.proto";
import "lock_on_road/lock_on_road.proto";
// import "lam_common/routing/navinfo_routing.proto";

message LockOnRoadGroundTruth {
  optional int64 pose_time_us = 1;  // time of pose
  optional int64 ras_map_time = 2;  // time of ras map measurement
  optional int32 lane_index =
      3;  // estimation result, from left to right, of same direction
  optional int32 lane_sum =
      4;  // estimation result, from left to right, of same direction
  optional int64 sd_link_id = 5;  // corresponding link id of returned SD map
  optional common.Point3D position = 6;  // UTM position of pose, for debug
  optional common.Point3D matched_position =
      7;  // geo referenced position on the link, in UTM, for debug
  optional LockOnRoadResult.Status status = 8;
  optional LaneIndexEstimationResult.Type lane_index_status = 9;
}

message UtmRoutingResponse{
  optional string request_id = 1;
  repeated common.Point3D way_points  = 2;
}

message LockOnRoadGroundTruths {
  repeated LockOnRoadGroundTruth results = 1;
  optional string region_name = 2;
  repeated UtmRoutingResponse utm_routing_responses = 3;
  repeated string bag_names = 4;
}