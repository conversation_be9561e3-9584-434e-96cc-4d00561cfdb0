package(default_visibility = ["//visibility:public"])


load("@deeproute_build_tools//:globals.bzl", "global_definitions")
load("@deeproute_build_tools//:cpu_configs.bzl", "CPU_NAMES")
load("@deeproute_build_tools//rules/pkg:deeproute_release.bzl", "deeproute_release_package")

exports_files([ "setup.bash" ])

global_definitions()

deeproute_release_package(
    name = "bin",
    srcs = [
        # "@driver//integration:mainboard",
        "//scripts:convert_wgs84_to_gcj02_from_txt",
        "//executable:ddmm_debug_performance_on_bag",
        "//executable:generate_autotune_file",
        "//executable:run_lor_on_bags",
        "//executable:run_lor_on_case",
    ] + select({
        "@deeproute_build_tools//:{}".format(CPU_NAMES.gcc_x86): [
            "//executable:ddmm_debug_on_bag",
        ],
        "@deeproute_build_tools//:{}".format(CPU_NAMES.nvidia_orin): [],
    }),
    mode = "0755",
    package_dir = "bin",
)

