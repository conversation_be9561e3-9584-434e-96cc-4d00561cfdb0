#!/bin/bash

SCRIPT_PATH=$(
    cd $(dirname "$0")
    pwd
)

REPO_ROOT=$SCRIPT_PATH/../../

set -e

#VEHICLE_ID=${VEHICLE_ID} RELEASE_TYPE=${CI_RELEASE_TYPE} ARCH_VERSION=${CI_RELEASE_ARCH_VERSION} CORE_NUMS=${CI_CORE_NUMS} ./pipeline/package.sh

VEHICLE_ID=${VEHICLE_ID:-"C01-PT"}
ARCH_VERSION=${ARCH_VERSION:-"x86_1804"}
RELEASE_TYPE=${RELEASE_TYPE:-"release"}
CORE_NUMS=${CORE_NUMS:-"8"}

declare arch="$(uname -m)"
declare compile_flags_with_werror=""
if [[ "${arch}" == "x86_64" ]]; then
    compile_flags_with_werror="--config=werror"
elif [[ "${arch}" == "aarch64" ]]; then
    compile_flags_with_werror=""
else
    :
fi

build_options="${build_options} -j ${CORE_NUMS}"
build_options="${build_options} --config=${ARCH_VERSION}"
build_options="${build_options} --config=${RELEASE_TYPE}"
build_options="${build_options} --config=${VEHICLE_ID}"
build_options="${build_options} --config=remote_download"
build_options="${build_options} --config=remote_cache"
build_options="${build_options} --profile=/tmp/profile"
build_options="${build_options} --cache_test_results=no"
#build_options="${build_options} ${compile_flags_with_werror}"
build_targets=${build_targets:=$(bazel query "kind(cc_test, //...)")}

echo ""
echo "========================================Platform Build=================================================="
echo "build_options=${build_options}"
echo "build_targets=${build_targets}"

bazel build ${build_options} ${build_targets}
bazel analyze-profile /tmp/profile

echo "===== begin to run all tests ===="

# show test output on failure
export CTEST_OUTPUT_ON_FAILURE=1

bazel test ${build_options} ${build_targets} # --sandbox_debug

