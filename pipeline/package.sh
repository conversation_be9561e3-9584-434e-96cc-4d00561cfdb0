#!/bin/bash

set -e

echo "bazel vesion: $(bazel --version)"

# Show help message
show_help() {
    echo "Usage: $0 [-i] [-c] [-r] [-j] [-a] [-d] [--chip] [-h]"
    echo "Options:"
    echo "  -i          Install compiled packages"
    echo "  -c          Generate compile_commands.json, for c++ lsp clangd"
    echo "  --chip      Chip type(number), 0 (nvidia, default), 1 (qualcomm)"
    echo "  -r          Enable ros"
    echo "  -a          Compile all targets (include all unit tests)"
    echo "  -d          Build: release with debug symbol"
    echo "  -j          Run N jobs in parallel"
    echo "  -p          Compile data provider without source code"
    echo "  -h, --help  Display this help message"
    exit
}

# Default values
CHIP_NUM=0 # Default chip: 0 (nvidia)
INSTALL_COMPILED_PACKAGE=0
GENERATE_COMPILE_COMMANDS=0
ENABLE_ROS=0
BUILD_WITH_DEBUG_SYMBOL=0
COMPILE_ALL_TARGET=0
CORE_NUMS=${CORE_NUMS:-"8"}                         # Default number of cores: 8
COMPILE_DATA_PROVIDER=${COMPILE_DATA_PROVIDER:-"1"} # Default: compile data provider

# Parse command-line arguments
while [[ "$#" -gt 0 ]]; do
    case "$1" in
    -i) INSTALL_COMPILED_PACKAGE=1 ;;
    -c) GENERATE_COMPILE_COMMANDS=1 ;;
    -r) ENABLE_ROS=1 ;;
    -a) COMPILE_ALL_TARGET=1 ;;
    -d) BUILD_WITH_DEBUG_SYMBOL=1 ;;
    -j)
        shift
        CORE_NUMS="$1"
        ;;
    --chip)
        shift
        CHIP_NUM="$1"
        ;;
    -p) COMPILE_DATA_PROVIDER=0 ;; # 0 means don't compile with source code
    -h | --help) show_help ;;
    -*)
        # Loop through each character in a combined short option
        for ((i = 1; i < ${#1}; i++)); do
            case "${1:$i:1}" in
            i) INSTALL_COMPILED_PACKAGE=1 ;;
            c) GENERATE_COMPILE_COMMANDS=1 ;;
            r) ENABLE_ROS=1 ;;
            a) COMPILE_ALL_TARGET=1 ;;
            d) BUILD_WITH_DEBUG_SYMBOL=1 ;;
            p) COMPILE_DATA_PROVIDER=0 ;; # 0 means don't compile with source code
            *)
                echo "Unknown option: ${1:$i:1}"
                show_help
                ;;
            esac
        done
        ;;
    *)
        echo "Unknown option: $1"
        show_help
        ;;
    esac
    shift
done

# Print parsed values for debugging
echo "INSTALL_COMPILED_PACKAGE: $INSTALL_COMPILED_PACKAGE"
echo "GENERATE_COMPILE_COMMANDS: $GENERATE_COMPILE_COMMANDS"
echo "ENABLE_ROS: $ENABLE_ROS"
echo "COMPILE_ALL_TARGET: $COMPILE_ALL_TARGET"
echo "BUILD_WITH_DEBUG_SYMBOL: $BUILD_WITH_DEBUG_SYMBOL"
echo "CORE_NUMS: $CORE_NUMS"
echo "CHIP_NUM: $CHIP_NUM"
echo "COMPILE_DATA_PROVIDER: $COMPILE_DATA_PROVIDER"

RUNNING_DIR=$(pwd)
SCRIPT_DIR=$(cd $(dirname "${BASH_SOURCE[0]}") && pwd)
REPO_ROOT=$(readlink -m $SCRIPT_DIR/../)

CHIP="nvidia"
if [[ "${CHIP_NUM}" == "1" ]]; then
    CHIP="qualcomm"
fi

echo "REPO_ROOT: ${REPO_ROOT}"
echo "CHIP: ${CHIP}"

cd $REPO_ROOT

# do not worry, SOTA ci can override this config
RELEASE_TYPE=${RELEASE_TYPE:-"relwithdebinfo"}

# VEHICLE_ID=${VEHICLE_ID} RELEASE_TYPE=${CI_RELEASE_TYPE} ARCH_VERSION=${CI_RELEASE_ARCH_VERSION} CORE_NUMS=${CI_CORE_NUMS} ./pipeline/package.sh
ARCH_VERSION_CPU="x86"
ARCH_VERSION_VERSION="2004"

VEHICLE_ID=${VEHICLE_ID:-"C01-PT"}

declare arch="$(uname -m)"
declare compile_flags_with_werror="--config=werror"
if [[ "${arch}" == "x86_64" ]]; then
    export PLATFORM="X86"
    ARCH_VERSION_CPU="x86"
elif [[ "${arch}" == "aarch64" ]]; then
    export PLATFORM="ORIN"
    ARCH_VERSION_CPU="aarch64"
else
    :
fi

if [[ $(lsb_release -is) == "Ubuntu" ]]; then
    version=$(lsb_release -r | awk '{print $2}')
    if [[ "$version" == "18.04" ]]; then
        echo "This is Ubuntu 18.04."
        export UBUNTU="1804"
        ARCH_VERSION_VERSION="1804"
    elif [[ "$version" == "20.04" ]]; then
        echo "This is Ubuntu 20.04."
        export UBUNTU="2004"
        ARCH_VERSION_VERSION="2004"
    else
        echo "This is neither Ubuntu 18.04 nor 20.04."
    fi
fi

DEFAULT_ARCH_VERSION=${ARCH_VERSION_CPU}_${ARCH_VERSION_VERSION}
ARCH_VERSION=${ARCH_VERSION:-"${DEFAULT_ARCH_VERSION}"}

# get DEEPROUTE_PATH, or set to default value: /opt/deeproute
DEEPROUTE_PATH=${DEEPROUTE_PATH:-"/opt/deeproute"}

if [[ "${BUILD_WITH_DEBUG_SYMBOL}" == "1" ]]; then
    # build_options="${build_options} --copt=-g --strip=never "
    RELEASE_TYPE="relwithdebinfo"
fi

build_options="${build_options} -j ${CORE_NUMS}"
build_options="${build_options} --config=${ARCH_VERSION}"
build_options="${build_options} --config=${RELEASE_TYPE}"
build_options="${build_options} --config=${VEHICLE_ID}"
build_options="${build_options} --config=${CHIP}"

if [[ "${ENABLE_ROS}" == "1" ]]; then
    build_options="${build_options} --config=enable_ros "
fi

if [[ "${COMPILE_DATA_PROVIDER}" == "0" ]]; then
    build_options="${build_options} --config=build_from_deb"
fi

build_options="${build_options} --profile=/tmp/profile"
build_options="${build_options} ${compile_flags_with_werror}"
build_targets=${build_targets:="//..."}

if [[ "${COMPILE_ALL_TARGET}" == "1" ]]; then
    echo " -------------- add all bazel target -----------"
    build_targets="//..."
fi

echo ""
echo "========================================Lock-on-road Build=================================================="
echo "build_options=${build_options}"
echo "build_targets=${build_targets}"

# bazel --output_base="${output_base}" build ${build_options} ${build_targets}
bazel build ${build_options} ${build_targets}
bazel analyze-profile /tmp/profile

if [[ "${GENERATE_COMPILE_COMMANDS}" == "1" ]]; then
    bazel run //:refresh_compile_commands -- ${build_options}
fi

# show test output on failure
export CTEST_OUTPUT_ON_FAILURE=1
bazel test ${build_options} //...
