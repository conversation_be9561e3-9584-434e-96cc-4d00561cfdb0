<?xml version="1.0" encoding="UTF-8"?>

<project>
    <current_subproject>default</current_subproject>

    <!-- default project-->
    <subproject id="default">
        <module_name>lock_on_road</module_name>
        <optional_task></optional_task>
        <architecture>amd64</architecture>
        <version>1.0.159</version>
        <depends>
            <depend>deeproute-absl-dev (&gt;= 1.4.0),deeproute-absl-dev (&lt;&lt; 1.5.0)</depend>
            <depend>deeproute-grpc-dev (&gt;= 1.37.3),deeproute-grpc-dev (&lt;&lt; 1.38.0)</depend>
            <depend>deeproute-s2geometry-dev (&gt;= 1.1.0),deeproute-s2geometry-dev (&lt;&lt; 1.2.0)</depend>
            <!-- <depend>deeproute-fmm-2004-t2-dev (= 1.0.22)</depend> -->
            <depend>deeproute-protobuf-dev (= 3.13.0)</depend>
            <depend>deeproute-draco-dev (= 1.5.10)</depend>
            <depend>deeproute-inference-engine-dev (&gt;= 1.6.0), deeproute-inference-engine-dev (&lt;&lt; 1.7.0)</depend>
            <depend>deeproute-venom-dev (>= 1.2.5), deeproute-venom-dev (&lt;&lt; 1.3.0)</depend>
        </depends>
        <maintainers>
          <maintainer><EMAIL></maintainer>
        </maintainers>
        <priority>optional</priority>
        <breaks>
        </breaks>
        <description>DLA fix; output datatype</description>
    </subproject>

    <subproject id="ubuntu2004">
        <module_name>lock_on_road</module_name>
        <optional_task>2004dev</optional_task>
        <architecture>amd64</architecture>
        <version>1.0.159</version>
        <depends>
            <depend>deeproute-absl-dev (&gt;= 1.4.0),deeproute-absl-dev (&lt;&lt; 1.5.0)</depend>
            <depend>deeproute-grpc-dev (&gt;= 1.37.3),deeproute-grpc-dev (&lt;&lt; 1.38.0)</depend>
            <depend>deeproute-s2geometry-dev (&gt;= 1.1.0),deeproute-s2geometry-dev (&lt;&lt; 1.2.0)</depend>
            <!-- <depend>deeproute-fmm-2004-t2-dev (= 1.0.22)</depend> -->
            <depend>deeproute-protobuf-dev (= 3.13.0)</depend>
            <depend>deeproute-draco-dev (= 1.5.10)</depend>
            <depend>deeproute-inference-engine-dev (&gt;= 1.6.0), deeproute-inference-engine-dev (&lt;&lt; 1.7.0)</depend>
            <depend>deeproute-venom-dev (>= 1.2.5), deeproute-venom-dev (&lt;&lt; 1.3.0)</depend>
        </depends>
        <maintainers>
          <maintainer><EMAIL></maintainer>
        </maintainers>
        <priority>optional</priority>
        <breaks>
        </breaks>
        <description>adaptive YAW threshold adjustment based on traj heading diff when making Uturn</description>
    </subproject>

    <subproject id="orin">
        <module_name>lock_on_road</module_name>
        <optional_task>2004dev</optional_task>
        <architecture>amd64</architecture>
        <version>1.0.159</version>
        <depends>
            <depend>deeproute-absl-dev (&gt;= 1.4.0),deeproute-absl-dev (&lt;&lt; 1.5.0)</depend>
            <depend>deeproute-grpc-dev (&gt;= 1.37.3),deeproute-grpc-dev (&lt;&lt; 1.38.0)</depend>
            <depend>deeproute-s2geometry-dev (&gt;= 1.1.0),deeproute-s2geometry-dev (&lt;&lt; 1.2.0)</depend>
            <!-- <depend>deeproute-fmm-2004-t2-dev (= 1.0.22)</depend> -->
            <depend>deeproute-protobuf-dev (= 3.13.0)</depend>
            <depend>deeproute-draco-dev (= 1.5.10)</depend>
            <depend>deeproute-inference-engine-dev (&gt;= 1.6.0), deeproute-inference-engine-dev (&lt;&lt; 1.7.0)</depend>
            <depend>deeproute-venom-dev (>= 1.2.5), deeproute-venom-dev (&lt;&lt; 1.3.0)</depend>
        </depends>
        <maintainers>
          <maintainer><EMAIL></maintainer>
        </maintainers>
        <priority>optional</priority>
        <breaks>
        </breaks>
        <description>adaptive YAW threshold adjustment based on traj heading diff when making Uturn</description>
    </subproject>
</project>
