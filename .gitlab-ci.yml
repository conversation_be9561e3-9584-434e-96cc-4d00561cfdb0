stages:
    - build
    - release-deb
    - generate_unit_test
    - trigger_unit_test
    - generate_integration_test
    - trigger_integration_test

include:
    - project: 'deeproute-projects/cicd/cicd-common'
      ref: development
      file: 'global.gitlab-ci.yml'
    - project: 'deeproute-projects/cicd/cicd-common'
      ref: development
      file: '/deb-package/monorepo-package.gitlab-ci.yml'

variables:
    MONOREPO_MANIFEST_URL: "*********************:deeproute-projects/public/repo_manifest.git"
    MONOREPO_MANIFEST_BRANCH: "dev_master"
    PROJECT_MONOREPO_PATH: "lock-on-road"
    PACKAGE_NAME: "bazel-bin/lock_on_road_package_release.tar"
    S2I_FEATURE_FLAG: "mono_repo"
