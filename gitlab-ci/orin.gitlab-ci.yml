.rule_build: &rules_build
  - if: $CI_PIPELINE_SOURCE != "schedules"

orin-version-control:
  image: "${CI_BUILD_IMAGE}"
  variables:
    SUBPROJECT_ID: "orin"
    release_source: deeproute-release-2004
  tags:
    - arm-platform
  extends: .version-control
  rules: !reference [.rules_template, pipeline-for-version-control]
  needs:
    - arm-env-ubuntu20.04

orin-build:
  extends: .build-job
  # image: "${ORIN_IMAGE}"
  image: "${CI_BUILD_IMAGE}"
  tags:
    - arm-platform
  variables:
    MAKE_JOBS: 16
  rules:
    - if: $CI_PIPELINE_SOURCE == "api"
      when: manual
    - if: $CI_PIPELINE_SOURCE != "schedules"
  needs:
    - job: orin-version-control
      optional: true
      artifacts: false
    - job: arm-env-ubuntu20.04
      artifacts: true
  before_script:
    - !reference [.before_script, orin_before_script]
    - !reference [.before_script, apt_update]
    - source /etc/bash.bashrc
  script:
    - !reference [.build_script, build_script]
    - echo ${FULL_MODULE_VERSION}
  cache:
    key: ORIN-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: push

orin-unit-test:
  extends: .unit-test
  # image: "${ORIN_IMAGE}"
  image: "${CI_BUILD_IMAGE}"
  tags:
    - arm-platform
  variables:
    MAKE_JOBS: 16
  rules:
    - !reference [.rules_template, pipeline-for-unit-test]
  needs:
    - orin-build
    - job: arm-env-ubuntu20.04
      artifacts: true
  cache:
    key: ORIN-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull
  before_script:
    - !reference [.before_script, orin_before_script]
    - !reference [.build_script, get_make_result]
  script:
    - echo "test"

orin-snapshot-deb:
  extends: .snapshot-deb
  image: "${CI_BUILD_IMAGE}"
  tags:
    - arm-platform
  variables:
    SUBPROJECT_ID: orin # point to pipeline/config.xml, searching for subject that id equal "orin"
    snapshot_source: deeproute-snapshot-2004
    FILENAME_DEB: local-routing.deb
  rules:
    - !reference [.rules_template, pipeline-for-snapshot]
  needs:
    - orin-build
    - job: arm-env-ubuntu20.04
      artifacts: true
  before_script:
    - !reference [.build_script, get_make_result]
  cache:
    key: ORIN-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull

orin-release-deb:
  extends: .release-deb
  image: "${CI_BUILD_IMAGE}"
  tags:
    - arm-platform
  rules:
    - !reference [.rules_template, pipeline-for-release]
  variables:
    VERSION_CONTROL_JOB_NAME: orin-version-control
    SUBPROJECT_ID: orin # point to pipeline/config.xml, searching for subject that id equal "orin"
    release_source: deeproute-release-2004
    FILENAME_DEB: local-routing.deb
  needs:
    - orin-version-control
    - orin-build
    - job: arm-env-ubuntu20.04
      artifacts: true
  before_script:
    - !reference [.build_script, get_make_result]
    - cat pipeline/DEBIAN/control
  cache:
    key: ORIN-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull