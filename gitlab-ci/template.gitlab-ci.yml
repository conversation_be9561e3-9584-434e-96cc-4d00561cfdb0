.rules_template:
  # 触发规则
  pipeline-except-pushes-build:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG == null
      # when: manual

  pipeline-for-release:
    - if: $CI_COMMIT_TAG

  pipeline-for-snapshot:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_COMMIT_TAG == null

  pipeline-for-version-control:
    - if: $CI_COMMIT_TAG
      allow_failure: true

  pipeline-for-unit-test:
    - if: $CI_COMMIT_TAG
      when: manual
      allow_failure: true
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      when: manual
      allow_failure: true
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      allow_failure: true

# .make_script:
#   script:
#     - echo -e "\n\n\e[34m make \e[0m\n\n"
#     - export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
#     - if [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "18.04" ];then
#     - echo "Ubuntu1804"
#     - source /opt/ros/melodic/setup.bash
#     - elif [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "20.04" ];then
#     - echo "Ubuntu2004"
#     - export OS_INTERFACE_PACKAGE=deeproute-os-interface-u2004-test-2-dev=1.1.7
#     - source /opt/ros/noetic/setup.bash
#     - apt update && apt install ${COMMON_PROTOCOL_PACKAGE} ${OS_INTERFACE_PACKAGE} -y
#     - mkdir build && cd build && cmake .. && cmake ..
#     - make -j${MAKE_JOBS}
#     - make install
#     - cd ${CI_PROJECT_DIR}
#     - mkdir -p make_result
#     - cp -rf /opt/deeproute/lock_on_road make_result
#     - tar -cf make_result.tar make_result

.make_script:
  script:
    # - echo -e "\n\n\e[34m Running Make Script \e[0m\n\n"
    - export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
    - OS_VERSION=$(lsb_release -rs)

    - if [ "$OS_VERSION" = "18.04" ]; then
        echo "Detected Ubuntu 18.04";
        source /opt/ros/melodic/setup.bash;
      elif [ "$OS_VERSION" = "20.04" ]; then
        echo "Detected Ubuntu 20.04";
        export OS_INTERFACE_PACKAGE=deeproute-os-interface-u2004-test-2-dev=1.1.7;
        source /opt/ros/noetic/setup.bash;
        apt update && apt install ${COMMON_PROTOCOL_PACKAGE} ${OS_INTERFACE_PACKAGE} -y;
      else
        exit 1;
      fi

    - mkdir -p build
    - cd build

    - cmake ..
    - cmake ..
    - make -j${MAKE_JOBS}
    - make install
    - cd ${CI_PROJECT_DIR}
    - mkdir -p make_result
    - cp -rf /opt/deeproute/lock_on_road make_result
    - tar -cf make_result.tar make_result

  get_make_install_cache:
    - cd ${CI_PROJECT_DIR}
    - tar -xf make_result.tar
    # 在这里将make_result中编译结果移动到应该在的位置，也就是进行还原，
    # 因为在这里会用过cache 缓存make_result.tar
    - mkdir -p /opt/deeproute/
    - echo ${CI_PROJECT_DIR}/
    - mv make_result/lock_on_road /opt/deeproute/

.before_script:
  before_script:
    - eval $(ssh-agent -s)
    - echo "${SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add -
    - export LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH
    # - source /opt/ros/noetic/setup.bash
    - if [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "18.04" ];then
    - echo "Ubuntu1804"
    - source /opt/ros/melodic/setup.bash
    - elif [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "20.04" ];then
    - echo "Ubuntu2004"
    - source /opt/ros/noetic/setup.bash
    - fi

    # - apt update && apt install ${COMMON_PROTOCOL_PACKAGE} ${OS_INTERFACE_PACKAGE} -y
    # - apt install ${FMM_PACKAGE} -y
    # - apt install deeproute-absl-dev=1.3.1

  orin_before_script:
    - eval $(ssh-agent -s)
    - echo "${SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add -
    # - source /opt/ros/noetic/setup.bash
    - if [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "18.04" ];then
    - echo "Ubuntu1804"
    - source /opt/ros/melodic/setup.bash
    - elif [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "20.04" ];then
    - echo "Ubuntu2004"
    - source /opt/ros/noetic/setup.bash
    - fi

    - |
      if [[ -n ${DOWNSTREAM_COMMON_PROTOCOL} ]]; then
        echo "override COMMON_PROTOCOL_PACKAGE from downstream: ${DOWNSTREAM_COMMON_PROTOCOL}"
        export COMMON_PROTOCOL_PACKAGE=${DOWNSTREAM_COMMON_PROTOCOL}
      fi
    # - apt update && apt install  ${ORIN_OS_INTERFACE_PACKAGE} ${ORIN_DRACO_PACKAGE} ${ORIN_CCACHE_PACKAGE} ${ORIN_FMM_PACKAGE} -y
    # - apt install deeproute-absl-dev=1.3.1
    - source /etc/bash.bashrc

  apt_update:
    - curl -s https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | apt-key add -

  before_script_os:
    # - source /opt/ros/noetic/setup.bash
    - if [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "18.04" ];then
    - echo "Ubuntu1804"
    - source /opt/ros/melodic/setup.bash
    - elif [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "20.04" ];then
    - echo "Ubuntu2004"
    - source /opt/ros/noetic/setup.bash
    - fi

  before_script_chaos:
    - source /opt/deeproute/chaos/setup.bash

  before_script_ccache:
    - |
      if [[ ${USE_CCACHE} == "True" ]]; then
        echo -e "\n\n\e[34m deeproute-cache will be used. USE_CCACHE=${USE_CCACHE}\e[0m\n\n"
        source /etc/bash.bashrc
      else
        echo -e "\n\n\e[34m deeproute-cache will not be used. USE_CCACHE=${USE_CCACHE}\e[0m\n\n"
      fi

.install_script:
  script:
    - echo -e "\n\n\e[34m installer \e[0m\n\n"
  #    - 所有ros和chaos环境的安装操作写在这里

.build_script:
  build_script:
    - mkdir build && cd build && cmake .. && cmake ..
    - make -j${MAKE_JOBS}
    - make install
    - cd ${CI_PROJECT_DIR}
    - mkdir -p install_dir
    - cp -rf /opt/deeproute/lock_on_road install_dir
    - cp -rf ./build install_dir
    - tar -cf make_result.tar install_dir

  get_make_result:
    - tar -xf make_result.tar
    - mkdir -p /opt/deeproute
    - mv install_dir/lock_on_road /opt/deeproute
    - mv install_dir/build ${CI_PROJECT_DIR}/
    - rm -rf install_dir
    - rm -rf make_result.tar

.bazel_build_script:
  build_asan_script:
    - |
      bash pipeline/test.bash //... --config=asan
      echo "OK"
  test_script:
    - |
      bash pipeline/test.bash //...
  build_script:
    - |
      bash pipeline/build.bash
      mkdir -p /opt/deeproute
      cp bazel-bin/pipeline/lock_on_road.tar lock_on_road.tar
      tar -xf lock_on_road.tar -C /opt/deeproute
      echo "OK"
  coverage_build_script:
    - |
      bash pipeline/coverage.bash
      wget https://github.com/linux-test-project/lcov/releases/download/v1.16/lcov-1.16.tar.gz && tar -zxvf lcov-1.16.tar.gz
      mkdir -p coverage_result/
      lcov-1.16/bin/genhtml --branch-coverage --output genhtml "$(bazel info output_path)/_coverage/_coverage_report.dat" -o coverage_result/
  get_make_result:
    - mkdir -p /opt/deeproute
    - tar -xf lock_on_road.tar -C /opt/deeproute/
    - rm -rf lock_on_road.tar
    
.monorepo_dependency:
  script:
    - apt install ssh
    - !reference [.ssh-agent, script0]
    - !reference [.ssh-agent, script1]
    - sudo chown -hR ${USER}:${USER} /opt/deeproute/
    - sudo mkdir /pipeline && sudo chown -hR ${USER}:${USER} /pipeline
    - if [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "18.04" ];then
    - echo "Ubuntu1804"
    - source /opt/ros/melodic/setup.bash
    - elif [ "$(lsb_release -a 2>&1 | grep "Release" | awk '{print $2}')" = "20.04" ];then
    - echo "Ubuntu2004"
    - source /opt/ros/noetic/setup.bash
    -   if [ "$(uname -m)" = "x86_64" ];then
    -       echo "X86"
    -   elif [ "$(uname -m)" = "aarch64" ];then
    -       echo "aarch64"
    -       cd ${CI_PROJECT_DIR} && sed -i "s/amd64/arm64/g" pipeline/config.xml
    -   fi
    - fi
    - git submodule init
    - git submodule update --remote --merge

.bazel_version_script:
  debug_optional_task:
    - sed -i 's/<\/optional_task>/-debug-bazel<\/optional_task>/g' ${CI_PROJECT_DIR}/pipeline/config.xml
    - sed -i 's#<optional_task>null-debug-bazel</optional_task>#<optional_task>debug-bazel</optional_task>#g' ${CI_PROJECT_DIR}/pipeline/config.xml

  release_optional_task:
    - sed -i 's/<\/optional_task>/-release-bazel<\/optional_task>/g' ${CI_PROJECT_DIR}/pipeline/config.xml
    - sed -i 's#<optional_task>null-release-bazel</optional_task>#<optional_task>release-bazel</optional_task>#g' ${CI_PROJECT_DIR}/pipeline/config.xml