ros-version-control:
  extends: .version-control
  image: "${CI_BUILD_IMAGE}"
  rules:
    !reference [.rules_template, pipeline-for-version-control]
  needs:
    - x86-env-ubuntu18.04

ros-build:
  extends: .build-job
  image: "${CI_BUILD_IMAGE}"
  rules:
    !reference [.rules_template, pipeline-except-pushes-build]
  tags:
    - mapping
  before_script:
    - !reference [.configure-ssh-agent, ssh-keyscan]
    - !reference [.configure-ssh-agent, ssh-agent]
    - !reference [.install_script, script]
    - !reference [.before_script, before_script]
    - !reference [.before_script, before_script_os]
    - !reference [.before_script, before_script_ccache]
  script:
    - !reference [.make_script, script]
  cache:
    key: ROS-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: push
  needs:
    - job: ros-version-control
      optional: true
    - job: x86-env-ubuntu18.04
      artifacts: true

############################################## unit test ##################################################

ros-unit-test:
  extends: .unit-test
  image: "${CI_BUILD_IMAGE}"
  rules:
    !reference [.rules_template, pipeline-for-unit-test]
  needs:
    - ros-build
  cache:
    key: ROS-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull
  before_script:
    - !reference [.configure-ssh-agent, ssh-keyscan]
    - !reference [.configure-ssh-agent, ssh-agent]
    - !reference [.install_script, script]
    - !reference [.before_script, before_script]
    - !reference [.before_script, before_script_os]
    - !reference [.before_script, before_script_ccache]
    - !reference [.make_script, get_make_install_cache]
  needs:
    - job: x86-env-ubuntu18.04
      artifacts: true

########################################### make deb ##############################################

ros-snapshot-deb:
  extends: .snapshot-deb
  image: "${CI_BUILD_IMAGE}"
  rules:
    !reference [.rules_template, pipeline-for-snapshot]
  before_script:
    - !reference [.make_script, get_make_install_cache]
  # after_script:
  #   - !reference [ .export_module_version, script ]
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 3 days
  needs:
    - ros-build
    - job: x86-env-ubuntu18.04
      artifacts: true
  cache:
    key: ROS-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull

ros-release-deb:
  extends: .release-deb
  image: "${CI_BUILD_IMAGE}"
  rules:
    !reference [.rules_template, pipeline-for-release]
  variables:
    VERSION_CONTROL_JOB_NAME: ros-version-control
  cache:
    key: ROS-$CI_PIPELINE_ID
    paths:
      - make_result.tar
    policy: pull
  needs:
    - ros-version-control
    - ros-build
    - job: x86-env-ubuntu18.04
      artifacts: true
  before_script:
    - !reference [.make_script, get_make_install_cache]
    - cat pipeline/DEBIAN/control
    
#################################### sil-test #########################################

ros-sil-test:
  stage: sil-test
  rules:
    !reference [.rules_template, pipeline-for-release]
  variables:
    ADDITIONAL_VERSION: ${FULL_MODULE_VERSION}
    OS_PLATFORM: "ros"
  trigger:
    project: internal/integration_tests
    branch: integration_latest_stable_20220110
    strategy: depend
  needs:
    - job: ros-snapshot-deb
      artifacts: true

############################################ code-analysis ###################################################

code-analysis:
  extends: .code-analysis-clang-tidy
  image: "${CI_BUILD_IMAGE}"
  tags:
    - mapping
  variables:
      CLANG_TIDY_BUILD_PATH: ${CI_PROJECT_DIR}/build
  before_script:
      - !reference [.configure-ssh-agent, ssh-keyscan]
      - !reference [.configure-ssh-agent, ssh-agent]
      - !reference [.install_script, script]
      - !reference [.before_script, before_script]
      - !reference [.before_script, before_script_os]
      - !reference [.before_script, before_script_ccache]
      - mkdir build && cd build
      - cmake .. -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
      - make -j12
  rules:
    - if: $CI_COMMIT_TAG == /v[0-9]+\.[0-9]+\.[0-9]+$/
    - if: $CI_COMMIT_TAG == null
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual
  needs:
    - job: x86-env-ubuntu18.04
      artifacts: true