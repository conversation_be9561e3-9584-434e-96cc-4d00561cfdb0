workspace(name = "lock_on_road")

load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository")

local_repository(
    name = "deeproute_build_tools",
    path = "../bazel_configs",
)

local_repository(
    name = "third_party",
    path = "../third_party",
)

load("//:deps.bzl", "lane_based_localization_benchmark_deps")
lane_based_localization_benchmark_deps()

load("@third_party//:dr_third_party.bzl", "declare_dr_third_party_repositories")
declare_dr_third_party_repositories("../")
load("@third_party//:dr_third_party_extra.bzl", "declare_dr_third_party_repositories_extra")
declare_dr_third_party_repositories_extra()

# GRPC
load("@com_github_grpc_grpc//bazel:grpc_deps.bzl", "grpc_deps")
grpc_deps()

load("@com_github_grpc_grpc//bazel:grpc_extra_deps.bzl", "grpc_extra_deps")
grpc_extra_deps()
