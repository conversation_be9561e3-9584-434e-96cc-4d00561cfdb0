load("@deeproute_build_tools//:globals.bzl", "global_definitions")
load("@deeproute_build_tools//rules/config_settings:config_setting.bzl", "deeproute_runtime", "deeproute_ubuntu_version", "deeproute_vehicle")
load("@deeproute_build_tools//rules/pkg:deeproute_release.bzl", "deeproute_release_package")
load("@deeproute_build_tools//toolchains:defs.bzl", "deeproute_register_toolchains")
load("@hedron_compile_commands//:refresh_compile_commands.bzl", "refresh_compile_commands")

package(default_visibility = ["//visibility:public"])

global_definitions()

deeproute_register_toolchains()

deeproute_runtime(name = "deeproute_runtime")

deeproute_vehicle(
    name = "deeproute_vehicle",
    has_default_value = True,
)

deeproute_ubuntu_version(
    name = "deeproute_ubuntu_version",
    has_default_value = True,
)

exports_files([
    "CPPLINT.cfg",
])

deeproute_release_package(
    name = "lock_on_road_package_release",
    srcs = ["//pipeline:setup.bash"],
    mode = "0755",
    package_dir = "lock_on_road",
    deps = [
        "//pipeline:bin",
    ],
)

refresh_compile_commands(
    name = "refresh_compile_commands",

    # Specify the targets of interest.
    # For example, specify a dict of targets and any flags required to build.
    targets = {
        "//...": "",
    },
    # No need to add flags already in .bazelrc. They're automatically picked up.
    # If you don't need flags, a list of targets is also okay, as is a single target string.
    # Wildcard patterns, like //... for everything, *are* allowed here, just like a build.
    # As are additional targets (+) and subtractions (-), like in bazel query https://docs.bazel.build/versions/main/query.html#expressions
    # And if you're working on a header-only library, specify a test or binary target that compiles it.
)
