package(default_visibility = ["//visibility:public"], features=["-hidden_visibility_feature"])
load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

STRIP_IMPORT_PREFIX = "/"
proto_library(
    name = "config_region_proto",
    srcs = [
        "config_region.proto",
    ],
    strip_import_prefix = STRIP_IMPORT_PREFIX,
    deps = [
        "@lam_common//lam_common/proto/lam_common:projection_proto",
    ],
)

cc_proto_library(
    name = "config_region_cc_proto",
    deps = [":config_region_proto"],
)

#struct_proto_library(
#   name = "config_region_struct_proto",
#    srcs = [
#        "config_region.proto",
#    ],
#    deps = [
#        "@lam_common//lam_common:projection_struct_proto",
#    ],
#)

