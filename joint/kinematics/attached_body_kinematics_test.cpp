#include "joint/kinematics/attached_body_kinematics.h"

#include <gtest/gtest.h>
#include <third_party/eigen_checks/gtest.h>

namespace deeproute {
namespace kinematics {

using ::common::Transformation3;
const double kEps = 1e-3;

TEST(AttchedBodyFrameVelocityTest, HasExpectedImuFrameVelocity) {
  Vector6d vehicle_frame_velocity;
  vehicle_frame_velocity << 1.0, 0.0, 0.0, 5.0, 0.0, 0.0;
  Transformation3 vehicle_to_imu_transform(1.0, 0.0, 0.0, 0, 0, 0);

  Vector6d imu_frame_velocity;
  Matrix6d jacobian;
  AttachedBodyFrameVelocity(vehicle_frame_velocity, vehicle_to_imu_transform,
                           &imu_frame_velocity, &jacobian);

  EXPECT_TRUE(
      EIGEN_MATRIX_NEAR(imu_frame_velocity, vehicle_frame_velocity, kEps));
  Matrix6d expected_jacobian = Matrix6d::Identity();
  expected_jacobian(4, 2) = 1.0;
  expected_jacobian(5, 1) = -1.0;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(jacobian, expected_jacobian, kEps));
}

TEST(AttchedBodyFrameAccelerationTest, HasExpectedImuFrameAcceleration) {
  Vector6d vehicle_frame_velocity;
  vehicle_frame_velocity << 1.0, 0.0, 0.0, 5.0, 0.0, 0.0;
  Vector6d vehicle_frame_acceleration;
  vehicle_frame_acceleration << 1.0, 0.0, 0.0, 1.0, 0.0, 0.0;

  Transformation3 vehicle_to_imu_transform(1.0, 0.0, 0.0, 0, 0, 0);

  Vector6d imu_frame_acceleration;
  Matrix6d jacobian_wrt_vehicle_frame_velocity;
  Matrix6d jacobian_wrt_vehicle_frame_acceleration;
  AttachedBodyFrameAcceleration(
      vehicle_frame_velocity, vehicle_frame_acceleration,
      vehicle_to_imu_transform, &imu_frame_acceleration,
      &jacobian_wrt_vehicle_frame_velocity,
      &jacobian_wrt_vehicle_frame_acceleration);

  // All expected values are calculated by the corresponding equations from the
  // document.
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(imu_frame_acceleration,
                                vehicle_frame_acceleration, kEps));
  Matrix6d expected_jacobian_wrt_vehicle_frame_velocity = Matrix6d::Zero();
  expected_jacobian_wrt_vehicle_frame_velocity(4, 1) = 1.0;
  expected_jacobian_wrt_vehicle_frame_velocity(5, 2) = 1.0;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(jacobian_wrt_vehicle_frame_velocity,
                                expected_jacobian_wrt_vehicle_frame_velocity,
                                kEps));
  Matrix6d expected_jacobian_wrt_vehicle_frame_acceleration =
      Matrix6d::Identity();
  expected_jacobian_wrt_vehicle_frame_acceleration(4, 2) = 1.0;
  expected_jacobian_wrt_vehicle_frame_acceleration(5, 1) = -1.0;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(
      jacobian_wrt_vehicle_frame_acceleration,
      expected_jacobian_wrt_vehicle_frame_acceleration, kEps));
}

}  // namespace kinematics
}  // namespace deeproute
