package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")

cc_library(
  name = "attached_body_kinematics",
  hdrs = ["attached_body_kinematics.h"],
  srcs = ["attached_body_kinematics.cpp"],
  deps = [
        ":algebra",
        "@lam_common//lam_common:types",
        "@common//transform:transformation",
  ],
)

cc_library(
  name = "algebra",
  hdrs = ["algebra.h"],
  srcs = [],
  deps = [
        "@lam_common//lam_common:types"
  ],
)