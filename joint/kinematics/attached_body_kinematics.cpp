#include "joint/kinematics/attached_body_kinematics.h"

#include "joint/kinematics/algebra.h"

namespace deeproute {
namespace kinematics {

void AttachedBodyFrameVelocity(
    const Vector6d& vehicle_frame_velocity,
    const ::common::Transformation3& vehicle_to_imu_transform,
    Vector6d* imu_frame_velocity,
    Matrix6d* partial_imu_frame_velocity_partial_vehicle_frame_velocity) {
  // First, calculate IMU velocity in vehicle frame.
  // Rigidly attached body has the same angular speed.
  imu_frame_velocity->head<3>() = vehicle_frame_velocity.head<3>();
  // v_imu = v_vehicle + v_rotation
  // v_rotation = w x translation = - translation x w
  const Matrix3d hat_translation =
      crossMatrix(vehicle_to_imu_transform.GetTranslation());
  imu_frame_velocity->tail<3>() =
      vehicle_frame_velocity.tail<3>() -
      hat_translation * vehicle_frame_velocity.head<3>();

  if (partial_imu_frame_velocity_partial_vehicle_frame_velocity != nullptr) {
    partial_imu_frame_velocity_partial_vehicle_frame_velocity->setIdentity();
    partial_imu_frame_velocity_partial_vehicle_frame_velocity->block<3, 3>(
        3, 0) = -hat_translation;
  }

  // Secondly, update IMU velocity to IMU frame.
  // This is just a frame change, so the velocity coordinates should be applied
  // with the multiplication of an inverse frame relative rotation.
  const Matrix3d inverse_rotation =
      vehicle_to_imu_transform.GetRotationMatrix().transpose();
  Matrix6d frame_change_jacobian;
  frame_change_jacobian.setZero();
  frame_change_jacobian.block<3, 3>(0, 0) = inverse_rotation;
  frame_change_jacobian.block<3, 3>(3, 3) = inverse_rotation;
  *imu_frame_velocity = frame_change_jacobian * *imu_frame_velocity;

  if (partial_imu_frame_velocity_partial_vehicle_frame_velocity != nullptr) {
    *partial_imu_frame_velocity_partial_vehicle_frame_velocity =
        frame_change_jacobian *
        *partial_imu_frame_velocity_partial_vehicle_frame_velocity;
  }
}

void AttachedBodyFrameAcceleration(
    const Vector6d& vehicle_frame_velocity,
    const Vector6d& vehicle_frame_acceleration,
    const ::common::Transformation3& vehicle_to_imu_transform,
    Vector6d* imu_frame_acceleration,
    Matrix6d* partial_imu_frame_acceleration_partial_vehicle_frame_velocity,
    Matrix6d*
        partial_imu_frame_acceleration_partial_vehicle_frame_acceleration) {
  // First, calculate IMU acceleration in vehicle frame.
  // Rigidly attached body has the same angular acceleration.
  imu_frame_acceleration->head<3>() = vehicle_frame_acceleration.head<3>();

  // This is eq. 6 of
  // http://confluence.deeproute.ai/pages/viewpage.action?pageId=7831854,
  // excluding the gravity and bias part.
  const Matrix3d hat_angular_velocity =
      crossMatrix(Vector3d(vehicle_frame_velocity.head<3>()));
  const Matrix3d hat_angular_acceleration =
      crossMatrix(Vector3d(vehicle_frame_acceleration.head<3>()));
  const Vector3d translation = vehicle_to_imu_transform.GetTranslation();
  imu_frame_acceleration->tail<3>() =
      vehicle_frame_acceleration.tail<3>() +
      hat_angular_acceleration * translation +
      hat_angular_velocity * hat_angular_velocity * translation;

  if (partial_imu_frame_acceleration_partial_vehicle_frame_velocity !=
      nullptr) {
    // The linear velocity has no effect on IMU acceleration, according to
    // above two equations.
    partial_imu_frame_acceleration_partial_vehicle_frame_velocity->setZero();

    // The jacobian of IMU acceleration w.r.t. angular velocity. This is eq. 15.
    const Vector3d tmp = hat_angular_velocity * translation;
    partial_imu_frame_acceleration_partial_vehicle_frame_velocity->block<3, 3>(
        3, 0) =
        -crossMatrix(tmp) - hat_angular_velocity * crossMatrix(translation);
  }
  if (partial_imu_frame_acceleration_partial_vehicle_frame_acceleration !=
      nullptr) {
    // This can be seen from the two summary equations for h_w and h_a in the
    // document.
    partial_imu_frame_acceleration_partial_vehicle_frame_acceleration
        ->setIdentity();
    partial_imu_frame_acceleration_partial_vehicle_frame_acceleration
        ->block<3, 3>(3, 0) -= crossMatrix(translation);
  }

  // Secondly, update IMU acceleration to IMU frame.
  // This is just a frame change, so the acceleration coordinates should be
  // applied with the multiplication of an inverse frame relative rotation.
  const Matrix3d inverse_rotation =
      vehicle_to_imu_transform.GetRotationMatrix().transpose();
  Matrix6d frame_change_jacobian;
  frame_change_jacobian.setZero();
  frame_change_jacobian.block<3, 3>(0, 0) = inverse_rotation;
  frame_change_jacobian.block<3, 3>(3, 3) = inverse_rotation;
  *imu_frame_acceleration = frame_change_jacobian * *imu_frame_acceleration;

  if (partial_imu_frame_acceleration_partial_vehicle_frame_velocity !=
      nullptr) {
    *partial_imu_frame_acceleration_partial_vehicle_frame_velocity =
        frame_change_jacobian *
        *partial_imu_frame_acceleration_partial_vehicle_frame_velocity;
  }
  if (partial_imu_frame_acceleration_partial_vehicle_frame_acceleration !=
      nullptr) {
    *partial_imu_frame_acceleration_partial_vehicle_frame_acceleration =
        frame_change_jacobian *
        *partial_imu_frame_acceleration_partial_vehicle_frame_acceleration;
  }
}

}  // namespace kinematics
}  // namespace deeproute
