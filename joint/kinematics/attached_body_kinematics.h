#pragma once

#include "lam_common/types.h"
#include "transform/transformation.h"

namespace deeproute {
namespace kinematics {

// These functions are used to compute the velocity/acceleration on rigidly
// attached body frame, given the velocity/acceleration of the main body and the
// attachment configuration.

// For our system, a use case is to predict the IMU observation at the IMU
// frame, from the internal EKF states at vehicle frame. The implementation
// names variables as `vehicle_frame` to refer to main body frame. And
// `imu_frame` refers to attached body frame.
// The detail derivation is documented at
// http://confluence.deeproute.ai/pages/viewpage.action?pageId=7831854

// Computes attached body frame velocity, given vehicle frame velocity.
void AttachedBodyFrameVelocity(
    const Vector6d& vehicle_frame_velocity,
    const ::common::Transformation3& vehicle_to_imu_transform,
    Vector6d* imu_frame_velocity,
    Matrix6d* partial_imu_frame_velocity_partial_vehicle_frame_velocity =
        nullptr);

// Computes attached body frame acceleration, given vehicle frame velocity and
// acceleration.
void AttachedBodyFrameAcceleration(
    const Vector6d& vehicle_frame_velocity,
    const Vector6d& vehicle_frame_acceleration,
    const ::common::Transformation3& vehicle_to_imu_transform,
    Vector6d* imu_frame_acceleration,
    Matrix6d* partial_imu_frame_acceleration_partial_vehicle_frame_velocity =
        nullptr,
    Matrix6d*
        partial_imu_frame_acceleration_partial_vehicle_frame_acceleration =
            nullptr);

}  // namespace kinematics
}  // namespace deeproute
