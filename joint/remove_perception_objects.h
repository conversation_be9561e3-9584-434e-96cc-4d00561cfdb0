#pragma once

#include <string>
#include <vector>

#include "perception/deeproute_perception_obstacle.pb.h"

#include "common/point.h"
#include "transform/transformation.h"

namespace deeproute {

// Returns whether `obstacle` is a moving one.
bool IsMovingPerceptionObstacle(const perception::PerceptionObstacle& obstacle);

// Returns a vector indicating whether each point in `point_cloud` belongs to
// `obstacles`.
template <class PointT>
std::vector<bool> MarkPerceptionObjects(
    const ::common::Transformation3& sensing_to_map,
    const perception::PerceptionObstacles& obstacles,
    const std::vector<PointT, Eigen::aligned_allocator<PointT>>& points,
    double bounding_box_buffer = 1.0);

template <typename PointT>
std::vector<bool> MarkPointsInBoundingBox(
    const std::vector<PointT, Eigen::aligned_allocator<PointT>>& points,
    const Eigen::Vector3d& bounding_box_center,
    const Eigen::Vector3d& bounding_box_size, double bounding_box_heading,
    double bounding_box_buffer = 0);

}  // namespace deeproute
