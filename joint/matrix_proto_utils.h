#pragma once

#include "lam_common/matrix.pb.h"

#include "lam_common/types.h"

namespace deeproute {

Vector3d ProtoToVector3d(const proto::Vector3d& vector_proto);

Matrix3d ProtoToMatrix3d(const proto::Matrix3d& matrix_proto);

Matrix6d ProtoToMatrix6d(const proto::Matrix6d& matrix_proto);

proto::Vector3d Vector3dToProto(const Vector3d& vector);

proto::Matrix3d Matrix3dToProto(const Matrix3d& matrix);

proto::Matrix6d Matrix6dToProto(const Matrix6d& matrix);
}  // namespace deeproute
