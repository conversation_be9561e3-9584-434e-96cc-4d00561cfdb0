float32[3] position

float32[3] roll_pitch_yaw

float32[3] velocity_flu  # in m/s

float32[3] angular_velocity_flu  # in rad/s

float32[3] acceleration_flu  # in m/s^2

float32[6] imu_bias 

float32[2] wheel_radius_rear

float32[3] gnss_bias

float32[3] imu_measurement_acceleration_flu

float32[3] imu_measurement_angular_velocity_flu

float32[3] position_std

float32[3] so3_std

float32[3] velocity_std

float32[3] angular_velocity_std

float32[3] acceleration_std

float32[3] angular_acceleration_std

float32[6] imu_bias_std

float32[2] wheel_radius_std

float32[3] gnss_bias_std
