// Generated by gencpp from file joint/DebugPublish.msg
// DO NOT EDIT!

// Note(<PERSON>): this file is edited in two place to support chaos
//   1. Line 16-19 is modified to use header files from os_interface
//   2. Line 213-277 is newly added to support chaos message traits

#ifndef JOINT_MESSAGE_DEBUGPUBLISH_H
#define JOINT_MESSAGE_DEBUGPUBLISH_H

#include <string>
#include <vector>
#include <map>

#include "os_interface/ros_dependency/builtin_message_traits.h"
#include "os_interface/ros_dependency/message_operations.h"
#include "os_interface/ros_dependency/serialization.h"
#include "os_interface/ros_dependency/types.h"

namespace joint {
template <class ContainerAllocator>
struct DebugPublish_ {
  typedef DebugPublish_<ContainerAllocator> Type;

  DebugPublish_()
      : position(),
        roll_pitch_yaw(),
        velocity_flu(),
        angular_velocity_flu(),
        acceleration_flu(),
        imu_bias(),
        wheel_radius_rear(),
        gnss_bias(),
        imu_measurement_acceleration_flu(),
        imu_measurement_angular_velocity_flu(),
        position_std(),
        so3_std(),
        velocity_std(),
        angular_velocity_std(),
        acceleration_std(),
        angular_acceleration_std(),
        imu_bias_std(),
        wheel_radius_std(),
        gnss_bias_std() {
    position.assign(0.0);

    roll_pitch_yaw.assign(0.0);

    velocity_flu.assign(0.0);

    angular_velocity_flu.assign(0.0);

    acceleration_flu.assign(0.0);

    imu_bias.assign(0.0);

    wheel_radius_rear.assign(0.0);

    gnss_bias.assign(0.0);

    imu_measurement_acceleration_flu.assign(0.0);

    imu_measurement_angular_velocity_flu.assign(0.0);

    position_std.assign(0.0);

    so3_std.assign(0.0);

    velocity_std.assign(0.0);

    angular_velocity_std.assign(0.0);

    acceleration_std.assign(0.0);

    angular_acceleration_std.assign(0.0);

    imu_bias_std.assign(0.0);

    wheel_radius_std.assign(0.0);

    gnss_bias_std.assign(0.0);
  }
  DebugPublish_(const ContainerAllocator& _alloc)
      : position(),
        roll_pitch_yaw(),
        velocity_flu(),
        angular_velocity_flu(),
        acceleration_flu(),
        imu_bias(),
        wheel_radius_rear(),
        gnss_bias(),
        imu_measurement_acceleration_flu(),
        imu_measurement_angular_velocity_flu(),
        position_std(),
        so3_std(),
        velocity_std(),
        angular_velocity_std(),
        acceleration_std(),
        angular_acceleration_std(),
        imu_bias_std(),
        wheel_radius_std(),
        gnss_bias_std() {
    (void)_alloc;
    position.assign(0.0);

    roll_pitch_yaw.assign(0.0);

    velocity_flu.assign(0.0);

    angular_velocity_flu.assign(0.0);

    acceleration_flu.assign(0.0);

    imu_bias.assign(0.0);

    wheel_radius_rear.assign(0.0);

    gnss_bias.assign(0.0);

    imu_measurement_acceleration_flu.assign(0.0);

    imu_measurement_angular_velocity_flu.assign(0.0);

    position_std.assign(0.0);

    so3_std.assign(0.0);

    velocity_std.assign(0.0);

    angular_velocity_std.assign(0.0);

    acceleration_std.assign(0.0);

    angular_acceleration_std.assign(0.0);

    imu_bias_std.assign(0.0);

    wheel_radius_std.assign(0.0);

    gnss_bias_std.assign(0.0);
  }

  typedef boost::array<float, 3> _position_type;
  _position_type position;

  typedef boost::array<float, 3> _roll_pitch_yaw_type;
  _roll_pitch_yaw_type roll_pitch_yaw;

  typedef boost::array<float, 3> _velocity_flu_type;
  _velocity_flu_type velocity_flu;

  typedef boost::array<float, 3> _angular_velocity_flu_type;
  _angular_velocity_flu_type angular_velocity_flu;

  typedef boost::array<float, 3> _acceleration_flu_type;
  _acceleration_flu_type acceleration_flu;

  typedef boost::array<float, 6> _imu_bias_type;
  _imu_bias_type imu_bias;

  typedef boost::array<float, 2> _wheel_radius_rear_type;
  _wheel_radius_rear_type wheel_radius_rear;

  typedef boost::array<float, 3> _gnss_bias_type;
  _gnss_bias_type gnss_bias;

  typedef boost::array<float, 3> _imu_measurement_acceleration_flu_type;
  _imu_measurement_acceleration_flu_type imu_measurement_acceleration_flu;

  typedef boost::array<float, 3> _imu_measurement_angular_velocity_flu_type;
  _imu_measurement_angular_velocity_flu_type
      imu_measurement_angular_velocity_flu;

  typedef boost::array<float, 3> _position_std_type;
  _position_std_type position_std;

  typedef boost::array<float, 3> _so3_std_type;
  _so3_std_type so3_std;

  typedef boost::array<float, 3> _velocity_std_type;
  _velocity_std_type velocity_std;

  typedef boost::array<float, 3> _angular_velocity_std_type;
  _angular_velocity_std_type angular_velocity_std;

  typedef boost::array<float, 3> _acceleration_std_type;
  _acceleration_std_type acceleration_std;

  typedef boost::array<float, 3> _angular_acceleration_std_type;
  _angular_acceleration_std_type angular_acceleration_std;

  typedef boost::array<float, 6> _imu_bias_std_type;
  _imu_bias_std_type imu_bias_std;

  typedef boost::array<float, 2> _wheel_radius_std_type;
  _wheel_radius_std_type wheel_radius_std;

  typedef boost::array<float, 3> _gnss_bias_std_type;
  _gnss_bias_std_type gnss_bias_std;

  typedef boost::shared_ptr<::joint::DebugPublish_<ContainerAllocator>> Ptr;
  typedef boost::shared_ptr<::joint::DebugPublish_<ContainerAllocator> const>
      ConstPtr;

  // support chaos message traits
#ifndef BUILD_WITH_ROS
  static void GetDescriptorString(const std::string&, std::string* desc_str) {
    *desc_str = "joint.DebugPublish.Descriptor";
  }

  bool SerializeToArray(void* data, int size) const {
    int serialize_length = ByteSizeLong();
    if (data == nullptr || size < serialize_length) {
      return false;
    }
    uint8_t* buf = reinterpret_cast<uint8_t*>(data);
    ros::serialization::OStream out(buf, size);
    ros::serialization::serialize(out, *this);
    return true;
  }

  bool SerializeToString(std::string* str) const {
    int serialize_length = ByteSizeLong();
    if (str == nullptr) {
      return false;
    }
    uint8_t buf[serialize_length];
    ros::serialization::OStream out(buf, serialize_length);
    ros::serialization::serialize(out, *this);
    *str = std::string(buf, buf + serialize_length);
    return true;
  }

  bool ParseFromArray(const void* data, int size) {
    if (data == nullptr || size < static_cast<int>(ByteSizeLong())) {
      return false;
    }
    ros::serialization::IStream in((uint8_t*)(data), size);
    ros::serialization::deserialize(in, *this);
    return true;
  }

  bool ParseFromString(const std::string& str) {
    if (str.size() < ByteSizeLong()) {
      return false;
    }
    std::vector<uint8_t> buffer(str.begin(), str.end());
    ros::serialization::IStream in(buffer.data(), static_cast<int>(str.size()));
    ros::serialization::deserialize(in, *this);
    return true;
  }

  std::size_t ByteSizeLong() const {
    return ros::serialization::Serializer<
        ::joint::DebugPublish_<ContainerAllocator>>::serializedLength(*this);
  }

  static std::string TypeName() { return "joint.DebugPublish"; }

  struct Descriptor {
    std::string full_name() const { return "joint.DebugPublish"; }
  };

  static const Descriptor* descriptor() {
    static Descriptor desc;
    return &desc;
  }
#endif

};  // struct DebugPublish_

typedef ::joint::DebugPublish_<std::allocator<void>> DebugPublish;

typedef boost::shared_ptr<::joint::DebugPublish> DebugPublishPtr;
typedef boost::shared_ptr<::joint::DebugPublish const> DebugPublishConstPtr;

// constants requiring out of line definition

template <typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s,
                         const ::joint::DebugPublish_<ContainerAllocator>& v) {
  ros::message_operations::Printer<
      ::joint::DebugPublish_<ContainerAllocator>>::stream(s, "", v);
  return s;
}

template <typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::joint::DebugPublish_<ContainerAllocator1>& lhs,
                const ::joint::DebugPublish_<ContainerAllocator2>& rhs) {
  return lhs.position == rhs.position &&
         lhs.roll_pitch_yaw == rhs.roll_pitch_yaw &&
         lhs.velocity_flu == rhs.velocity_flu &&
         lhs.angular_velocity_flu == rhs.angular_velocity_flu &&
         lhs.acceleration_flu == rhs.acceleration_flu &&
         lhs.imu_bias == rhs.imu_bias &&
         lhs.wheel_radius_rear == rhs.wheel_radius_rear &&
         lhs.gnss_bias == rhs.gnss_bias &&
         lhs.imu_measurement_acceleration_flu ==
             rhs.imu_measurement_acceleration_flu &&
         lhs.imu_measurement_angular_velocity_flu ==
             rhs.imu_measurement_angular_velocity_flu &&
         lhs.position_std == rhs.position_std && lhs.so3_std == rhs.so3_std &&
         lhs.velocity_std == rhs.velocity_std &&
         lhs.angular_velocity_std == rhs.angular_velocity_std &&
         lhs.acceleration_std == rhs.acceleration_std &&
         lhs.angular_acceleration_std == rhs.angular_acceleration_std &&
         lhs.imu_bias_std == rhs.imu_bias_std &&
         lhs.wheel_radius_std == rhs.wheel_radius_std &&
         lhs.gnss_bias_std == rhs.gnss_bias_std;
}

template <typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::joint::DebugPublish_<ContainerAllocator1>& lhs,
                const ::joint::DebugPublish_<ContainerAllocator2>& rhs) {
  return !(lhs == rhs);
}

}  // namespace joint

namespace ros {
namespace message_traits {

template <class ContainerAllocator>
struct IsFixedSize<::joint::DebugPublish_<ContainerAllocator>> : TrueType {};

template <class ContainerAllocator>
struct IsFixedSize<::joint::DebugPublish_<ContainerAllocator> const>
    : TrueType {};

template <class ContainerAllocator>
struct IsMessage<::joint::DebugPublish_<ContainerAllocator>> : TrueType {};

template <class ContainerAllocator>
struct IsMessage<::joint::DebugPublish_<ContainerAllocator> const> : TrueType {
};

template <class ContainerAllocator>
struct HasHeader<::joint::DebugPublish_<ContainerAllocator>> : FalseType {};

template <class ContainerAllocator>
struct HasHeader<::joint::DebugPublish_<ContainerAllocator> const> : FalseType {
};

template <class ContainerAllocator>
struct MD5Sum<::joint::DebugPublish_<ContainerAllocator>> {
  static const char* value() { return "d11f33c415b467406030c955f87c0ade"; }

  static const char* value(const ::joint::DebugPublish_<ContainerAllocator>&) {
    return value();
  }
  static const uint64_t static_value1 = 0xd11f33c415b46740ULL;
  static const uint64_t static_value2 = 0x6030c955f87c0adeULL;
};

template <class ContainerAllocator>
struct DataType<::joint::DebugPublish_<ContainerAllocator>> {
  static const char* value() { return "joint/DebugPublish"; }

  static const char* value(const ::joint::DebugPublish_<ContainerAllocator>&) {
    return value();
  }
};

template <class ContainerAllocator>
struct Definition<::joint::DebugPublish_<ContainerAllocator>> {
  static const char* value() {
    return "float32[3] position\n"
           "\n"
           "float32[3] roll_pitch_yaw\n"
           "\n"
           "float32[3] velocity_flu  # in m/s\n"
           "\n"
           "float32[3] angular_velocity_flu  # in rad/s\n"
           "\n"
           "float32[3] acceleration_flu  # in m/s^2\n"
           "\n"
           "float32[6] imu_bias \n"
           "\n"
           "float32[2] wheel_radius_rear\n"
           "\n"
           "float32[3] gnss_bias\n"
           "\n"
           "float32[3] imu_measurement_acceleration_flu\n"
           "\n"
           "float32[3] imu_measurement_angular_velocity_flu\n"
           "\n"
           "float32[3] position_std\n"
           "\n"
           "float32[3] so3_std\n"
           "\n"
           "float32[3] velocity_std\n"
           "\n"
           "float32[3] angular_velocity_std\n"
           "\n"
           "float32[3] acceleration_std\n"
           "\n"
           "float32[3] angular_acceleration_std\n"
           "\n"
           "float32[6] imu_bias_std\n"
           "\n"
           "float32[2] wheel_radius_std\n"
           "\n"
           "float32[3] gnss_bias_std\n";
  }

  static const char* value(const ::joint::DebugPublish_<ContainerAllocator>&) {
    return value();
  }
};

}  // namespace message_traits
}  // namespace ros

namespace ros {
namespace serialization {

template <class ContainerAllocator>
struct Serializer<::joint::DebugPublish_<ContainerAllocator>> {
  template <typename Stream, typename T>
  inline static void allInOne(Stream& stream, T m) {
    stream.next(m.position);
    stream.next(m.roll_pitch_yaw);
    stream.next(m.velocity_flu);
    stream.next(m.angular_velocity_flu);
    stream.next(m.acceleration_flu);
    stream.next(m.imu_bias);
    stream.next(m.wheel_radius_rear);
    stream.next(m.gnss_bias);
    stream.next(m.imu_measurement_acceleration_flu);
    stream.next(m.imu_measurement_angular_velocity_flu);
    stream.next(m.position_std);
    stream.next(m.so3_std);
    stream.next(m.velocity_std);
    stream.next(m.angular_velocity_std);
    stream.next(m.acceleration_std);
    stream.next(m.angular_acceleration_std);
    stream.next(m.imu_bias_std);
    stream.next(m.wheel_radius_std);
    stream.next(m.gnss_bias_std);
  }

  ROS_DECLARE_ALLINONE_SERIALIZER
};  // struct DebugPublish_

}  // namespace serialization
}  // namespace ros

namespace ros {
namespace message_operations {

template <class ContainerAllocator>
struct Printer<::joint::DebugPublish_<ContainerAllocator>> {
  template <typename Stream>
  static void stream(Stream& s, const std::string& indent,
                     const ::joint::DebugPublish_<ContainerAllocator>& v) {
    s << indent << "position[]" << std::endl;
    for (size_t i = 0; i < v.position.size(); ++i) {
      s << indent << "  position[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.position[i]);
    }
    s << indent << "roll_pitch_yaw[]" << std::endl;
    for (size_t i = 0; i < v.roll_pitch_yaw.size(); ++i) {
      s << indent << "  roll_pitch_yaw[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.roll_pitch_yaw[i]);
    }
    s << indent << "velocity_flu[]" << std::endl;
    for (size_t i = 0; i < v.velocity_flu.size(); ++i) {
      s << indent << "  velocity_flu[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.velocity_flu[i]);
    }
    s << indent << "angular_velocity_flu[]" << std::endl;
    for (size_t i = 0; i < v.angular_velocity_flu.size(); ++i) {
      s << indent << "  angular_velocity_flu[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.angular_velocity_flu[i]);
    }
    s << indent << "acceleration_flu[]" << std::endl;
    for (size_t i = 0; i < v.acceleration_flu.size(); ++i) {
      s << indent << "  acceleration_flu[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.acceleration_flu[i]);
    }
    s << indent << "imu_bias[]" << std::endl;
    for (size_t i = 0; i < v.imu_bias.size(); ++i) {
      s << indent << "  imu_bias[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.imu_bias[i]);
    }
    s << indent << "wheel_radius_rear[]" << std::endl;
    for (size_t i = 0; i < v.wheel_radius_rear.size(); ++i) {
      s << indent << "  wheel_radius_rear[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.wheel_radius_rear[i]);
    }
    s << indent << "gnss_bias[]" << std::endl;
    for (size_t i = 0; i < v.gnss_bias.size(); ++i) {
      s << indent << "  gnss_bias[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.gnss_bias[i]);
    }
    s << indent << "imu_measurement_acceleration_flu[]" << std::endl;
    for (size_t i = 0; i < v.imu_measurement_acceleration_flu.size(); ++i) {
      s << indent << "  imu_measurement_acceleration_flu[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ",
                             v.imu_measurement_acceleration_flu[i]);
    }
    s << indent << "imu_measurement_angular_velocity_flu[]" << std::endl;
    for (size_t i = 0; i < v.imu_measurement_angular_velocity_flu.size(); ++i) {
      s << indent << "  imu_measurement_angular_velocity_flu[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ",
                             v.imu_measurement_angular_velocity_flu[i]);
    }
    s << indent << "position_std[]" << std::endl;
    for (size_t i = 0; i < v.position_std.size(); ++i) {
      s << indent << "  position_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.position_std[i]);
    }
    s << indent << "so3_std[]" << std::endl;
    for (size_t i = 0; i < v.so3_std.size(); ++i) {
      s << indent << "  so3_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.so3_std[i]);
    }
    s << indent << "velocity_std[]" << std::endl;
    for (size_t i = 0; i < v.velocity_std.size(); ++i) {
      s << indent << "  velocity_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.velocity_std[i]);
    }
    s << indent << "angular_velocity_std[]" << std::endl;
    for (size_t i = 0; i < v.angular_velocity_std.size(); ++i) {
      s << indent << "  angular_velocity_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.angular_velocity_std[i]);
    }
    s << indent << "acceleration_std[]" << std::endl;
    for (size_t i = 0; i < v.acceleration_std.size(); ++i) {
      s << indent << "  acceleration_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.acceleration_std[i]);
    }
    s << indent << "angular_acceleration_std[]" << std::endl;
    for (size_t i = 0; i < v.angular_acceleration_std.size(); ++i) {
      s << indent << "  angular_acceleration_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.angular_acceleration_std[i]);
    }
    s << indent << "imu_bias_std[]" << std::endl;
    for (size_t i = 0; i < v.imu_bias_std.size(); ++i) {
      s << indent << "  imu_bias_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.imu_bias_std[i]);
    }
    s << indent << "wheel_radius_std[]" << std::endl;
    for (size_t i = 0; i < v.wheel_radius_std.size(); ++i) {
      s << indent << "  wheel_radius_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.wheel_radius_std[i]);
    }
    s << indent << "gnss_bias_std[]" << std::endl;
    for (size_t i = 0; i < v.gnss_bias_std.size(); ++i) {
      s << indent << "  gnss_bias_std[" << i << "]: ";
      Printer<float>::stream(s, indent + "  ", v.gnss_bias_std[i]);
    }
  }
};

}  // namespace message_operations
}  // namespace ros

#endif  // JOINT_MESSAGE_DEBUGPUBLISH_H
