#include "joint/matrix_proto_utils.h"

#include "common/log.h"

namespace deeproute {
namespace {
template <typename Derived>
void SetVector6dProto(const Eigen::Block<Derived, 6, 1>& vector6d,
                      proto::Vector6d* vector_proto) {
  const Vector6d vec = vector6d.derived();
  vector_proto->set_v0(vec(0));
  vector_proto->set_v1(vec(1));
  vector_proto->set_v2(vec(2));
  vector_proto->set_v3(vec(3));
  vector_proto->set_v4(vec(4));
  vector_proto->set_v5(vec(5));
}
}  // namespace

Vector3d ProtoToVector3d(const proto::Vector3d& vector) {
  return Vector3d(vector.x(), vector.y(), vector.z());
}

proto::Vector3d Vector3dToProto(const Vector3d& vector) {
  proto::Vector3d vector_proto;
  vector_proto.set_x(vector(0));
  vector_proto.set_y(vector(1));
  vector_proto.set_z(vector(2));
  return vector_proto;
}

Matrix3d ProtoToMatrix3d(const proto::Matrix3d& matrix) {
  Matrix3d result = Matrix3d::Zero();
  // clang-format off
  result << matrix.c0().x(), matrix.c0().y(), matrix.c0().z(),
	  		    matrix.c1().x(), matrix.c1().y(), matrix.c1().z(),
	  		    matrix.c2().x(), matrix.c2().y(), matrix.c2().z();
  // clang-format on
  return result.transpose();
}

proto::Matrix3d Matrix3dToProto(const Matrix3d& matrix) {
  proto::Matrix3d matrix_proto;
  *matrix_proto.mutable_c0() = Vector3dToProto(matrix.col(0));
  *matrix_proto.mutable_c1() = Vector3dToProto(matrix.col(1));
  *matrix_proto.mutable_c2() = Vector3dToProto(matrix.col(2));
  return matrix_proto;
}

Matrix6d ProtoToMatrix6d(const proto::Matrix6d& matrix_proto) {
  Matrix6d matrix;
  // clang-format off
  matrix << matrix_proto.c0().v0(),  matrix_proto.c1().v0(), matrix_proto.c2().v0(), matrix_proto.c3().v0(), matrix_proto.c4().v0(), matrix_proto.c5().v0(),
            matrix_proto.c0().v1(),  matrix_proto.c1().v1(), matrix_proto.c2().v1(), matrix_proto.c3().v1(), matrix_proto.c4().v1(), matrix_proto.c5().v1(),
            matrix_proto.c0().v2(),  matrix_proto.c1().v2(), matrix_proto.c2().v2(), matrix_proto.c3().v2(), matrix_proto.c4().v2(), matrix_proto.c5().v2(),
            matrix_proto.c0().v3(),  matrix_proto.c1().v3(), matrix_proto.c2().v3(), matrix_proto.c3().v3(), matrix_proto.c4().v3(), matrix_proto.c5().v3(),
            matrix_proto.c0().v4(),  matrix_proto.c1().v4(), matrix_proto.c2().v4(), matrix_proto.c3().v4(), matrix_proto.c4().v4(), matrix_proto.c5().v4(),
            matrix_proto.c0().v5(),  matrix_proto.c1().v5(), matrix_proto.c2().v5(), matrix_proto.c3().v5(), matrix_proto.c4().v5(), matrix_proto.c5().v5();
  // clang-format on
  return matrix;
}

proto::Matrix6d Matrix6dToProto(const Matrix6d& matrix) {
  proto::Matrix6d matrix_proto;
  SetVector6dProto(matrix.block<6, 1>(0, 0), matrix_proto.mutable_c0());
  SetVector6dProto(matrix.block<6, 1>(0, 1), matrix_proto.mutable_c1());
  SetVector6dProto(matrix.block<6, 1>(0, 2), matrix_proto.mutable_c2());
  SetVector6dProto(matrix.block<6, 1>(0, 3), matrix_proto.mutable_c3());
  SetVector6dProto(matrix.block<6, 1>(0, 4), matrix_proto.mutable_c4());
  SetVector6dProto(matrix.block<6, 1>(0, 5), matrix_proto.mutable_c5());
  return matrix_proto;
}
}  // namespace deeproute
