project(joint)

find_package(catkin REQUIRED
        COMPONENTS
        message_generation
        roscpp
        sensor_msgs
        cv_bridge)

include_directories(${catkin_INCLUDE_DIRS})

# catkin_package(
#   LIBRARIES joint
#   CATKIN_DEPENDS message_runtime roscpp
# )

add_library(joint
        kinematics/attached_body_kinematics.cpp
        # thread/thread_pool.cpp
        # mesh/export_obj.cpp
        matrix_proto_utils.cpp
        # utils/load_configs.cpp
        # utils/onboard_utils.cpp
        # remove_perception_objects.cpp
)

target_link_libraries(joint
        Common::proto
        joint_proto
        common_transform
        absl::strings
        absl::statusor
        lam_common::base
        Boost::filesystem)
# if (NOT INU)
# target_link_libraries(joint
#         Common::os_interface)
# endif()              

# attached_body_kinematics
# add_executable(attached_body_kinematics_test kinematics/attached_body_kinematics_test.cpp)
# target_link_libraries(attached_body_kinematics_test joint ${GTEST_BOTH_LIBRARIES})
# add_test(attached_body_kinematics_test ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/attached_body_kinematics_test)

# # matrix_proto_utils
# add_executable(matrix_proto_utils_test matrix_proto_utils_test.cpp)
# target_link_libraries(matrix_proto_utils_test joint ${GTEST_BOTH_LIBRARIES})
# add_test(matrix_proto_utils_test ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/matrix_proto_utils_test)

# # remove_perception_objects
# add_executable(remove_perception_objects_test remove_perception_objects_test.cpp)
# target_link_libraries(remove_perception_objects_test
#         joint
#         ${GTEST_BOTH_LIBRARIES})
# add_test(NAME remove_perception_objects_test COMMAND remove_perception_objects_test)

# # time
# add_executable(time_test time_test.cpp)
# target_link_libraries(time_test
#         joint
#         ${GTEST_BOTH_LIBRARIES})
# add_test(time_test ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/time_test)

# add_subdirectory(simulation_data)

install(TARGETS joint
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)

set(PROTOBUF_GENERATE_CPP_APPEND_PATH FALSE)
set(Protobuf_IMPORT_DIRS ${CMAKE_CURRENT_SOURCE_DIR}
                         ${CMAKE_SOURCE_DIR}/external/common/proto
                         ${CMAKE_SOURCE_DIR}/external/lam_common/lam_common/proto)

file(GLOB_RECURSE ProtoFiles *.proto)

# list(FILTER ProtoFiles EXCLUDE REGEX "localization/data_adapter/church/idl/localization_service\.proto")
PROTOBUF_GENERATE_CPP(ProtoSources ProtoHeaders ${ProtoFiles})
add_library(joint_proto ${ProtoSources})
target_link_libraries(joint_proto ${PROTOBUF_LIBRARIES} lam_common::proto)
install(TARGETS joint_proto
  ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
install(FILES ${ProtoHeaders}
        DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION})
