#include "joint/mesh/export_obj.h"

#include <fstream>

namespace deeproute {

void ExportObjToFile(const ObjVerticesWithColors& vertices_with_colors,
                     const ObjTriangles& triangles,
                     const std::string& output_file) {
  std::ofstream file_stream(output_file);
  for (const auto& vertice_with_color : vertices_with_colors) {
    file_stream << 'v' << ' ' << vertice_with_color[0] << ' '
                << vertice_with_color[1] << ' ' << vertice_with_color[2] << ' '
                << vertice_with_color[3] << ' ' << vertice_with_color[4] << ' '
                << vertice_with_color[5] << std::endl;
  }
  for (const auto& triangle : triangles) {
    file_stream << 'f' << ' ' << triangle[0] + 1 << ' ' << triangle[1] + 1
                << ' ' << triangle[2] + 1 << std::endl;
  }

  file_stream.close();
}

}  // namespace deeproute
