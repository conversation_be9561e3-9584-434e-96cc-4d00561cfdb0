#pragma once

#include <string>

#include <Eigen/Core>

#include "lam_common/types.h"

namespace deeproute {

using ObjVertices = std::vector<Vector3d>;
using ObjVerticesWithColors =
    std::vector<Vector6d, Eigen::aligned_allocator<Vector6d>>;
using ObjTriangles = std::vector<Vector3i>;

void ExportObjToFile(const ObjVerticesWithColors& vertices_with_colors,
                     const ObjTriangles& triangles,
                     const std::string& output_file);

}  // namespace deeproute
