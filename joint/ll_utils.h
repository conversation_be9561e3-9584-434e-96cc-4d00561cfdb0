#pragma once

#include <cmath>

// the following part copys from <PERSON><PERSON><PERSON><PERSON>'s work
constexpr double kKRASOVSKY_A = 6378245.0;  // equatorial radius [unit: meter]
constexpr double kKRASOVSKY_B = 6356863.0187730473;  // polar radius
constexpr double kKRASOVSKY_ECCSQ =
    6.6934216229659332e-3;  // first eccentricity squared
constexpr double kKRASOVSKY_ECC2SQ =
    6.7385254146834811e-3;                     // second eccentricity squared
constexpr double PI = 3.14159265358979323846;  //π
constexpr double kDEG2RAD = PI / 180.0;
constexpr double kRAD2DEG = 180.0 / PI;

constexpr inline double Deg2Rad(const double deg) { return deg * kDEG2RAD; }
constexpr inline double Rad2Deg(const double rad) { return rad * kRAD2DEG; }

constexpr double kOffset = 100000;

struct LatLon {
  double lat;
  double lon;
};

static void GetGeodeticOffset(const double& wgs84lon, const double& wgs84lat,
                              double* lon2, double* lat2) {
  // get geodetic offset relative to 'center china'
  double lon0 = wgs84lon - 105.0;
  double lat0 = wgs84lat - 35.0;

  // generate an pair offset roughly in meters
  double lon1 = 300.0 + lon0 + 2.0 * lat0 + 0.1 * lon0 * lon0 +
                0.1 * lon0 * lat0 + 0.1 * sqrt(fabs(lon0));
  lon1 = lon1 + (20.0 * sin(6.0 * lon0 * PI) + 20.0 * sin(2.0 * lon0 * PI)) *
                    2.0 / 3.0;
  lon1 =
      lon1 + (20.0 * sin(lon0 * PI) + 40.0 * sin(lon0 / 3.0 * PI)) * 2.0 / 3.0;
  lon1 =
      lon1 + (150.0 * sin(lon0 / 12.0 * PI) + 300.0 * sin(lon0 * PI / 30.0)) *
                 2.0 / 3.0;
  double lat1 = -100.0 + 2.0 * lon0 + 3.0 * lat0 + 0.2 * lat0 * lat0 +
                0.1 * lon0 * lat0 + 0.2 * sqrt(fabs(lon0));
  lat1 = lat1 + (20.0 * sin(6.0 * lon0 * PI) + 20.0 * sin(2.0 * lon0 * PI)) *
                    2.0 / 3.0;
  lat1 =
      lat1 + (20.0 * sin(lat0 * PI) + 40.0 * sin(lat0 / 3.0 * PI)) * 2.0 / 3.0;
  lat1 =
      lat1 + (160.0 * sin(lat0 / 12.0 * PI) + 320.0 * sin(lat0 * PI / 30.0)) *
                 2.0 / 3.0;

  // latitude in radian
  double B = Deg2Rad(wgs84lat);
  double sinB = sin(B), cosB = cos(B);
  double W = sqrt(1 - kKRASOVSKY_ECCSQ * sinB * sinB);
  double N = kKRASOVSKY_A / W;

  // geodetic offset used by GCJ-02
  *lon2 = Rad2Deg(lon1 / (N * cosB));
  *lat2 = Rad2Deg(lat1 * W * W / (N * (1 - kKRASOVSKY_ECCSQ)));
}

static inline LatLon Wgs84ToGcj02(const LatLon& gps) {
  double dlon, dlat;
  GetGeodeticOffset(gps.lon, gps.lat, &dlon, &dlat);

  LatLon ret;
  ret.lat = gps.lat + dlat;
  ret.lon = gps.lon + dlon;

  return ret;
}

static inline void Wgs84ToGcj02(const double& wgs84lon, const double& wgs84lat,
                                double* gcj02lon, double* gcj02lat) {
  double dlon, dlat;
  GetGeodeticOffset(wgs84lon, wgs84lat, &dlon, &dlat);
  *gcj02lon = wgs84lon + dlon;
  *gcj02lat = wgs84lat + dlat;
}

static inline void Gcj02ToWgs84(const double& gcj02lon, const double& gcj02lat,
                                double* wgs84_longitude,
                                double* wgs84_latitude) {
  *wgs84_longitude = gcj02lon;
  *wgs84_latitude = gcj02lat;
  int nIterCount = 0;
  while (++nIterCount < 1000) {
    // get geodetic offset relative to 'center china'
    double lon0 = *wgs84_longitude - 105.0;
    double lat0 = *wgs84_latitude - 35.0;

    // generate an pair offset roughly in meters
    double lon1 = 300.0 + lon0 + 2.0 * lat0 + 0.1 * lon0 * lon0 +
                  0.1 * lon0 * lat0 + 0.1 * sqrt(fabs(lon0));
    lon1 = lon1 + (20.0 * sin(6.0 * lon0 * PI) + 20.0 * sin(2.0 * lon0 * PI)) *
                      2.0 / 3.0;
    lon1 = lon1 +
           (20.0 * sin(lon0 * PI) + 40.0 * sin(lon0 / 3.0 * PI)) * 2.0 / 3.0;
    lon1 =
        lon1 + (150.0 * sin(lon0 / 12.0 * PI) + 300.0 * sin(lon0 * PI / 30.0)) *
                   2.0 / 3.0;
    double lat1 = -100.0 + 2.0 * lon0 + 3.0 * lat0 + 0.2 * lat0 * lat0 +
                  0.1 * lon0 * lat0 + 0.2 * sqrt(fabs(lon0));
    lat1 = lat1 + (20.0 * sin(6.0 * lon0 * PI) + 20.0 * sin(2.0 * lon0 * PI)) *
                      2.0 / 3.0;
    lat1 = lat1 +
           (20.0 * sin(lat0 * PI) + 40.0 * sin(lat0 / 3.0 * PI)) * 2.0 / 3.0;
    lat1 =
        lat1 + (160.0 * sin(lat0 / 12.0 * PI) + 320.0 * sin(lat0 * PI / 30.0)) *
                   2.0 / 3.0;

    double g_lon0 = 0;
    if (lon0 > 0)
      g_lon0 = 0.05 / sqrt(lon0);
    else if (lon0 < 0)
      g_lon0 = -0.05 / sqrt(-lon0);
    else
      g_lon0 = 0;

    double PIlon0 = PI * lon0, PIlat0 = PI * lat0;
    double dlon1_dlonwgs =
        1 + 0.2 * lon0 + 0.1 * lat0 + g_lon0 +
        ((120 * PI * cos(6 * PIlon0) + 40 * PI * cos(2 * PIlon0)) +
         (20 * PI * cos(PIlon0) + 40 * PI / 3.0 * cos(PIlon0 / 3.0)) +
         (12.5 * PI * cos(PIlon0 / 12.0) + 10 * PI * cos(PIlon0 / 30.0))) *
            2.0 / 3.0;
    double dlon1_dlatwgs = 2 + 0.1 * lon0;

    double dlat1_dlonwgs =
        2 + 0.1 * lat0 + 2 * g_lon0 +
        (120 * PI * cos(6 * PIlon0) + 40 * PI * cos(2 * PIlon0)) * 2.0 / 3.0;
    double dlat1_dlatwgs =
        3 + 0.4 * lat0 + 0.1 * lon0 +
        ((20 * PI * cos(PIlat0) + 40.0 * PI / 3.0 * cos(PIlat0 / 3.0)) +
         (40 * PI / 3.0 * cos(PIlat0 / 12.0) +
          32.0 * PI / 3.0 * cos(PIlat0 / 30.0))) *
            2.0 / 3.0;

    // latitude in radian
    double B = Deg2Rad(*wgs84_latitude);
    double sinB = sin(B), cosB = cos(B);
    double WSQ = 1 - kKRASOVSKY_ECCSQ * sinB * sinB;
    double W = sqrt(WSQ);
    double N = kKRASOVSKY_A / W;

    double dW_dlatwgs = -PI * kKRASOVSKY_ECCSQ * sinB * cosB / (180.0 * W);
    double dN_dlatwgs = -kKRASOVSKY_A * dW_dlatwgs / WSQ;

    double PIxNxCosB = PI * N * cosB;
    double dlongcj_dlonwgs = 1.0 + 180.0 * dlon1_dlonwgs / PIxNxCosB;
    double dlongcj_dlatwgs = 180 * dlon1_dlatwgs / PIxNxCosB -
                             180 * lon1 * PI *
                                 (dN_dlatwgs * cosB - PI * N * sinB / 180.0) /
                                 (PIxNxCosB * PIxNxCosB);

    double PIxNxSubECCSQ = PI * N * (1 - kKRASOVSKY_ECCSQ);
    double dlatgcj_dlonwgs = 180 * WSQ * dlat1_dlonwgs / PIxNxSubECCSQ;
    double dlatgcj_dlatwgs =
        1.0 + 180 *
                  (N * (dlat1_dlatwgs * WSQ + 2.0 * lat1 * W * dW_dlatwgs) -
                   lat1 * WSQ * dN_dlatwgs) /
                  (N * PIxNxSubECCSQ);

    double gcj02lonEst, gcj02latEst;
    Wgs84ToGcj02(*wgs84_longitude, *wgs84_latitude, &gcj02lonEst, &gcj02latEst);
    double l_lon = gcj02lon - gcj02lonEst;
    double l_lat = gcj02lat - gcj02latEst;

    double d_latwgs =
        (l_lon * dlatgcj_dlonwgs - l_lat * dlongcj_dlonwgs) /
        (dlongcj_dlatwgs * dlatgcj_dlonwgs - dlatgcj_dlatwgs * dlongcj_dlonwgs);
    double d_lonwgs = (l_lon - dlongcj_dlatwgs * d_latwgs) / dlongcj_dlonwgs;

    if (fabs(d_latwgs) < 1.0e-9 && fabs(d_lonwgs) < 1.0e-9) break;
    *wgs84_longitude = *wgs84_longitude + d_lonwgs;
    *wgs84_latitude = *wgs84_latitude + d_latwgs;
  }
}
