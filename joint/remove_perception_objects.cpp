#include "joint/remove_perception_objects.h"

#include <glog/logging.h>

#include "mapping/tightly_coupled/quaternion_utils.h"

namespace deeproute {

namespace {
bool IsWithinObstacleBox3D(const Eigen::Vector3d& point,
                           const Eigen::Vector3d& obstacle_center,
                           const Eigen::Matrix3d& point_rotation,
                           const double dx, const double dy, const double dz) {
  Eigen::Vector3d point_in_object_coordinate =
      point_rotation * (point - obstacle_center);
  return (point_in_object_coordinate[0] <= dx &&
          point_in_object_coordinate[0] >= -dx &&
          point_in_object_coordinate[1] <= dy &&
          point_in_object_coordinate[1] >= -dy &&
          point_in_object_coordinate[2] <= dz &&
          point_in_object_coordinate[2] >= -dz);
}

template <typename PointT>
void MarkPointsInBoundingBoxWithSkippingMarkedPoints(
    std::vector<bool>* is_marked_ptr,
    const std::vector<PointT, Eigen::aligned_allocator<PointT>>& points,
    const Eigen::Vector3d& bounding_box_center,
    const Eigen::Vector3d& bounding_box_size, const double bounding_box_heading,
    const double bounding_box_buffer = 0) {
  CHECK(is_marked_ptr->size() == points.size());
  auto& is_marked = *is_marked_ptr;
  // Used for coarse collision test.
  const Eigen::Vector3d box_half_size = bounding_box_size / 2.0;
  const double max_distance = box_half_size.norm() + bounding_box_buffer;
  const double max_x = bounding_box_center.x() + max_distance;
  const double min_x = bounding_box_center.x() - max_distance;
  const double max_y = bounding_box_center.y() + max_distance;
  const double min_y = bounding_box_center.y() - max_distance;
  const double max_z = bounding_box_center.z() + max_distance;
  const double min_z = bounding_box_center.z() - max_distance;

  // Used for accurate collision test.
  const Eigen::Matrix3d inverse_rotation =
      mapping::RollPitchYawToQuaternion<double>(0.0, 0.0, -bounding_box_heading)
          .toRotationMatrix();

  for (size_t m = 0; m < points.size(); m++) {
    const auto& point = points[m];
    if (point.z >= max_z || point.z <= min_z || point.x >= max_x ||
        point.x <= min_x || point.y >= max_y || point.y <= min_y) {
      continue;
    }
    if (is_marked[m]) {
      continue;
    }

    Eigen::Vector3d p(points[m].x, points[m].y, points[m].z);
    if (IsWithinObstacleBox3D(p, bounding_box_center, inverse_rotation,
                              box_half_size.x() + bounding_box_buffer,
                              box_half_size.y() + bounding_box_buffer,
                              box_half_size.z() + bounding_box_buffer)) {
      is_marked[m] = true;
    }
  }
}
}  // namespace

bool IsMovingPerceptionObstacle(
    const perception::PerceptionObstacle& obstacle) {
  return obstacle.type() == perception::PerceptionObstacle::Type::
                                PerceptionObstacle_Type_PEDESTRIAN ||
         obstacle.type() == perception::PerceptionObstacle::Type::
                                PerceptionObstacle_Type_BICYCLE ||
         obstacle.type() == perception::PerceptionObstacle::Type::
                                PerceptionObstacle_Type_VEHICLE ||
         obstacle.type() == perception::PerceptionObstacle::Type::
                                PerceptionObstacle_Type_TRUCK ||
         obstacle.type() == perception::PerceptionObstacle::Type::
                                PerceptionObstacle_Type_UNKNOWN_MOVABLE;
}

template <class PointT>
std::vector<bool> MarkPerceptionObjects(
    const ::common::Transformation3& sensing_to_map,
    const perception::PerceptionObstacles& obstacles,
    const std::vector<PointT, Eigen::aligned_allocator<PointT>>& points,
    const double bounding_box_buffer) {
  if (points.empty()) {
    return std::vector<bool>();
  }

  std::vector<bool> is_marked(points.size(), false);
  for (const auto& perception_obstacle : obstacles.perception_obstacle()) {
    if (!IsMovingPerceptionObstacle(perception_obstacle)) {
      continue;
    }
    const Eigen::Vector3d obstacle_center_m{perception_obstacle.position().x(),
                                            perception_obstacle.position().y(),
                                            perception_obstacle.position().z()};
    const Eigen::Vector3d obstacle_center_s =
        sensing_to_map * obstacle_center_m;

    // Used for accurate collision test.
    const double obstacle_heading =
        perception_obstacle.theta() + sensing_to_map.GetRollPitchYaw()[2];
    const Eigen::Vector3d box_size{perception_obstacle.length(),
                                   perception_obstacle.width(),
                                   perception_obstacle.height()};
    MarkPointsInBoundingBoxWithSkippingMarkedPoints(
        &is_marked, points, obstacle_center_s, box_size, obstacle_heading,
        bounding_box_buffer);
  }

  return is_marked;
}

template <typename PointT>
std::vector<bool> MarkPointsInBoundingBox(
    const std::vector<PointT, Eigen::aligned_allocator<PointT>>& points,
    const Eigen::Vector3d& bounding_box_center,
    const Eigen::Vector3d& bounding_box_size, const double bounding_box_heading,
    const double bounding_box_buffer) {
  std::vector<bool> is_marked(points.size(), false);
  MarkPointsInBoundingBoxWithSkippingMarkedPoints(
      &is_marked, points, bounding_box_center, bounding_box_size,
      bounding_box_heading, bounding_box_buffer);
  return is_marked;
}

template std::vector<bool> MarkPerceptionObjects<PointXYZIRT>(
    const ::common::Transformation3& sensing_to_map,
    const perception::PerceptionObstacles& obstacles,
    const std::vector<PointXYZIRT, Eigen::aligned_allocator<PointXYZIRT>>&
        points,
    const double bounding_box_buffer);

template std::vector<bool> MarkPerceptionObjects<pcl::PointXYZ>(
    const ::common::Transformation3& sensing_to_map,
    const perception::PerceptionObstacles& obstacles,
    const std::vector<pcl::PointXYZ, Eigen::aligned_allocator<pcl::PointXYZ>>&
        points,
    const double bounding_box_buffer);

template std::vector<bool> MarkPointsInBoundingBox(
    const std::vector<pcl::PointXYZ, Eigen::aligned_allocator<pcl::PointXYZ>>&
        points,
    const Eigen::Vector3d& bounding_box_center,
    const Eigen::Vector3d& bounding_box_size, const double bounding_box_heading,
    const double bounding_box_buffer);

}  // namespace deeproute
