#include "joint/remove_perception_objects.h"

#include <random>

#include <gtest/gtest.h>

namespace deeproute {

namespace {
std::vector<pcl::PointXYZ, Eigen::aligned_allocator<pcl::PointXYZ>>
GenerateRandomPoints(size_t point_size) {
  std::default_random_engine e(time(nullptr));
  std::uniform_real_distribution<float> u(0, 200);
  std::vector<pcl::PointXYZ, Eigen::aligned_allocator<pcl::PointXYZ>> cloud;
  cloud.reserve(point_size);
  for (size_t i = 0; i < point_size; ++i) {
    cloud.emplace_back(u(e), u(e), u(e));
  }
  return cloud;
}

perception::PerceptionObstacles GeneratePerceptionObstacles() {
  perception::PerceptionObstacles obstacles;

  perception::PerceptionObstacle obstacle1;
  obstacle1.mutable_position()->set_x(100);
  obstacle1.mutable_position()->set_y(110);
  obstacle1.mutable_position()->set_z(120);
  obstacle1.set_theta(0.5);
  obstacle1.set_length(50.0);
  obstacle1.set_width(40.0);
  obstacle1.set_height(30.0);
  obstacle1.set_type(
      perception::PerceptionObstacle::Type::PerceptionObstacle_Type_TRUCK);
  *obstacles.add_perception_obstacle() = obstacle1;

  perception::PerceptionObstacle obstacle2;
  obstacle2.mutable_position()->set_x(150.1);
  obstacle2.mutable_position()->set_y(160.2);
  obstacle2.mutable_position()->set_z(170.3);
  obstacle2.set_theta(-0.3);
  obstacle2.set_length(60.0);
  obstacle2.set_width(40.0);
  obstacle2.set_height(20.0);
  obstacle2.set_type(
      perception::PerceptionObstacle::Type::PerceptionObstacle_Type_VEHICLE);
  *obstacles.add_perception_obstacle() = obstacle2;

  return obstacles;
}

}  // namespace

TEST(MarkPointsToDeleteTest, Test1) {
  size_t point_size = 10000;
  const auto cloud = GenerateRandomPoints(point_size);
  Eigen::Vector3d box_size{50, 50, 50};
  Eigen::Vector3d box_center{20, 20, 20};
  const auto to_del1 =
      MarkPointsInBoundingBox(cloud, box_center, box_size, 0, 0);
  std::vector<bool> to_del2(point_size, false);
  const Eigen::Vector3d upper_bound = box_center + box_size / 2;
  const Eigen::Vector3d lower_bound = box_center - box_size / 2;
  for (size_t i = 0; i < point_size; ++i) {
    const auto& point = cloud[i];
    bool is_in_box = true;
    for (size_t dimension = 0; dimension < 3; ++dimension) {
      const auto value = point.getVector3fMap()[dimension];
      if (value >= lower_bound[dimension] && value <= upper_bound[dimension]) {
        // pass
      } else {
        is_in_box = false;
        break;
      }
    }
    to_del2[i] = is_in_box;
  }
  EXPECT_TRUE(to_del2 == to_del1);
}

TEST(MarkPerceptionObjects, MarkRandomPointsInsideObjects) {
  ::common::Transformation3 sensing_to_map(10, 20, 30, 0, 0, 0.13);
  auto obstacles = GeneratePerceptionObstacles();
  size_t point_size = 10000;
  const auto cloud = GenerateRandomPoints(point_size);
  const double bounding_box_buffer = 0.1;

  std::vector<bool> marked_perception_obstacle_points = MarkPerceptionObjects(
      sensing_to_map, obstacles, cloud, bounding_box_buffer);

  std::vector<bool> point_belongs_to_obstacles(point_size, false);
  for (const auto& obstacle : obstacles.perception_obstacle()) {
    const ::common::Transformation3 map_to_obstacle_flu(
        obstacle.position().x(), obstacle.position().y(),
        obstacle.position().z(), 0, 0, obstacle.theta());
    const ::common::Transformation3 obstacle_flu_to_sensing =
        map_to_obstacle_flu.Inverse() * sensing_to_map.Inverse();
    const double obstacle_frame_x_max =
        obstacle.length() * 0.5 + bounding_box_buffer;
    const double obstacle_frame_y_max =
        obstacle.width() * 0.5 + bounding_box_buffer;
    const double obstacle_frame_z_max =
        obstacle.height() * 0.5 + bounding_box_buffer;

    for (size_t i = 0; i < point_size; ++i) {
      if (point_belongs_to_obstacles[i]) {
        continue;
      }
      const Eigen::Vector3d sensing_frame_point =
          cloud[i].getVector3fMap().cast<double>();
      const Eigen::Vector3d obstacle_flu_frame_point =
          obstacle_flu_to_sensing * sensing_frame_point;

      if (fabs(obstacle_flu_frame_point[0]) <= obstacle_frame_x_max &&
          fabs(obstacle_flu_frame_point[1]) <= obstacle_frame_y_max &&
          fabs(obstacle_flu_frame_point[2]) <= obstacle_frame_z_max) {
        point_belongs_to_obstacles[i] = true;
      }
    }
  }

  EXPECT_TRUE(marked_perception_obstacle_points == point_belongs_to_obstacles);
}

}  // namespace deeproute
