#pragma once

#ifdef DR_NVIDIA
#include <cuda_runtime.h>
#endif

#include <vector>

#include "common/log.h"

namespace localization {
namespace base {

#ifdef DR_NVIDIA

inline cudaError_t cudaStreamSyncAndCheck(cudaStream_t stream, const char* file,
                                          unsigned line) {
  cudaStreamSynchronize(stream);
  const auto err = cudaGetLastError();
  if (err != cudaSuccess) {
    MLOG(FATAL) << file << ":" << line << " code: " << err
                << " desc: " << cudaGetErrorString(err);
  }
  return err;
}

#define CUDA_SYNC_AND_CHECK(stream)                                           \
  {                                                                           \
    ::localization::base::cudaStreamSyncAndCheck(stream, __FILE__, __LINE__); \
  }

inline void CudaDeviceMemoryDeleter(void* p) {
  if (p) {
    cudaFree(p);
  }
}

template <class T>
inline void CudaDeviceMemoryDeleter(T* p) {
  if (p) {
    cudaFree(p);
  }
}

inline void CudaHostMemoryDeleter(void* p) {
  if (p) {
    cudaFreeHost(p);
  }
}

template <class T>
inline void CudaHostMemoryDeleter(T* p) {
  if (p) {
    cudaFreeHost(p);
  }
}
template <typename T>
struct CudaHostAllocator {
  using value_type = T;
  static T* allocate(size_t num) {
    T* ptr;
    if (auto err = cudaMallocHost(&ptr, num * sizeof(T)); err != cudaSuccess) {
      MLOG(FATAL) << "CudaHostAllocator::allocate failed: "
                  << cudaGetErrorString(err);
    }
    return ptr;
  }
  static void deallocate(T* ptr, size_t) {
    if (auto err = cudaFreeHost(ptr); err != cudaSuccess) {
      MLOG(FATAL) << "CudaHostAllocator::deallocate failed: "
                  << cudaGetErrorString(err);
    }
  }
};

// Imitate a value of type T, ensure uses pin memory for storage
// Attention!! need to use `Value::value_type` to obtain sizeof
template <typename T>
struct CudaHostValue {
  template <typename... Args>
  CudaHostValue(Args&&... args) {
    ptr_ = CudaHostAllocator<T>::allocate(1);
    ::new (ptr_) T(std::forward<Args>(args)...);
  }
  ~CudaHostValue() {
    ptr_->~T();
    CudaHostAllocator<T>::deallocate(ptr_, 1);
  }
  template <typename... Args>
  CudaHostValue& operator=(Args&&... args) {
    *ptr_ = {std::forward<Args>(args)...};
    return *this;
  }

  CudaHostValue(const CudaHostValue&) = delete;
  CudaHostValue& operator=(const CudaHostValue&) = delete;

  operator T&() { return *ptr_; }
  operator const T&() const { return *ptr_; }
  T* operator&() { return ptr_; }
  const T* operator&() const { return ptr_; }

 private:
  T* ptr_;
};

template <typename T>
using PinVector = std::vector<T, CudaHostAllocator<T>>;

// Imitate a value of type T, ensure uses pin memory for storage
// Attention!! need to use `Value::value_type` to obtain sizeof
template <typename T>
using PinValue = CudaHostValue<T>;

#else

template <typename T>
using PinVector = std::vector<T>;

template <typename T>
using PinValue = T;

#endif

}  // namespace base
}  // namespace localization
