load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "onboard_utils",
    srcs = ["onboard_utils.cpp"],
    hdrs = ["onboard_utils.h"],
    deps = [
        "@common//base:deeproute_path",
    ],
)

cc_library(
    name = "cuda_utils",
    srcs = [],
    hdrs = ["cuda_utils.h"],
    deps = [
        "@common//common:log",
    ] + select({
        "@deeproute_build_tools//:nvidia_setting": ["@local_cuda//:cuda_runtime"],
        "//conditions:default": [],
    }),
)
