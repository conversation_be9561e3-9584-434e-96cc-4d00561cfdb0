#include "joint/utils/onboard_utils.h"

#include <absl/status/statusor.h>
#include <absl/strings/str_cat.h>
#include <boost/filesystem.hpp>
#include <boost/filesystem/operations.hpp>
#include <boost/filesystem/path.hpp>

#include "base/deeproute_path.h"

namespace deeproute {
absl::StatusOr<std::string> GetCarConfigFilePathOnboard(
    const std::string& car_id) {
  const std::string car_config_path =
      boost::filesystem::absolute(
          boost::filesystem::path(deeproute::base::GetDeeproutePath().c_str()) /
          boost::filesystem::path(
              absl::StrCat("onboard/config/car/", car_id, "_car_config.cfg")))
          .string();
  const std::string default_car_config_path =
      boost::filesystem::absolute(
          boost::filesystem::path(deeproute::base::GetDeeproutePath().c_str()) /
          boost::filesystem::path("onboard/config/car/car_config.cfg"))
          .string();
  if (boost::filesystem::exists(default_car_config_path)) {
    return default_car_config_path;
  }
  if (boost::filesystem::exists(car_config_path)) {
    return car_config_path;
  }
  return absl::NotFoundError(absl::StrCat("car config not found: [",
                                          car_config_path, "] or [",
                                          default_car_config_path, "]"));
}
}  // namespace deeproute