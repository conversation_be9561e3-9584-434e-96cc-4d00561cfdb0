load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "matrix_proto_utils",
    srcs = ["matrix_proto_utils.cpp"],
    hdrs = ["matrix_proto_utils.h"],
    deps = [
        "@common//common:log",
        "@lam_common//lam_common:types",
        "@lam_common//lam_common/proto/lam_common:matrix_cc_proto",
    ],
)

cc_library(
    name = "math",
    srcs = [],
    hdrs = ["math.h"],
)

cc_library(
    name = "ll_utils",
    srcs = [],
    hdrs = ["ll_utils.h"],
)
