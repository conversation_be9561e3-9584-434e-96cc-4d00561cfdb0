#include "joint/matrix_proto_utils.h"

#include <gtest/gtest.h>
#include <third_party/eigen_checks/gtest.h>

namespace deeproute {

const double kEps = 1e-10;

TEST(ProtoToVector3dTest, ATrivialExample) {
  proto::Vector3d vector3d_proto;
  vector3d_proto.set_x(1.375);
  vector3d_proto.set_z(2.413);

  Vector3d expected_vector = Vector3d::Zero();
  expected_vector(0) = 1.375;
  expected_vector(2) = 2.413;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(expected_vector,
                                ProtoToVector3d(vector3d_proto), kEps));
}

TEST(Vector3dToProtoTest, ATrivialExample) {
  Vector3d vector = Vector3d::Zero();
  vector(0) = 1.375;
  vector(2) = 2.413;

  EXPECT_TRUE(EIGEN_MATRIX_NEAR(ProtoToVector3d(Vector3dToProto(vector)),
                                vector, kEps));
}

TEST(ProtoToMatrix3dTest, ATrivialExample) {
  proto::Matrix3d matrix6d_proto;
  matrix6d_proto.mutable_c0()->set_x(1.375);
  matrix6d_proto.mutable_c2()->set_y(2.413);

  Matrix3d expected_matrix = Matrix3d::Zero();
  expected_matrix(0, 0) = 1.375;
  expected_matrix(1, 2) = 2.413;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(expected_matrix,
                                ProtoToMatrix3d(matrix6d_proto), kEps));
}

TEST(Matrix3dToProtoTest, ATrivialExample) {
  Matrix3d matrix = Matrix3d::Zero();
  matrix(0, 0) = 1.375;
  matrix(1, 2) = 2.413;

  EXPECT_TRUE(EIGEN_MATRIX_NEAR(ProtoToMatrix3d(Matrix3dToProto(matrix)),
                                matrix, kEps));
}

TEST(ProtoToMatrix6dTest, ATrivialExample) {
  proto::Matrix6d matrix6d_proto;
  matrix6d_proto.mutable_c0()->set_v3(1.375);
  matrix6d_proto.mutable_c3()->set_v5(2.413);

  Matrix6d expected_matrix = Matrix6d::Zero();
  expected_matrix(3, 0) = 1.375;
  expected_matrix(5, 3) = 2.413;
  EXPECT_TRUE(EIGEN_MATRIX_NEAR(expected_matrix,
                                ProtoToMatrix6d(matrix6d_proto), kEps));
}

TEST(Matrix6dToProtoTest, ATrivialExample) {
  Matrix6d matrix = Matrix6d::Zero();
  matrix(0, 3) = 1.375;
  matrix(3, 5) = 2.413;

  EXPECT_TRUE(EIGEN_MATRIX_NEAR(ProtoToMatrix6d(Matrix6dToProto(matrix)),
                                matrix, kEps));
}

}  // namespace deeproute
