<!-- # Lane Based Localization Benchmark

# install FMM

```
sudo apt update && sudo apt install deeproute-fmm-dev -y
```

# install lock on road from source or from apt
   ```
   cmake .. && make -j6 && sudo make install
   ```

   or

   ```
   sudo apt install deeproute-lock-on-road-dev
   ```


# Run
  ```
  modify config at:
  'lane-based-localization-benchmark/executable/ros_share/config.cfg'
  ```

  ```
  source /opt/deeproute/lock_on_road/setup.bash && /opt/deeproute/lock_on_road/lib/lock_on_road/lock_on_road_node
  ```


# run benchmark
./devel/lib/lane_based_localization/run_benchmark_on_all_cases --root '/media/songhaoran/DATA/POC/benchmark' --out_root '/media/songhaoran/DATA/POC/benchmark_output'

where 'root' means path to your benchmark directory;  'out_root' means path to results


# release steps:
1. change config.xml, remove any CHECK(false), which you shoulnt have used!;
2. update fmm version in both gitlab-ci.yaml and config.xml
3. git tag to the latest version and push
4. go to drivers, add lock on road release note and jsonnet, push, then click release at:
#https://code.deeproute.ai/deeproute-projects/public/driver/-/merge_requests/406
https://code.deeproute.ai/deeproute-projects/public/driver/-/merge_requests/938


# save logs to a file
source /opt/deeproute/lock_on_road/setup.bash && /opt/deeproute/lock_on_road/lib/lock_on_road/lock_on_road_node 2>&1 | tee log.txt


# current search radius logic
```
  double search_radius;
  if (gnss_type_ == 6) {
    search_radius = 50;
  } else if (gnss_type_ == 0) {
    search_radius = 200;
  } else {
    search_radius = 100;
  }
```


# to play back data
```
rosbag play /localization/lock_on_map:=aa222 /localization/lock_on_map_debug:=aa222 /localization/routing_mask:=aa222 /localization/lane_index:=aa222 /localization/lock_on_road:=aa222 /localization/lock_on_road_debug:=aa222 /localization/lock_on_map:=aa222 /localization/lock_on_map_debug:=aa222 /localization/routing_mask:=aa222 /localization/lane_mask:=aa222  *.bag

```

# version info

deeproute-lock-on-road-dev <=1.0.57 : os interface 1.0.24 + no church

deeproute-lock-on-road-dev > 1.0.62: os interface 1.1.3 + church


# to use python scritps
cd 到 script目录下然后使用如下命令安装依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple -->

# 本地 debug

```
# install bazel

# build driver using local directory
<NAME_EMAIL>:deeproute-projects/public/driver.git
cd driver
source setup_dev.bash && source /opt/deeproute/lock_on_road/setup.bash  && bash driver.sh build integration:mainboard integration/components:liblock_on_road_component.so --override_repository=lock_on_road=/home/<USER>/shr/lane-based-localization-benchmark

# run component
# in driver directory
source setup_dev.bash && source /opt/deeproute/lock_on_road/setup.bash && bazel-bin/integration/mainboard -m config/component/lock_on_road.jsonnet
```

# run from map engine

```
bash driver.sh build integration:mainboard integration/components/libmap_engine_component.so --override_repository=map_engine=/home/<USER>/shr/ws2/map-engine
source setup_dev.bash && bazel-bin/integration/mainboard -m config/component/map_engine.jsonnet -p /task/task_type=L2_driving
```

# connect to remote 4090 maching

sshpass -p Deeproute@123 ssh lam@************

# connect to beijing ORIN

sshpass -p nvidia ssh nvidia@***********

# to disable DDMM

vim /opt/deeproute/lock_on_road/share/lock_on_road/config.cfg
将其中的 enable_ddmm 的 true 改为 false, 重新启动.

# ownership

sudo chown -R $USER:$USER /opt/deeproute/lock_on_road

# mount bj113

sudo sshfs -o allow_other,default_permissions lam@************:/home/<USER>/shr /mnt/bj113/

# download bags by feishu id

export feishu_id=********** && python ~/shr/lane-based-localization-benchmark/scripts/download_case_by_feishu_id.py ${feishu_id} . && mkdir ${feishu_id}/out && python ~/shr/lane-based-localization-benchmark/scripts/extract_pose_and_routing_response_to_geojson_gcj02.py ${feishu_id}/\*.bag ${feishu_id}/out

# DDMM

run ddmm on multiple single bags
./devel/lib/lock_on_road/run_lor_on_bags --in /home/<USER>/shr/ddmm_cases/********** --out /home/<USER>/shr/ddmm_cases/bags_out

run ddmm on single case
./devel/lib/lock_on_road/run_lor_on_case --in /home/<USER>/shr/ddmm_cases/********** --out /home/<USER>/shr/ddmm_cases/bags_out

run ddmm cases
./executable/run_lor_cases.sh /home/<USER>/shr/ddmm_cases
