cmake_minimum_required(VERSION 3.11)

project(lock_on_road)

SET(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE) # required by OsInterface
message(STATUS "CMAKE version ${CMAKE_VERSION}")

option(CHURCH_BUILD "Build church component" ON)

if(CHURCH_BUILD)
  add_definitions(-DCHURCH_BUILD)
endif()

set(CMAKE_CXX_STANDARD 17 CACHE STRING "The C++ standard to build with")
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

set(CMAKE_INSTALL_PREFIX /opt/deeproute/lock_on_road)
set(CMAKE_CXX_FLAGS "-pthread -O2 -Wreturn-type -Wextra -fPIC -Werror -Wno-error=comment -Wno-error=reorder -Wno-error=unused-but-set-variable -Wno-error=unused-parameter -Wno-error=sign-compare")
add_compile_options(-g -fomit-frame-pointer)
# add_compile_options(-Werror=return-type)
# add_compile_options(-Werror=reorder)
# add_compile_options(-Werror=unused-but-set-variable)

set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_CURRENT_SOURCE_DIR}/cmake)

set(PREINSTALL_PROTO OFF)
set(BUILD_COMMON_MODULE_TEST OFF)
set(USE_QPOASE OFF)
set(BUILD_WITH_MARCH_NATIVE OFF)
option(ENABLE_ROS "for ros debug. ci will turn off" ON)

if(ENABLE_ROS)
  set(USE_DPBAG ON)
endif()

if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
  set(USE_DPBAG ON)
elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64")
  set(USE_DPBAG OFF)
endif()

set(ENABLE_DPBAG OFF)
set(USE_DRMETRIC ON)
set(BUILD_BACKEND_BASE_LIBS ON)
set(BUILD_BACKEND_CLIENT_LIBS ON)

add_definitions(
  -DEIGEN_NO_DEBUG
  -DPCL_NO_PRECOMPILE
  -DROSCPP_USE_TCP_NODELAY)

find_package(OpenMP)
find_package(drinfer REQUIRED PATHS
  /opt/deeproute/common/inference_engine/lib/cmake/inference_engine)

if(OPENMP_FOUND)
  set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
endif()

message("CMAKE_SYSTEM_PROCESSOR" , ${CMAKE_SYSTEM_PROCESSOR})

if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
  message("x86_64 architecture detected")
  include_directories(/usr/local/cuda/targets/x86_64-linux/include/)
  link_directories(/usr/local/cuda/targets/x86_64-linux/lib/)
elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64")
  message("ARM architecture detected")
  include_directories(/usr/local/cuda/targets/aarch64-linux/include/)
  link_directories(/usr/local/cuda/targets/aarch64-linux/lib/)
else()
  message("Unknown or unsupported architecture: ${CMAKE_SYSTEM_PROCESSOR}")

  # Handle unsupported architecture
endif()

include(FetchContent)
set(FETCHCONTENT_QUIET off)
set(PREINSTALL_PROTO OFF)
set(FETCHCONTENT_BASE_DIR ${PROJECT_SOURCE_DIR}/external)
FetchContent_Declare(
  common
  GIT_REPOSITORY *********************:deeproute-projects/public/common.git
  # GIT_TAG dev_master
  GIT_TAG feat/lor_parallel_road_status # feat/lor_parallel_road_status 250124
  GIT_PROGRESS TRUE
  SOURCE_DIR ${PROJECT_SOURCE_DIR}/external/common
)
FetchContent_Declare(
  lam_common
  GIT_REPOSITORY *********************:deeproute-org/localization-mapping-calibration/all/lam-common.git
  GIT_TAG 83a46c9b3c20882354098492f2d776917afea956
  GIT_PROGRESS TRUE
  SOURCE_DIR ${PROJECT_SOURCE_DIR}/external/lam_common)
FetchContent_Declare(
  fmm
  GIT_REPOSITORY *********************:haoransong/fmm.git
  GIT_TAG d8660cc6f32f10610520aa5a85e75f5f3640fafc
  GIT_PROGRESS TRUE
  SOURCE_DIR ${PROJECT_SOURCE_DIR}/external/fmm)

set(BUILD_COMMON_MODULE_TEST OFF)
FetchContent_MakeAvailable(common)
FetchContent_MakeAvailable(lam_common)
FetchContent_MakeAvailable(fmm)

include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/experimental)

include_directories(${CMAKE_SOURCE_DIR}/external/common)
include_directories(${CMAKE_SOURCE_DIR}/external/common/proto)
include_directories(${CMAKE_SOURCE_DIR}/external/common/proto_gen)
include_directories(${CMAKE_CURRENT_BINARY_DIR}/proto)
include_directories(${CMAKE_CURRENT_BINARY_DIR})
include_directories(${CMAKE_CURRENT_BINARY_DIR}/joint)
include_directories(${CMAKE_SOURCE_DIR}/external/lam_common)
include_directories(${CMAKE_SOURCE_DIR}/external/lam_common-build/lam_common/proto)
include_directories(${CMAKE_SOURCE_DIR}/external/common-build/dpbag)
include_directories(${CMAKE_SOURCE_DIR}/external/common-build/dpbag/proto)

include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/fmm)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/fmm/src)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/external/fmm/third_party)

enable_testing()

find_package(catkin REQUIRED
  COMPONENTS
  message_generation
  roscpp
  rosbag
  sensor_msgs)
catkin_package(
  LIBRARIES calibration
  CATKIN_DEPENDS rosbag
)
include_directories(${catkin_INCLUDE_DIRS})

message(STATUS "Finding LZ4 libraries")
find_library(LZ4_LIBRARIES NAMES liblz4.so HINTS "lz4/lib")

if(LZ4_LIBRARIES)
  message(STATUS "Found: ${LZ4_LIBRARIES}")
else()
  message(STATUS "Not found: ${LZ4_LIBRARIES}")
  message(FATAL_ERROR "Cannot find required LZ4 libraries")
endif()

find_package(absl REQUIRED CONFIG)

find_package(GMock REQUIRED)
include_directories(${GMOCK_INCLUDE_DIRS})

find_package(GTest REQUIRED)
include_directories(${GTest_INCLUDE_DIRS})

find_package(Boost REQUIRED filesystem)
include_directories(${Boost_INCLUDE_DIRS})
find_package(GFlags REQUIRED)
include_directories(${GFlags_INCLUDE_DIRS})
find_package(Protobuf 3.13.0 REQUIRED)

# set(FMM_LIBRARY "/opt/deeproute/fmm/lib/libFMMLIB.so")
# include_directories("/opt/deeproute/fmm/include/fmm")
# message("Fmm path: " , ${FMM_LIBRARY})
set(FMM_LIBRARY ${CMAKE_CURRENT_SOURCE_DIR}/external/fmm-build/libFMMLIB.so)

find_package(GDAL REQUIRED)
link_libraries(${GDAL_LIBRARIES})
include_directories(${GDAL_INCLUDE_DIR})

find_package(PCL REQUIRED COMPONENTS common io)
include_directories(${PCL_INCLUDE_DIRS})
link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})

# for offline ddmm debug node
find_package(OsInterface REQUIRED PATHS
  /opt/deeproute/common/os_interface/lib/cmake/os_interface)

add_definitions(
  -Wno-attributes
  -DS2_USE_GLOG)

add_subdirectory(data_adapter)
add_subdirectory(joint)
add_subdirectory(executable)
add_subdirectory(proto)
add_subdirectory(lane_estimator)
add_subdirectory(mm_localizer)
add_subdirectory(localizer)
add_subdirectory(geometry)

add_subdirectory(scripts)
add_subdirectory(third_party)
