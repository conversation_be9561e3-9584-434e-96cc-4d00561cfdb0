#include "data_adapter/ins_adapter.h"

#include "data_adapter/utm_projector.h"
#include "joint/math.h"

namespace deeproute {
namespace localization {
namespace {

void SetCovariance(const google::protobuf::RepeatedField<float>& input,
                   google::protobuf::RepeatedField<float>* output) {
  output->CopyFrom(input);
}

drivers::gnss::Ins_Type InsTypeCast(drivers::gnss::SensorsIns_Type ins_type) {
  switch (ins_type) {
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_GOOD:
      return drivers::gnss::Ins_Type::Ins_Type_GOOD;
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_CONVERGING:
      return drivers::gnss::Ins_Type::Ins_Type_CONVERGING;
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_INVALID:
      return drivers::gnss::Ins_Type::Ins_Type_INVALID;
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_MAP_COLLECTION:
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_UNCONVERGE_DR:
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_GNSS_LOST:
    case drivers::gnss::SensorsIns_Type::SensorsIns_Type_ABNORMAL_STATE:
      // MLOG(FATAL) << "Unsupported type conversion, exit....";
      // google::LogMessage::Fail();
      return drivers::gnss::Ins_Type::Ins_Type_CONVERGING;
  }
  return drivers::gnss::Ins_Type::Ins_Type_CONVERGING;
}

}  // namespace

InsAdapter::InsAdapter(
    const proto::Projection& projection,
    const ::common::Transformation3& vehicle_to_imu_transform,
    const bool use_rtk_pose_forward, const bool use_autopilot_mode)
    : imu_to_vehicle_transform_(Eigen::Vector3d(.0, .0, .0),
                                -vehicle_to_imu_transform.GetTranslation()),
      projection_transformation_(
          std::make_unique<ProjectionTransformation>(projection)),
      use_rtk_pose_forward_(use_rtk_pose_forward),
      use_autopilot_mode_(use_autopilot_mode),
      gravity_(.0, .0, -9.8),
      projection_(projection) {}

std::shared_ptr<drivers::gnss::Ins> InsAdapter::GetIns(
    const drivers::gnss::SensorsIns& sensors_ins) {
  auto ins_ptr = std::make_shared<drivers::gnss::Ins>();

  ins_ptr->set_measurement_time(sensors_ins.measurement_time());
  ins_ptr->set_type(InsTypeCast(sensors_ins.type()));

  Eigen::Matrix3d flu_to_enu;

  if (sensors_ins.has_imu_frame_position_llh()) {
    PositionAndEulerAnglesTransform(
        imu_to_vehicle_transform_, projection_transformation_.get(),
        sensors_ins.imu_frame_position_llh(), sensors_ins.roll_pitch_azimuth(),
        /*is_imu_frame=*/true, sensors_ins.reference_coodinate_system(),
        &flu_to_enu, ins_ptr->mutable_position(),
        ins_ptr->mutable_euler_angles());

  } else if (sensors_ins.has_vehicle_frame_position_llh()) {
    PositionAndEulerAnglesTransform(
        imu_to_vehicle_transform_, projection_transformation_.get(),
        sensors_ins.vehicle_frame_position_llh(),
        sensors_ins.roll_pitch_azimuth(), /*is_imu_frame=*/false,
        sensors_ins.reference_coodinate_system(), &flu_to_enu,
        ins_ptr->mutable_position(), ins_ptr->mutable_euler_angles());
  }

  SetCovariance(sensors_ins.position_covariance(),
                ins_ptr->mutable_position_covariance());
  for (int i = 0; i < sensors_ins.euler_angles_covariance_size(); ++i) {
    double cov_deg = sensors_ins.euler_angles_covariance(i);
    ins_ptr->add_euler_angles_covariance(kDegToRad * kDegToRad * cov_deg);
  }

  SetCovariance(sensors_ins.linear_velocity_covariance(),
                ins_ptr->mutable_linear_velocity_covariance());

  ins_ptr->set_map_collection(true);
  return ins_ptr;
}
}  // namespace localization
}  // namespace deeproute
