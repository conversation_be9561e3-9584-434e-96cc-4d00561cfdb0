#include "data_adapter/convert_pose_utils.h"

#include "joint/kinematics/attached_body_kinematics.h"

namespace deeproute {

void VelocityTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    const common::Point3D& angular_velocity,
    const common::Point3D& linear_velocity_enu,
    const Eigen::Matrix3d& flu_to_enu, bool is_imu_frame,
    common::Point3D* vehicle_linear_velocity_flu) {
  Vector6d vehicle_frame_velocity;
  Vector6d velocity;
  velocity << angular_velocity.x(), angular_velocity.y(), angular_velocity.z(),
      linear_velocity_enu.x(), linear_velocity_enu.y(), linear_velocity_enu.z();
  // enu to flu
  velocity.tail<3>() = flu_to_enu.transpose() * velocity.tail<3>();
  if (is_imu_frame) {
    // imu to vehicle linear velocity.
    kinematics::AttachedBodyFrameVelocity(velocity, imu_to_vehicle_transform,
                                          &vehicle_frame_velocity);
  } else {
    vehicle_frame_velocity = velocity;
  }
  vehicle_linear_velocity_flu->set_x(vehicle_frame_velocity[3]);
  vehicle_linear_velocity_flu->set_y(vehicle_frame_velocity[4]);
  vehicle_linear_velocity_flu->set_z(vehicle_frame_velocity[5]);
}

void ImuToVehicleAccelerationTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    const Vector3d& gravity, const common::Point3D& angular_velocity,
    const common::Point3D& imu_frame_linear_acceleration,
    const Eigen::Matrix3d& flu_to_enu, bool has_gravity,
    common::Point3D* vehicle_frame_linear_acceleration) {
  Vector6d imu_frame_velocity;
  imu_frame_velocity << angular_velocity.x(), angular_velocity.y(),
      angular_velocity.z(), .0, .0, .0;

  Vector6d imu_frame_acceleration;
  imu_frame_acceleration << .0, .0, .0, imu_frame_linear_acceleration.x(),
      imu_frame_linear_acceleration.y(), imu_frame_linear_acceleration.z();
  // remove gravity.
  if (has_gravity) {
    imu_frame_acceleration.tail<3>() += flu_to_enu.transpose() * gravity;
  }
  Vector6d vehicle_frame_acceleration;
  // imu acc to vehicle acc
  kinematics::AttachedBodyFrameAcceleration(
      imu_frame_velocity, imu_frame_acceleration, imu_to_vehicle_transform,
      &vehicle_frame_acceleration);
  vehicle_frame_linear_acceleration->set_x(vehicle_frame_acceleration[3]);
  vehicle_frame_linear_acceleration->set_y(vehicle_frame_acceleration[4]);
  vehicle_frame_linear_acceleration->set_z(vehicle_frame_acceleration[5]);
}

}  // namespace deeproute
