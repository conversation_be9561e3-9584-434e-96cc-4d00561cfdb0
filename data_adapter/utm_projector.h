#pragma once

#include "common/geometry.pb.h"
#include "drivers/gnss/ins.pb.h"

#include "data_adapter/projection_transformation.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

using RefSystem = drivers::gnss::SensorsIns::ReferenceCoordinateSystem;

void PositionAndEulerAnglesTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    ProjectionTransformation* projection_transformation,
    const common::PointLLH& imu_frame_position_llh,
    const common::Point3D& roll_pitch_azimuth, bool is_imu_frame,
    const RefSystem& ref_system, Eigen::Matrix3d* flu_to_enu,
    common::Point3D* vehicle_frame_position, common::Point3D* euler_angles_rad);

}  // namespace localization
}  // namespace deeproute
