package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")

cc_library(
  name = "projection_transformation",
  hdrs = ["projection_transformation.h"],
  srcs = ["projection_transformation.cpp"],
  deps = [
        "@geographiclib//:transverse_mercator",
        "@common//common:types",
        "@common//transform:transformation",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
  ],
)

cc_library(
  name = "utm_projector",
  hdrs = ["utm_projector.h"],
  srcs = ["utm_projector.cpp"],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/common:geometry_proto_cc",
        ":projection_transformation",
        "//joint:ll_utils",
        "//joint:math",
        "@common//common:geometry",
  ],
)

cc_library(
  name = "ins_adapter",
  hdrs = ["ins_adapter.h"],
  srcs = ["ins_adapter.cpp"],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        ":convert_pose_utils",
        ":utm_projector",
        "//joint/kinematics:attached_body_kinematics",
        "//joint:math",
        "@lam_common//lam_common/client:region_server_client",
        "@lam_common//lam_common:geometry_proto_utils",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@common//transform:transformation",
  ],
)

cc_library(
  name = "convert_pose_utils",
  hdrs = ["convert_pose_utils.h"],
  srcs = ["convert_pose_utils.cpp"],
  deps = [
        "@common//proto/common:geometry_proto_cc",
        "//joint/kinematics:attached_body_kinematics",
        "//joint:math",
        "@common//transform:transformation",
        "@common//common:geometry",
        "@common//common:types",
  ],
)

