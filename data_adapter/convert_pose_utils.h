#pragma once

#include "common/geometry.pb.h"

#include "common/types.h"
#include "transform/transformation.h"

namespace deeproute {

void VelocityTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    const common::Point3D& angular_velocity,
    const common::Point3D& linear_velocity_enu,
    const Eigen::Matrix3d& flu_to_enu, bool is_imu_frame,
    common::Point3D* vehicle_linear_velocity_flu);

void ImuToVehicleAccelerationTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    const Vector3d& gravity, const common::Point3D& angular_velocity,
    const common::Point3D& imu_frame_linear_acceleration,
    const Eigen::Matrix3d& flu_to_enu, bool has_gravity,
    common::Point3D* vehicle_frame_linear_acceleration);

}  // namespace deeproute
