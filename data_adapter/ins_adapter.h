#pragma once

#include "drivers/gnss/ins.pb.h"
#include "lam_common/projection.pb.h"

#include "data_adapter/projection_transformation.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

class InsAdapter {
 public:
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
  InsAdapter() {};

  InsAdapter(const proto::Projection& projection,
             const ::common::Transformation3& vehicle_to_imu_transform,
             const bool use_rtk_pose_forward = false,
             const bool use_autopilot_mode = true);

  std::shared_ptr<drivers::gnss::Ins> GetIns(
      const drivers::gnss::SensorsIns& sensors_ins);

 private:
  bool online_acquire_projection_ = false;
  bool online_acquire_finished_ = false;
  ::common::Transformation3 imu_to_vehicle_transform_;
  std::unique_ptr<ProjectionTransformation> projection_transformation_;
  bool use_rtk_pose_forward_;
  bool use_autopilot_mode_;
  Vector3d gravity_;
  proto::Projection projection_;
};

}  // namespace localization
}  // namespace deeproute
