// Copyright 2019 DeepRoute.ai. All Rights Reserved.
#pragma once

#include <memory>

#include "lam_common/projection.pb.h"

#include "common/types.h"
#include "third_party/geographiclib/TransverseMercator.hpp"

namespace deeproute {
namespace localization {

class ProjectionTransformation {
 public:
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW

  ProjectionTransformation(double latitude_rad, double longitude_rad,
                           double scale_factor);

  explicit ProjectionTransformation(const proto::Projection& projection);

  void LatLonToUtm(double latitude_deg, double longitude_deg, double* x,
                   double* y, double* meridian_convergence = nullptr,
                   double* projection_scale = nullptr) const;

  void UtmToLatLon(double x, double y, double* latitude_deg,
                   double* longitude_deg,
                   double* meridian_convergence = nullptr,
                   double* projection_scale = nullptr) const;

  Vector3d BlhToUtm(const Vector3d& position_blh_rad);

  Vector3d UtmToBlh(const Vector3d& position_utm_m);

 private:
  double projection_y0_;

  double central_meridian_;

  std::unique_ptr<::common::third_party::GeographicLib::TransverseMercator>
      transverse_mercator_;
};

}  // namespace localization
}  // namespace deeproute
