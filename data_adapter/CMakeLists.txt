add_library(localization_data_adapter
        # convert_pose_utils should in the folder joint/utils，
        # but it will cause circular references
        convert_pose_utils.cpp
        ins_adapter.cpp
        utm_projector.cpp
        projection_transformation.cpp)

target_link_libraries(localization_data_adapter
        Common::proto
        joint
        lane_localization_proto
        ${PCL_LIBRARIES}
        )

install(TARGETS 
        localization_data_adapter
        # localization_data_adapter_with_os_interface
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
        )