// Copyright 2019 DeepRoute.ai. All Rights Reserved.

#include "data_adapter/projection_transformation.h"

namespace deeproute {
namespace localization {

constexpr double kRadToDeg = 180 / M_PI;
constexpr double kDegToRad = M_PI / 180;
namespace GeographicLib = ::common::third_party::GeographicLib;

ProjectionTransformation::ProjectionTransformation(double latitude_rad,
                                                   double longitude_rad,
                                                   double scale_factor)
    : projection_y0_(0.0),
      central_meridian_(longitude_rad * kRadToDeg),
      transverse_mercator_(std::make_unique<GeographicLib::TransverseMercator>(
          GeographicLib::Constants::WGS84_a(),
          GeographicLib::Constants::WGS84_f(), scale_factor)) {
  double projection_x = 0.0;
  transverse_mercator_->Forward(central_meridian_, latitude_rad * kRadToDeg,
                                longitude_rad * kRadToDeg, projection_x,
                                projection_y0_);
}

ProjectionTransformation::ProjectionTransformation(
    const proto::Projection& projection)
    : central_meridian_(projection.longitude_deg()),
      transverse_mercator_(std::make_unique<GeographicLib::TransverseMercator>(
          GeographicLib::Constants::WGS84_a(),
          GeographicLib::Constants::WGS84_f(), projection.scale_factor())) {
  double projection_x = 0.0;
  transverse_mercator_->Forward(central_meridian_, projection.latitude_deg(),
                                projection.longitude_deg(), projection_x,
                                projection_y0_);
}

void ProjectionTransformation::LatLonToUtm(double latitude_deg,
                                           double longitude_deg, double* x,
                                           double* y,
                                           double* meridian_convergence,
                                           double* projection_scale) const {
  double gamma = 0.0;
  double k = 0.0;
  transverse_mercator_->Forward(central_meridian_, latitude_deg, longitude_deg,
                                *x, *y, gamma, k);

  *y -= projection_y0_;
  if (meridian_convergence != nullptr) {
    *meridian_convergence = gamma;
  }
  if (projection_scale != nullptr) {
    *projection_scale = k;
  }
}

void ProjectionTransformation::UtmToLatLon(double x, double y,
                                           double* latitude_deg,
                                           double* longitude_deg,
                                           double* meridian_convergence,
                                           double* projection_scale) const {
  double gamma = 0.0;
  double k = 0.0;
  transverse_mercator_->Reverse(central_meridian_, x, y + projection_y0_,
                                *latitude_deg, *longitude_deg, gamma, k);

  if (meridian_convergence != nullptr) {
    *meridian_convergence = gamma;
  }
  if (projection_scale != nullptr) {
    *projection_scale = k;
  }
}

Vector3d ProjectionTransformation::BlhToUtm(const Vector3d& position_blh_rad) {
  Vector3d position_blh_deg(position_blh_rad[0] * kRadToDeg,
                            position_blh_rad[1] * kRadToDeg,
                            position_blh_rad[2]);
  double projection_x = 0.0;
  double projection_y = 0.0;
  transverse_mercator_->Forward(central_meridian_, position_blh_deg[0],
                                position_blh_deg[1], projection_x,
                                projection_y);
  Vector3d position_utm_m;
  position_utm_m << projection_x, projection_y - projection_y0_,
      position_blh_deg[2];
  return position_utm_m;
}

Vector3d ProjectionTransformation::UtmToBlh(const Vector3d& position_utm_m) {
  Vector3d translation = position_utm_m;
  translation[1] = translation[1] + projection_y0_;

  double latitude = 0.0;
  double longitude = 0.0;
  transverse_mercator_->Reverse(central_meridian_, translation[0],
                                translation[1], latitude, longitude);

  Vector3d position_blh_deg(latitude, longitude, translation[2]);
  Vector3d position_blh_rad = Vector3d::Zero();
  position_blh_rad.head<2>(0) = position_blh_deg.head<2>(0) * kDegToRad;
  position_blh_rad(2) = position_blh_deg(2);
  return position_blh_rad;
}

}  // namespace localization
}  // namespace deeproute
