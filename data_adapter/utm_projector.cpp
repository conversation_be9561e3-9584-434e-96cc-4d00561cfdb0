#include "data_adapter/utm_projector.h"

#include "common/log.h"
#include "joint/ll_utils.h"
#include "joint/math.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

void PositionAndEulerAnglesTransform(
    const ::common::Transformation3& imu_to_vehicle_transform,
    ProjectionTransformation* projection_transformation,
    const common::PointLLH& imu_frame_position_llh,
    const common::Point3D& roll_pitch_azimuth, bool is_imu_frame,
    const RefSystem& ref_system, Eigen::Matrix3d* flu_to_enu,
    common::Point3D* vehicle_frame_position,
    common::Point3D* euler_angles_rad) {
  double lat = 0;
  double lon = 0;
  if (ref_system == RefSystem::SensorsIns_ReferenceCoordinateSystem_GCJ02) {
    Gcj02ToWgs84(imu_frame_position_llh.lon(), imu_frame_position_llh.lat(),
                 &lon, &lat);
  } else if (ref_system ==
             RefSystem::SensorsIns_ReferenceCoordinateSystem_WGS84) {
    lat = imu_frame_position_llh.lat();
    lon = imu_frame_position_llh.lon();
  } else {
    MLOG(WARN)
        << "Reference system is not set, this should not happen, use WGS84;";
    lat = imu_frame_position_llh.lat();
    lon = imu_frame_position_llh.lon();
  }

  double x = 0;
  double y = 0;
  double gamma = 0;
  projection_transformation->LatLonToUtm(lat, lon, &x, &y, &gamma);
  euler_angles_rad->set_x(degToRad(roll_pitch_azimuth.x()));
  euler_angles_rad->set_y(-degToRad(roll_pitch_azimuth.y()));
  euler_angles_rad->set_z(degToRad(90 - roll_pitch_azimuth.z() + gamma));
  const Eigen::Matrix3d enu_to_utm =
      ::common::Transformation3(0, 0, 0, euler_angles_rad->x(),
                                euler_angles_rad->y(), euler_angles_rad->z())
          .GetRotationMatrix();
  if (flu_to_enu != nullptr) {
    *flu_to_enu = ::common::Transformation3(
                      0, 0, 0, euler_angles_rad->x(), euler_angles_rad->y(),
                      degToRad(90 - roll_pitch_azimuth.z()))
                      .GetRotationMatrix();
  }
  Eigen::Vector3d position(x, y, imu_frame_position_llh.height());
  if (is_imu_frame) {
    position =
        position + enu_to_utm * imu_to_vehicle_transform.GetTranslation();
  }
  vehicle_frame_position->set_x(position[0]);
  vehicle_frame_position->set_y(position[1]);
  vehicle_frame_position->set_z(position[2]);
}

}  // namespace localization
}  // namespace deeproute
