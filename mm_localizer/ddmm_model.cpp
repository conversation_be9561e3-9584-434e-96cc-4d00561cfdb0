#ifdef DREN<PERSON>NE
#pragma message("macro DRENGINE defined. Ignore this message")
#endif

#ifdef TARGET_DEVICE_CUDA
#pragma message("macro TARGET_DEVICE_CUDA defined. Ignore this message")
#endif

#ifdef DR_QUALCOMM
#pragma message("macro DR_QUALCOMM defined. Ignore this message")
#endif

#ifdef DR_NVIDIA
#pragma message("macro DR_NVIDIA defined. Ignore this message")

#include "ddmm_model.h"

#include <sys/utsname.h>

#include <cmath>
#include <cstdint>
#include <cstdlib>
#include <iostream>
#include <string>

#include <Eigen/Core>
#include <boost/filesystem.hpp>
#include <opencv2/imgproc.hpp>

#include "map/sd_map.pb.h"

#include "base/deeproute_path.h"
#include "common/event_log_handle.h"
#include "common/log.h"
#include "common/time.h"
#include "common/types.h"
#include "ddmm_model_post.h"
#include "geometry/geometry.h"
#include "mm_localizer/ddmm_data_generate.h"

namespace deeproute {
namespace localization {

namespace {
using deeproute::base::GetDeeproutePath;

const std::unordered_map<uint64_t, double> GetDrLinkIdToUtmHeadingMap(
    const deeproute::sd_map::SdLinks& local_sd_map) {
  std::unordered_map<uint64_t, double> dr_link_id_to_utm_heading_map;

  for (const auto& link : local_sd_map.links()) {
    const auto dr_link_id = link.dr_link_id();

    if (link.points_size() < 2) {
      continue;
    }

    const Eigen::Vector2d link_first_pt(link.points()[0].lon(),
                                        link.points()[0].lat());
    const Eigen::Vector2d link_last_pt(link.points()[1].lon(),
                                       link.points()[1].lat());

    MLOG(INFO) << "ddmm debug link id: " << dr_link_id
               << ", first pt: " << link_first_pt.transpose()
               << ", last pt: " << link_last_pt.transpose();

    Eigen::Vector2d link_heading = link_last_pt - link_first_pt;
    const double link_heading_deg =
        std::atan2(link_heading(1), link_heading(0));
    dr_link_id_to_utm_heading_map[dr_link_id] = link_heading_deg;
  }

  return dr_link_id_to_utm_heading_map;
}

cv::Mat GetSegPtFromeImage(const cv::Mat& seg_image, cv::Point& pred_uv) {
  cv::Mat binary_image;
  double max_prob = 0;
  double max_area = 0;
  cv::Point max_loc;
  cv::minMaxLoc(seg_image, nullptr, &max_prob, nullptr, &max_loc);
  cv::threshold(seg_image, binary_image, max_prob * 0.75, max_prob,
                cv::THRESH_BINARY);

  std::vector<cv::Point> max_roi;
  std::vector<std::vector<cv::Point>> contours;
  cv::findContours(binary_image, contours, cv::RETR_EXTERNAL,
                   cv::CHAIN_APPROX_SIMPLE);
  MLOG(DEBUG) << "max prob = " << max_prob;
  for (const auto& contour : contours) {
    double area = cv::contourArea(contour);
    MLOG(DEBUG) << "area = " << area;
    if (area > max_area) {
      max_area = area;
      max_roi = contour;
    }
  }

  if (max_prob > 0 || !max_roi.empty()) {
    cv::Moments maxMoments = cv::moments(max_roi);
    pred_uv.x = maxMoments.m10 / maxMoments.m00;
    pred_uv.y = maxMoments.m01 / maxMoments.m00;

    if (pred_uv.x <= 0 || pred_uv.y <= 0 || pred_uv.x > IMAGE_SIZE ||
        pred_uv.y > IMAGE_SIZE) {
      MLOG(INFO) << "predict uv = " << pred_uv
                 << ",contours size = " << contours.size();
      if (max_prob > 0) {
        pred_uv = max_loc;
        MLOG(INFO) << "predict uv = " << pred_uv;
      } else {
        pred_uv.x = 80;
        pred_uv.y = 80;
        MLOG(INFO) << "predict uv = " << pred_uv;
      }
    }
  } else {
    pred_uv.x = 80;
    pred_uv.y = 80;
    MLOG(INFO) << "predict uv = " << pred_uv;
  }

  cv::cvtColor(binary_image, binary_image, cv::COLOR_GRAY2BGR);
  cv::circle(binary_image, pred_uv, 5, cv::Scalar(0, 255, 0), -1);

  return binary_image;
}

double RegulateAzi(double azi) {
  /* 存储规格化航向/角度 函数返回值  [0, 360) */
  int32_t c = 10;
  while (azi < 0.0f || azi >= 360.0f) {
    if (azi < 0.0f) {
      azi += 360.0f;
    } else {
      azi -= 360.0f;
    }

    if (c < 0) {
      // 防范传入的azi是NAN或±无穷大导致的死循环
      assert(0);
      return 0.0f;
    }
    c--;
  }

  return (azi);
}

// void WriteVectorToFile(const std::vector<float>& data,
//                        const std::string& filename) {
//   std::ofstream file(filename);
//   if (file.is_open()) {
//     for (size_t i = 0; i < data.size(); i++) {
//       file << std::setprecision(4) << data[i] << " ";
//       if ((i + 1) % 160 == 0) {
//         file << std::endl;
//       }
//     }
//     file.close();
//     std::cout << "sucess write to file" << std::endl;
//   } else {
//     std::cout << "can not open file" << std::endl;
//   }
// }

// std::vector<std::pair<float, int>> RunLengthEncode(
//     const std::vector<float>& input) {
//   std::vector<std::pair<float, int>> compressed;

//   if (input.empty()) {
//     return compressed;
//   }

//   float current = input[0];
//   int count = 1;

//   for (size_t i = 1; i < input.size(); ++i) {
//     if (input[i] == current) {
//       count++;
//     } else {
//       compressed.push_back(std::make_pair(current, count));
//       current = input[i];
//       count = 1;
//     }
//   }

//   // Add the last run
//   compressed.push_back(std::make_pair(current, count));

//   return compressed;
// }

std::vector<float> RunLengthDecode(
    const std::vector<std::pair<float, int>>& compressed) {
  std::vector<float> original;

  for (const auto& run : compressed) {
    for (int i = 0; i < run.second; ++i) {
      original.push_back(run.first);
    }
  }

  return original;
}

bool IsDataValid(const std::vector<float>& test_data) {
  if (test_data.empty()) {
    MLOG(WARN) << "Input data is empty!";
    return false;
  }

  float max_value = std::numeric_limits<float>::lowest();
  float min_value = std::numeric_limits<float>::max();
  bool has_nan = false;

  for (const auto& value : test_data) {
    if (std::isnan(value)) {
      has_nan = true;
    }
    if (value > max_value) {
      max_value = value;
    }
    if (value < min_value) {
      min_value = value;
    }
  }

  MLOG(DEBUG) << "Input data size: " << test_data.size();
  MLOG(DEBUG) << "Max value: " << max_value;
  MLOG(DEBUG) << "Min value: " << min_value;
  MLOG(DEBUG) << "Has NaN: " << (has_nan ? "Yes" : "No");
  if (has_nan || max_value > 100 || min_value < -100) {
    return false;
  }

  return true;
}

std::string GetDeviceType() {
  struct utsname systemInfo;
  std::string machine;
  if (uname(&systemInfo) != -1) {
    machine = systemInfo.machine;
    if (machine == "x86_64") {
      MLOG(INFO) << "System architecture: x86_64";
    } else if (machine == "aarch64") {
      MLOG(INFO) << "System architecture: aarch64";
    } else {
      MLOG(FATAL) << "Unknown system architecture";
    }
  } else {
    MLOG(FATAL) << "Failed to retrieve system information";
  }

  return machine;
}

std::string TypeIdToDDMMTypeName(const int id) {
  // enum DDMMStatus { UNINIT, NORMAL, LOST_MAP, ERROR };
  switch (id) {
    case 0:
      return "UNINIT";
    case 1:
      return "NORMAL";
    case 2:
      return "LOST_MAP";
    case 3:
      return "ERROR";
    default:
      return "UNKNOWN";
  }
}

}  // namespace

DDMMModel::DDMMModel(const DdmmConfig& config) : config_(config) {
  // set tune file path
  const char* kAutoTuneReadPath = "TUNE_FILE_PATH_READ";
  const std::string kAutoTuneReadValue =
      GetDeeproutePath() + "/ddmm-model/tune";
  const int ret = setenv(kAutoTuneReadPath, kAutoTuneReadValue.c_str(), 1);
  if (!ret) {
    MLOG(WARN) << "Setting " << kAutoTuneReadPath << " to "
               << kAutoTuneReadValue << " succeeded!";
  } else {
    MLOG(WARN) << "Setting " << kAutoTuneReadPath << " to "
               << kAutoTuneReadValue << " failed!";
  }

  // define input and output
  DRInfer::Shape input_shape(
      {BATCH_SIZE, INPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  DRInfer::Shape reg_out_shape({BATCH_SIZE, 2, 1, 1});
  DRInfer::Shape seg_out_shape(
      {BATCH_SIZE, SEG_OUTPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  std::string input_name = "input_data_0";
  cudaError_t result = cudaStreamCreate(&stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamCreate failed";
  }
  // allocate cuda memory
  result = cudaMallocAsync(
      &input_data_dev_, sizeof(float) * input_shape.CalculateSize(), stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMallocAsync failed";
  }
  result =
      cudaMallocAsync(&reg_output_data_dev_,
                      sizeof(float) * reg_out_shape.CalculateSize(), stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMallocAsync failed";
  }
  result =
      cudaMallocAsync(&seg_output_data_dev_,
                      sizeof(float) * seg_out_shape.CalculateSize(), stream_);

  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMallocAsync failed";
  }

  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
  }
  // get device type
  const std::string kDeviceType = GetDeviceType();
  MLOG(INFO) << "Device type: " << kDeviceType;

  // set model default path
  const std::string kDefaultDlaModelPath =
      GetDeeproutePath() + "/ddmm-model/weights/ddmm.dla";
  // const std::string kDefaultDlaModelPath2 =
  //      boost::filesystem::absolute(
  // boost::filesystem::path(deeproute::base::GetDeeproutePath().c_str()) /
  // boost::filesystem::path("map-engine/config/lock_on_road/ddmm.dla"))
  // .string();
  const std::string kDefaultEngineModelPath =
      GetDeeproutePath() + "/ddmm-model/weights/ddmm_engine_graph.bin";
  const std::string kDefaultCalibFilePath =
      GetDeeproutePath() + "/ddmm-model/weights/ddmm_calib.txt";

  data_gen_ = CreateDDMMDataGen();

  // ORIN DLA
  if (config.use_dla() && kDeviceType == "aarch64") {
    reg_out_name_ = "output_data_0";
    seg_out_name_ = "output_data_1";
    if (!boost::filesystem::exists(kDefaultDlaModelPath)) {
      MLOG(WARN) << "Model not found at: " << kDefaultDlaModelPath;
    } else {
      MLOG(INFO) << "Found model at" << kDefaultDlaModelPath;
    }

    // first create a config
    DRInfer::IEngineCreateConfig* engine_config = DRInfer::CreateEngineConfig();
    engine_config->EnableNvdla(true);

    // set to True and used to create dla_engine
    DRInfer::IEngine* engine = DRInfer::CreateInferenceEngineV2(engine_config);

    if (config.use_calib_file() &&
        boost::filesystem::exists(kDefaultCalibFilePath)) {
      engine_->LoadCalibrationFile(kDefaultCalibFilePath);
      MLOG(INFO) << "DLA + INT8!";
    } else {
      MLOG(INFO) << "DLA + FP16!";
    }

    engine_.reset(engine);
    engine_->SetDevice(1);
    engine_->SkipOptimizations(DRInfer::OPTIMIZATION_MERGE_CONCAT);
    engine_->SetModelFrameworkType(DRInfer::MODEL_FRAMEWORK_PYTORCH);
    engine_->LoadFromOfflineGraph(kDefaultDlaModelPath);
    MLOG(INFO) << "Load DLA model finished!";

    engine_->RegisterMaxInputShape(input_name.c_str(), &input_shape);
    engine_->RegisterInferInputShape(input_name.c_str(), &input_shape);
    engine_->RegisterInputDataType(input_name.c_str(), DRInfer::MODEL_FLOAT);
    engine_->RegisterOutputDataLayer(seg_out_name_.c_str(), 0);
    engine_->RegisterOutputDataLayer(reg_out_name_.c_str(), 0);
    engine_->RegisterOutputDataType(seg_out_name_.c_str(), 0,
                                    DRInfer::MODEL_FLOAT);
    engine_->RegisterOutputDataType(reg_out_name_.c_str(), 0,
                                    DRInfer::MODEL_FLOAT);

    engine_->BuildEngine();
    use_dla_ = true;
    MLOG(INFO) << "Build engine finished!";
  }
  // GPU + FP16
  else {
    use_dla_ = false;
    MLOG(INFO) << "GPU+FP16 mode!";
    reg_out_name_ = "output_data_0";
    seg_out_name_ = "output_data_1";
    if (!boost::filesystem::exists(kDefaultEngineModelPath)) {
      MLOG(FATAL) << "Model not found at: " << kDefaultEngineModelPath;
      common::ReportEvent(dr::common::LOCK_ON_ROAD,
                          dr::common::LOCK_ON_ROAD_INIT_DDMM_MODEL_NOT_FOUND);
    }

    DRInfer::IEngine* engine = DRInfer::CreateInferenceEngine(nullptr);
    engine_.reset(engine);
    engine_->SetModelFrameworkType(DRInfer::MODEL_FRAMEWORK_PYTORCH);
    engine_->SetRunTimeDataType(DRInfer::MODEL_HALF);
    engine_->AllowInputFloatConversion(true);
    engine_->LoadFromOfflineGraph(kDefaultEngineModelPath);

    engine_->RegisterMaxInputShape(input_name.c_str(), &input_shape);
    engine_->RegisterInferInputShape(input_name.c_str(), &input_shape);
    engine_->RegisterOutputDataLayer(reg_out_name_.c_str(), 0);
    engine_->RegisterOutputDataLayer(seg_out_name_.c_str(), 0);
    engine_->RegisterOutputDataType(seg_out_name_.c_str(), 0,
                                    DRInfer::MODEL_FLOAT);
    engine_->RegisterOutputDataType(reg_out_name_.c_str(), 0,
                                    DRInfer::MODEL_FLOAT);

    engine_->AddOptimizations(DRInfer::OPTIMIZATION_MEMORY_REUSE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_MEM_INTENSIVE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_CONV_BN_MERGE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_CONV_RELU_MERGE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_FC_RELU_MERGE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_TENSORFORMAT_TRANSFORM);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_AUTOTUNE);
    engine_->AddOptimizations(DRInfer::OPTIMIZATION_FC_BN_MERGE);
    engine_->AddOptimizations(
        DRInfer::OPTIMIZATION_MULTIPLE_STREAM_SINGLE_BATCH);

    if (config.use_calib_file()) {
      if (boost::filesystem::exists(kDefaultCalibFilePath)) {
        engine_->LoadCalibrationFile(kDefaultCalibFilePath);
      } else {
        MLOG(WARN) << "DDMM model calib file not found, not using int8 mode.";
        common::ReportEvent(
            dr::common::LOCK_ON_ROAD,
            dr::common::LOCK_ON_ROAD_INIT_DDMM_NO_CALIBRATION_FILE_FOUND);
      }
    }

    engine_->BuildEngine();
  }
  DRInfer::ComputeStream s;
  s.stream = stream_;
  engine_->SetComputeStream(s);

  gnss_time_and_poses_queue_ddmm_.set_capacity(200);
  status_ = DDMMStatus::UNINIT;
  latest_debug_image_ = cv::Mat::zeros(3, 3, CV_8UC3);

  char* ddmm_debug = std::getenv("DDMM_DEBUG");
  if (ddmm_debug == nullptr) {
    MLOG(WARN) << "DDMM DEBUG ENV VARIABLE NOT SET";
    ddmm_debug_mode_ = false;
  } else {
    ddmm_debug_mode_ = std::string(ddmm_debug) == "1" ? true : false;
    MLOG(WARN) << "DDMM DEBUG ENV VARIABLE SET TO " << ddmm_debug_mode_;
  }
}

void DDMMModel::SetInferenceFlagAndTrajWindow(const drivers::gnss::Ins& rtk) {
  double x = rtk.position().x();
  double y = rtk.position().y();
  double z = 0;
  double roll = rtk.euler_angles().x();
  double pitch = rtk.euler_angles().y();
  double yaw = rtk.euler_angles().z();
  const ::common::Transformation3 pose(x, y, z, roll, pitch, yaw);
  current_frame_need_inference_ = false;
  if (gnss_time_and_poses_queue_ddmm_.empty()) {
    gnss_time_and_poses_queue_ddmm_.push_back(
        std::make_pair(rtk.measurement_time(), pose));
  } else {
    Eigen::Vector2d last_pose(
        gnss_time_and_poses_queue_ddmm_.back().second.GetTranslation().head(2));
    double delta_pos = std::sqrt(std::pow((x - last_pose(0)), 2) +
                                 std::pow((y - last_pose(1)), 2));

    // const double kDDMMInferInterval = 5;
    const double kDDMMInferInterval = 2;
    if (delta_pos > kDDMMInferInterval) {
      current_frame_need_inference_ = true;
    }
  }

  if (current_frame_need_inference_) {
    gnss_time_and_poses_queue_ddmm_.push_back(
        std::make_pair(rtk.measurement_time(), pose));
  }
}

bool DDMMModel::PrepareData(const deeproute::sd_map::SdLinks& local_sd_map,
                            const perception::NnFrame& rasmap_nn) {
  data_gen_->GenerateInferenceData(local_sd_map, rasmap_nn,
                                   gnss_time_and_poses_queue_ddmm_,
                                   time_to_utm_x_y_);
  return true;
}

namespace {

// double ComputeLinkHeading(const double dx, const double dy) {
//   constexpr double kDegreePerRad = 57.29577951308232;
//   const double degree = std::atan2(dy, dx) * kDegreePerRad;
//   const double result = ((int(degree) + 360) % 360) / kDegreePerRad;
//   return result;
// }

deeproute::sd_map::SdLinks FilterSdmapByDistHeadingAndFLaneNumDiff(
    const drivers::gnss::Ins& rtk, const double trajectory_angle,
    const deeproute::sd_map::SdLinks& local_sd_map,
    const LaneIndexEstimationResult& lane_index_estimation,
    const double dist_diff_threshold, const double heading_diff_threshold,
    const double f_lane_num_diff_ratio_threshold,
    std::unordered_map<uint64_t, double>* dr_link_id_to_utm_heading_map) {
  // const int rasmap_f_lane_num = lane_index_estimation.sd_lane_sum();
  deeproute::sd_map::SdLinks filtered_local_sd_map;

  const Vector2d rtk_pos(rtk.position().x(), rtk.position().y());
  for (const auto& link : local_sd_map.links()) {
    Vector2dVector pts_list;
    double min_dist_to_link = 1e6;
    int min_dist_pt_index = 0;
    for (int idx = 0; idx < link.points_size(); idx++) {
      const auto point = link.points()[idx];
      pts_list.push_back(Vector2d(point.lon(), point.lat()));

      const double p2p_dist = (rtk_pos - pts_list.back()).norm();
      if (p2p_dist < min_dist_to_link) {
        min_dist_to_link = p2p_dist;
        min_dist_pt_index = idx;
      }
    }
    const double dist = Distance(rtk_pos, pts_list);

    // step 1: remove far away links
    if (dist > dist_diff_threshold) {
      continue;
    }

    const int consecutive_pt_index =
        min_dist_pt_index == 0 ? 1 : (min_dist_pt_index - 1);

    double link_heading_rad = 0;
    if (consecutive_pt_index < min_dist_pt_index) {
      const Eigen::Vector2d link_first_pt(
          link.points()[consecutive_pt_index].lon(),
          link.points()[consecutive_pt_index].lat());
      const Eigen::Vector2d link_last_pt(
          link.points()[min_dist_pt_index].lon(),
          link.points()[min_dist_pt_index].lat());

      Eigen::Vector2d link_heading = link_last_pt - link_first_pt;
      link_heading_rad = std::atan2(link_heading(1), link_heading(0));
    } else {
      const Eigen::Vector2d link_first_pt(
          link.points()[min_dist_pt_index].lon(),
          link.points()[min_dist_pt_index].lat());
      const Eigen::Vector2d link_last_pt(
          link.points()[consecutive_pt_index].lon(),
          link.points()[consecutive_pt_index].lat());

      Eigen::Vector2d link_heading = link_last_pt - link_first_pt;
      link_heading_rad = std::atan2(link_heading(1), link_heading(0));
    }

    (*dr_link_id_to_utm_heading_map)[link.dr_link_id()] = link_heading_rad;

    const double heading_diff =
        std::abs(ConstrainAngle((link_heading_rad - trajectory_angle), true));

    // step 2: remove heading different links
    if (heading_diff > heading_diff_threshold * 57.3) {
      continue;
    }

    // step 3: remove f lane num different links
    // const int link_f_lane_num = link.forward_lane_num();
    // const auto rtk_pose_and_rasmap_time_diff =
    //     std::abs(rtk.measurement_time() -
    //              lane_index_estimation.ras_map_time()) /
    //     1e6;
    // if (rasmap_f_lane_num != 0 && link_f_lane_num != 0 &&
    //     rtk_pose_and_rasmap_time_diff < 0.5 &&
    //     std::abs(rasmap_f_lane_num - link_f_lane_num) >= 2) {
    //   continue;
    // }

    *filtered_local_sd_map.add_links() = link;
  }

  return filtered_local_sd_map;
}

}  // namespace

bool DDMMModel::Match(const uint64_t& time, const drivers::gnss::Ins& rtk,
                      const deeproute::sd_map::SdLinks& local_sd_map,
                      const perception::NnFrame& rasmap_nn,
                      const std::string& hmm_link_id,
                      const LaneIndexEstimationResult& lane_index_estimation,
                      LockOnRoadResult& lock_on_road_result,
                      cv::Mat& debug_image,
                      LockOnRoadDdmmDebugInfo& debug_info) {
  // filter sdmap to avoid extra processing

  const auto start_time = std::chrono::system_clock::now();

  std::unordered_map<uint64_t, double> dr_link_id_to_utm_heading_map;
  auto filtered_local_sd_map = FilterSdmapByDistHeadingAndFLaneNumDiff(
      rtk, rtk.euler_angles().z(), local_sd_map, lane_index_estimation, 100,
      M_PI_2, 1, &dr_link_id_to_utm_heading_map);

  if (filtered_local_sd_map.links().empty()) {
    MLOG(WARN) << "DDMM DEBUG filtered_local_sd_map empty!";
    return false;
  }

  const auto time_spot_1 = std::chrono::system_clock::now();

  SetInferenceFlagAndTrajWindow(rtk);
  if (!current_frame_need_inference_) {
    MLOG(WARN) << "Current frame does not need inference; DDMM status = "
               << TypeIdToDDMMTypeName(status_);
    SetLockOnRoadResult(time, filtered_local_sd_map, lock_on_road_result);
    return false;
  }

  MLOG(INFO) << "DDMM DEBUG: prepare data.";
  PrepareData(filtered_local_sd_map, rasmap_nn);

  const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map =
      data_gen_->GetLinkPtsMap();

  if (id_to_pts_map.empty()) {
    if (status_ != DDMMStatus::UNINIT) {
      status_ = DDMMStatus::LOST_MAP;
    }
    MLOG(WARN) << "DDMM DEBUG link id to cv pt map empty. DDMM status = "
               << TypeIdToDDMMTypeName(status_) << ", skip DDMM.";
    SetLockOnRoadResult(time, filtered_local_sd_map, lock_on_road_result);
    return false;
  }

  const auto time_spot_2 = std::chrono::system_clock::now();

  const std::vector<float>& input_data = data_gen_->GetInferenceData();

  std::vector<float> test_data(input_data);
  if (!IsDataValid(test_data)) {
    common::ReportEvent(
        dr::common::LOCK_ON_ROAD,
        dr::common::LOCK_ON_ROAD_RUNTIME_DDMM_INPUT_DATA_INVALID);
    MLOG(INFO) << "DDMM DEBUG: input data invalid";
    return false;
  }

  DRInfer::Shape input_shape(
      {BATCH_SIZE, INPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  DRInfer::Shape reg_out_shape({REG_DIM1, REG_DIM2});
  DRInfer::Shape seg_out_shape(
      {BATCH_SIZE, SEG_OUTPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  std::string input_name = "input_data_0";

  engine_->RegisterInferInputData(input_name.c_str(), test_data.data(), false);
  const auto time_spot_3 = std::chrono::system_clock::now();

  // ------------------------engine related --------------------------
  ::common::Timer timer_infer;
  engine_->Inference(1, nullptr);
  auto infer_time = timer_infer.EndMilli();
  MLOG(INFO) << "inference_time(ms) = " << infer_time;
  // Todo(yms): Inference engine should implement CopyOutputDeviceDataToHost for
  // dla device

  const auto time_spot_4 = std::chrono::system_clock::now();

  uint32_t reg_tmp_size;
  std::vector<float> reg_output_data(reg_out_shape.CalculateSize());
  uint32_t seg_tmp_size;
  std::vector<float> seg_output_data(seg_out_shape.CalculateSize());
  cudaError_t result = cudaError_t::cudaSuccess;
  if (use_dla_) {
    engine_->CopyOutputDeviceDataFromLayer(
        reg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
        (void**)&reg_output_data_dev_, &reg_tmp_size, false);
    result = cudaMemcpyAsync(reg_output_data.data(), reg_output_data_dev_,
                             sizeof(float) * reg_out_shape.CalculateSize(),
                             cudaMemcpyDeviceToHost, stream_);
    if (result != cudaSuccess) {
      MLOG(ERROR) << "cudaMemcpyAsync failed";
    }
    engine_->CopyOutputDeviceDataFromLayer(
        seg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
        (void**)&seg_output_data_dev_, &seg_tmp_size, false);
    result = cudaMemcpyAsync(seg_output_data.data(), seg_output_data_dev_,
                             sizeof(float) * seg_out_shape.CalculateSize(),
                             cudaMemcpyDeviceToHost, stream_);
    if (result != cudaSuccess) {
      MLOG(ERROR) << "cudaMemcpyAsync failed";
    }

  } else {
    void* reg_data_ptr = reg_output_data.data();
    engine_->CopyOutputDeviceDataToHost(reg_out_name_.c_str(), 0,
                                        DRInfer::MODEL_FLOAT, &reg_data_ptr,
                                        &reg_tmp_size, false);
    void* seg_data_ptr = seg_output_data.data();
    engine_->CopyOutputDeviceDataToHost(seg_out_name_.c_str(), 0,
                                        DRInfer::MODEL_FLOAT, &seg_data_ptr,
                                        &seg_tmp_size, false);
  }
  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
    return false;
  }
  const auto time_spot_5 = std::chrono::system_clock::now();

  // ------------------------engine related --------------------------
  MLOG(INFO) << "DDMM DEBUG TIME: " << time << ", RUN DDMM post processing.";
  GetDDMMResult(time, test_data, local_sd_map, filtered_local_sd_map,
                dr_link_id_to_utm_heading_map, hmm_link_id, seg_output_data,
                debug_image);
  SetLockOnRoadResult(time, filtered_local_sd_map, lock_on_road_result);

  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "DDMM finsh,used  "
             << std::chrono::duration<double>(end_time - start_time).count()
             << " seconds.";

  MLOG(INFO) << "ddmm status = " << status_;
  latest_debug_image_ = debug_image;

  if (config_.enable_ddmm_debug_output()) {
    MLOG(DEBUG) << "[DDMM debug out]: " << debug_info.time()
                << ", trajectory_pts size: "
                << debug_info.trajectory_points_size()
                << ", model_input_data_size: "
                << debug_info.model_input_data_size();
  }
  const auto end_time2 = std::chrono::system_clock::now();
  MLOG(INFO)
      << "match finsh,used  "
      << std::chrono::duration<double>(end_time2 - start_time).count()
      << " seconds. t1-t2: "
      << std::chrono::duration<double>(time_spot_2 - time_spot_1).count()
      << " seconds. t2-t3: "
      << std::chrono::duration<double>(time_spot_3 - time_spot_2).count()
      << " seconds. t3-t4: "
      << std::chrono::duration<double>(time_spot_4 - time_spot_3).count()
      << " seconds. t4-t5: "
      << std::chrono::duration<double>(time_spot_5 - time_spot_4).count();

  return true;
}

bool DDMMModel::TestPerformance(const std::vector<float>& input_data) {
  std::vector<float> test_data(input_data);
  if (!IsDataValid(test_data)) {
    return false;
  }

  DRInfer::Shape input_shape(
      {BATCH_SIZE, INPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  DRInfer::Shape reg_out_shape({REG_DIM1, REG_DIM2});
  DRInfer::Shape seg_out_shape(
      {BATCH_SIZE, SEG_OUTPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  std::string input_name = "input_data_0";

  cudaError_t result =
      cudaMemcpyAsync(input_data_dev_, input_data.data(),
                      sizeof(float) * input_shape.CalculateSize(),
                      cudaMemcpyHostToDevice, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }
  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
  }
  MLOG(DEBUG) << "test_data.data() == nullptr? : "
              << (test_data.data() == nullptr);
  // engine_->RegisterInferInputData(input_name.c_str(), input_data_dev, true);
  engine_->RegisterInferInputData(input_name.c_str(), test_data.data(), false);

  engine_->Inference(1, nullptr);
  float inference_time = engine_->GetInferTime();
  MLOG(INFO) << "inference_time = " << inference_time;
  uint32_t reg_tmp_size;
  std::vector<float> reg_output_data(reg_out_shape.CalculateSize());
  engine_->CopyOutputDeviceDataFromLayer(
      reg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
      (void**)&reg_output_data_dev_, &reg_tmp_size);
  result = cudaMemcpyAsync(reg_output_data.data(), reg_output_data_dev_,
                           sizeof(float) * reg_out_shape.CalculateSize(),
                           cudaMemcpyDeviceToHost, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }

  uint32_t seg_tmp_size;
  std::vector<float> seg_output_data(seg_out_shape.CalculateSize());
  engine_->CopyOutputDeviceDataFromLayer(
      seg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
      (void**)&seg_output_data_dev_, &seg_tmp_size);
  result = cudaMemcpyAsync(seg_output_data.data(), seg_output_data_dev_,
                           sizeof(float) * seg_out_shape.CalculateSize(),
                           cudaMemcpyDeviceToHost, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }
  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
  }
  return true;
}

bool DDMMModel::DebugMatch(const LockOnRoadDdmmDebugInfo& debug_input,
                           LockOnRoadResult& lock_on_road_result,
                           cv::Mat& debug_image) {
  // step 1, record
  std::vector<uint64_t> matched_dr_links_bk(matched_dr_links_);
  std::vector<uint64_t> matched_links_bk(matched_links_);
  std::unordered_map<uint64_t, std::pair<double, double>> time_to_utm_x_y_bk(
      time_to_utm_x_y_);

  boost::circular_buffer<
      std::pair<::common::TimeMicro, ::common::Transformation3>>
      debug_gnss_time_and_poses_queue_ddmm;
  debug_gnss_time_and_poses_queue_ddmm.set_capacity(
      gnss_time_and_poses_queue_ddmm_.size());

  for (const auto& time_and_pose : gnss_time_and_poses_queue_ddmm_) {
    debug_gnss_time_and_poses_queue_ddmm.push_back(
        std::make_pair(time_and_pose.first, time_and_pose.second));
  }

  // step2, clear
  matched_links_.clear();
  matched_dr_links_.clear();
  gnss_time_and_poses_queue_ddmm_.clear();

  // recover model input data from debug msg
  MLOG(DEBUG) << "Trajectory pose size: "
              << debug_input.trajectory_points_size()
              << ", sdlink size: " << debug_input.sd_links().links_size()
              << ", model input data size: "
              << debug_input.model_input_data_size();

  // step 3, recover sdmap
  auto local_sd_map = debug_input.sd_links();
  // auto rasmap_nn = debug_input.nn_frame();
  auto rasmap_nn = perception::NnFrame();

  // step 4, recover gnss_time_and_poses_queue_ddmm_
  MLOG(INFO) << "traj size = " << debug_input.trajectory_points_size();
  for (const auto& time_and_pose : debug_input.trajectory_points()) {
    MLOG(INFO) << "time_and_pose first = " << time_and_pose.time();
    gnss_time_and_poses_queue_ddmm_.push_back(std::make_pair(
        time_and_pose.time(),
        ::common::Transformation3(
            time_and_pose.position().x(), time_and_pose.position().y(),
            time_and_pose.position().z(), time_and_pose.euler_angles().x(),
            time_and_pose.euler_angles().y(),
            time_and_pose.euler_angles().z())));
  }

  // step 5, revover matched_links_
  for (const auto& match_link : debug_input.matched_links()) {
    matched_links_.push_back(std::stoull(match_link));
  }
  if (matched_links_.size() >= 1) {
    matched_links_.pop_back();
  }

  // step 6, recover matched_dr_links_
  for (const auto& match_dr_link : debug_input.matched_dr_links()) {
    matched_dr_links_.push_back(std::stoull(match_dr_link));
  }
  if (matched_dr_links_.size() >= 1) {
    matched_dr_links_.pop_back();
  }

  // step 7, run data generation
  // data_gen_->GenerateSDmapTrainingData(local_sd_map,
  //                                      gnss_time_and_poses_queue_ddmm_);
  // data_gen_->GenerateTrajectoryTrainningData(gnss_time_and_poses_queue_ddmm_,
  //                                            time_to_utm_x_y_);

  data_gen_->GenerateInferenceData(local_sd_map, rasmap_nn,
                                   gnss_time_and_poses_queue_ddmm_,
                                   time_to_utm_x_y_);

  const std::vector<float>& gen_input_data = data_gen_->GetInferenceData();

  // step 8, recovert model input data
  std::vector<std::pair<float, int>> compressed_data;
  compressed_data.reserve(debug_input.model_input_data().size());
  for (const auto& data : debug_input.model_input_data()) {
    compressed_data.push_back(std::make_pair(data.value(), data.number()));
  }
  const std::vector<float>& input_data = RunLengthDecode(compressed_data);

  // // debug code, output test_data to file
  // std::string online_file_name = std::to_string(debug_input.time()) +
  // "_on.txt"; std::string online_path = "/home/<USER>/ddmm/test_data/" +
  // online_file_name; WriteVectorToFile(input_data ,online_path); std::string
  // offline_file_name = std::to_string(debug_input.time()) + "_off.txt";
  // std::string offline_path = "/home/<USER>/ddmm/test_data/" + offline_file_name;
  // WriteVectorToFile(gen_input_data ,offline_path);

  if (input_data.size() != gen_input_data.size()) {
    MLOG(ERROR) << "should not happen";
  }
  float max_diff = 0;
  for (size_t i = 0; i < input_data.size(); i++) {
    float diff = fabs(input_data[i] - gen_input_data[i]);
    if (diff > max_diff) {
      max_diff = diff;
    }
  }
  MLOG(INFO) << "max_diff = " << max_diff;

  // compare gen_input_data & input_data
  if (std::equal(gen_input_data.begin(), gen_input_data.end(),
                 input_data.begin(), input_data.end()) == false) {
    MLOG(ERROR) << "The two vectors are not equal.";
  } else {
    MLOG(INFO) << "The two vectors are equal.";
  }

  std::vector<float> test_data(input_data);
  if (!IsDataValid(input_data)) {
    return false;
  }

  // step 9, hmm_link_id
  auto hmm_link_id = lock_on_road_result.sd_link_id();

  // step 10, time
  auto time = debug_input.time();

  // step 11, run model inference
  const auto start_time = std::chrono::system_clock::now();

  DRInfer::Shape input_shape(
      {BATCH_SIZE, INPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  DRInfer::Shape reg_out_shape({REG_DIM1, REG_DIM2});
  DRInfer::Shape seg_out_shape(
      {BATCH_SIZE, SEG_OUTPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  std::string input_name = "input_data_0";

  cudaError_t result =
      cudaMemcpyAsync(input_data_dev_, input_data.data(),
                      sizeof(float) * input_shape.CalculateSize(),
                      cudaMemcpyHostToDevice, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }
  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
  }
  // engine_->RegisterInferInputData(input_name.c_str(), input_data_dev, true);
  engine_->RegisterInferInputData(input_name.c_str(), test_data.data(), false);

  engine_->Inference(1, nullptr);
  float inference_time = engine_->GetInferTime();
  MLOG(INFO) << "inference_time = " << inference_time;
  uint32_t reg_tmp_size;
  std::vector<float> reg_output_data(reg_out_shape.CalculateSize());
  engine_->CopyOutputDeviceDataFromLayer(
      reg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
      (void**)&reg_output_data_dev_, &reg_tmp_size);
  result = cudaMemcpyAsync(reg_output_data.data(), reg_output_data_dev_,
                           sizeof(float) * reg_out_shape.CalculateSize(),
                           cudaMemcpyDeviceToHost, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }
  uint32_t seg_tmp_size;
  std::vector<float> seg_output_data(seg_out_shape.CalculateSize());
  engine_->CopyOutputDeviceDataFromLayer(
      seg_out_name_.c_str(), 0, DRInfer::MODEL_FLOAT,
      (void**)&seg_output_data_dev_, &seg_tmp_size);
  result = cudaMemcpyAsync(seg_output_data.data(), seg_output_data_dev_,
                           sizeof(float) * seg_out_shape.CalculateSize(),
                           cudaMemcpyDeviceToHost, stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaMemcpyAsync failed";
  }
  result = cudaStreamSynchronize(stream_);
  if (result != cudaSuccess) {
    MLOG(ERROR) << "cudaStreamSynchronize failed";
  }

  const auto dr_link_id_to_utm_heading_map =
      GetDrLinkIdToUtmHeadingMap(local_sd_map);
  GetDDMMResult(time, test_data, local_sd_map, local_sd_map,
                dr_link_id_to_utm_heading_map, hmm_link_id, seg_output_data,
                debug_image);
  SetLockOnRoadResult(time, local_sd_map, lock_on_road_result);
  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "DDMM finsh,used  "
             << std::chrono::duration<double>(end_time - start_time).count()
             << " seconds.";
  MLOG(DEBUG) << "ddmm status = " << status_;

  // step12, clear
  matched_links_.clear();
  matched_dr_links_.clear();
  gnss_time_and_poses_queue_ddmm_.clear();

  // step13, restore
  matched_links_ = matched_links_bk;
  matched_dr_links_ = matched_dr_links_bk;

  for (const auto& time_and_pose : debug_gnss_time_and_poses_queue_ddmm) {
    gnss_time_and_poses_queue_ddmm_.push_back(
        std::make_pair(time_and_pose.first, time_and_pose.second));
  }
  time_to_utm_x_y_ = time_to_utm_x_y_bk;

  return true;
}

namespace {
bool GetClosestPointOnLine(const Vector2dVector& line_points,
                           const Vector2d& point, double* min_dist,
                           Vector2d* closest_pt_to_link,
                           double* closest_pt_distance_to_link_start,
                           double* closest_pt_distance_to_link_end) {
  if (line_points.size() < 2) {
    // Handle case where the line does not have enough points
    return false;
  }

  *min_dist = std::numeric_limits<double>::max();

  Vector2d closest = {0, 0};
  double totalDistance = 0.0;
  double minDistanceAlongLine = std::numeric_limits<double>::max();

  for (size_t i = 0; i + 1 < line_points.size(); ++i) {
    Vector2d P1 = line_points[i];
    Vector2d P2 = line_points[i + 1];

    // Calculate the vector representing the line segment
    Vector2d lineVec = P2 - P1;

    // Calculate the vector from P1 to the given point
    Vector2d pointVec = point - P1;

    // Calculate the projection of pointVec onto lineVec
    double t = pointVec.dot(lineVec) / lineVec.squaredNorm();

    // Clamp t to ensure the closest point lies on the line segment
    t = std::max(0.0, std::min(1.0, t));

    // Calculate the closest point on the line segment
    Vector2d closestOnSegment = P1 + t * lineVec;

    // Calculate the distance between the closest point on the segment and the
    // given point
    double dist = (closestOnSegment - point).norm();

    if (dist < *min_dist) {
      *min_dist = dist;
      closest = closestOnSegment;
      minDistanceAlongLine = totalDistance + t * lineVec.norm();
    }

    // Accumulate the distance along the line
    totalDistance += lineVec.norm();
  }

  *closest_pt_to_link = closest;
  *closest_pt_distance_to_link_start = minDistanceAlongLine;
  *closest_pt_distance_to_link_end = totalDistance - minDistanceAlongLine;

  return true;
}
}  // namespace
bool DDMMModel::GetDDMMResult(
    const uint64_t& time, const std::vector<float>& input_data,
    const deeproute::sd_map::SdLinks& local_sd_map,
    const deeproute::sd_map::SdLinks& filtered_local_sd_map,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_utm_heading_map,
    const std::string& hmm_link_id, std::vector<float>& seg_output_data,
    cv::Mat& debug_image) {
  cv::Mat seg_image(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1);

  for (size_t i = 0; i < seg_output_data.size(); ++i) {
    int row = i / IMAGE_SIZE;
    int col = i % IMAGE_SIZE;

    float value = 1.0 / (1.0 + std::exp(-seg_output_data[i]));
    seg_image.at<uint8_t>(row, col) = static_cast<uint8_t>(value * 255);
  }

  cv::Point pred_uv;
  cv::Mat binary_image = GetSegPtFromeImage(seg_image, pred_uv).clone();

  const auto vehicle_global_yaw =
      gnss_time_and_poses_queue_ddmm_.back().second.GetRollPitchYaw().z();

  // Eigen::Vector2d last_utm_position =
  //     gnss_time_and_poses_queue_ddmm_[gnss_time_and_poses_queue_ddmm_.size()
  //     -
  //                                     2]
  //         .second.GetTranslation()
  //         .head(2);

  Eigen::Vector2d curr_utm_position =
      gnss_time_and_poses_queue_ddmm_.back().second.GetTranslation().head(2);

  // bool key_frame = false;

  std::vector<std::vector<cv::Point>> seg_links;
  std::vector<std::vector<cv::Point>> topo_out_links;
  std::vector<std::vector<cv::Point>> candidate_links;
  uint64_t selected_link_id = 0;
  uint64_t selected_dr_link_id = 0;

  if (PostProcessByTopo(filtered_local_sd_map, data_gen_->GetLinkPtsMap(),
                        dr_link_id_to_utm_heading_map, vehicle_global_yaw,
                        seg_image, std::stoull(hmm_link_id), nearest_link_id_,
                        matched_dr_links_, matched_links_, &seg_links,
                        &topo_out_links, &candidate_links, &selected_link_id,
                        &selected_dr_link_id)) {
    MLOG(INFO) << "DDMM DEBUG candidate_links dr links: "
               << candidate_links.size()
               << ", selected_dr_link_id: " << selected_dr_link_id
               << ", selected_link_id: " << selected_link_id;

    nearest_link_id_ = selected_link_id;
    nearest_dr_link_id_ = selected_dr_link_id;

    status_ = DDMMStatus::NORMAL;
  } else {
    status_ = DDMMStatus::ERROR;
  }
  MLOG(INFO) << "DDMM DEBUG post processing finished, status_ = " << status_;

  const uint64_t pos_time = gnss_time_and_poses_queue_ddmm_.back().first;
  // utm_x_ =
  //     (pred_uv.x - IMAGE_SIZE / 2) * RESOLUTION_RATIO + curr_utm_position(0);
  // // utm_y_ =
  // //     (pred_uv.y - IMAGE_SIZE / 2) * RESOLUTION_RATIO +
  // curr_utm_position(1); utm_y_ =
  //     curr_utm_position(1) - (pred_uv.y - IMAGE_SIZE / 2) * RESOLUTION_RATIO;

  Vector2dVector selected_utm_link_pts_2d;
  for (const auto& link : local_sd_map.links()) {
    if (link.dr_link_id() == nearest_dr_link_id_) {
      for (const auto& pt : link.points()) {
        selected_utm_link_pts_2d.push_back(Vector2d(pt.lon(), pt.lat()));
      }
    }
  }

  Vector2d line_ref_pt;
  double distance_to_link_start, distance_to_link_end, min_dist;
  const bool ret = GetClosestPointOnLine(
      selected_utm_link_pts_2d, curr_utm_position, &min_dist, &line_ref_pt,
      &distance_to_link_start, &distance_to_link_end);
  if (!ret) {
    MLOG(WARN) << "DDMM DEBUG: GetClosestPointOnLine failed!";
    return false;
  }

  utm_x_ = line_ref_pt(0);
  utm_y_ = line_ref_pt(1);
  selected_link_info_.time = pos_time;
  selected_link_info_.sd_link_id = nearest_link_id_;
  selected_link_info_.dr_link_id = nearest_dr_link_id_;
  selected_link_info_.link_points_2d_utm = selected_utm_link_pts_2d;
  selected_link_info_.geo_ref_pt_utm = line_ref_pt;
  selected_link_info_.vehicle_pos_utm = curr_utm_position;
  selected_link_info_.dist_to_start = distance_to_link_start;
  selected_link_info_.dist_to_end = distance_to_link_end;

  if (status_ == DDMMStatus::NORMAL) {
    time_to_utm_x_y_.insert(
        std::make_pair(pos_time, std::pair(utm_x_, utm_y_)));
  }
  MLOG(INFO) << "DDMM DEBUG time = " << pos_time
             << ",nearest_link_id_ = " << nearest_link_id_
             << ",x = " << pred_uv.x << ",y = " << pred_uv.y;

  if (hmm_link_id != std::to_string(nearest_link_id_)) {
    // key_frame = true;
    MLOG(INFO) << "hmm_link_id = " << hmm_link_id
               << ",nearest id = " << nearest_link_id_;
  }

  if (ddmm_debug_mode_) {
    GenerateDebugImage(time, input_data, local_sd_map, filtered_local_sd_map,
                       binary_image, seg_image, seg_links, topo_out_links,
                       candidate_links, hmm_link_id, debug_image);
  }

  // clear old time_to_utm_x_y_

  for (auto iter = time_to_utm_x_y_.begin(); iter != time_to_utm_x_y_.end();) {
    bool stay_flag = false;
    for (const auto& time_to_pose : gnss_time_and_poses_queue_ddmm_) {
      if (static_cast<uint64_t>(time_to_pose.first) == iter->first) {
        stay_flag = true;
      }
    }
    if (stay_flag) {
      iter++;
    } else {
      iter = time_to_utm_x_y_.erase(iter);
    }
  }

  return true;
}

void DDMMModel::SetLockOnRoadResult(
    const uint64_t& time, const deeproute::sd_map::SdLinks& local_sd_map,
    LockOnRoadResult& lock_on_road_result) {
  lock_on_road_result.set_time_us(time);
  lock_on_road_result.set_request_id("null");

  if (status_ != DDMMStatus::NORMAL) {
    lock_on_road_result.set_status(
        LockOnRoadResult_Status::LockOnRoadResult_Status_YAWED);
  } else {
    lock_on_road_result.set_status(
        LockOnRoadResult_Status::LockOnRoadResult_Status_NORMAL);
  }

  lock_on_road_result.set_sd_link_id(std::to_string(nearest_link_id_));
  lock_on_road_result.set_dr_link_id(nearest_dr_link_id_);
  lock_on_road_result.mutable_matched_position_wgs84()->set_x(utm_x_);
  lock_on_road_result.mutable_matched_position_wgs84()->set_y(utm_y_);
  lock_on_road_result.mutable_matched_position_wgs84()->set_z(0);

  const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map =
      data_gen_->GetLinkYawsMap();
  auto it = dr_id_to_yaw_map.find(nearest_dr_link_id_);
  if (it != dr_id_to_yaw_map.end()) {
    double azi = RegulateAzi(450.0 - it->second * 180.0 / M_PI);
    lock_on_road_result.set_matched_heading(azi);
  }

  double link_length =
      selected_link_info_.dist_to_start + selected_link_info_.dist_to_end;
  if (status_ == DDMMStatus::NORMAL) {
    for (const auto& link : local_sd_map.links()) {
      if (link.dr_link_id() == nearest_dr_link_id_) {
        link_length = link.length();
      }
    }

    lock_on_road_result.set_sd_distance_to_link_start(
        selected_link_info_.dist_to_start);
    lock_on_road_result.set_sd_distance_to_link_end(
        std::max(0.0, link_length - selected_link_info_.dist_to_start));

    // MLOG(INFO) << "DDMM DEBUG: distance to link start: "
    //            << selected_link_info_.dist_to_start
    //            << ", distanec to link end: " <<
    //            selected_link_info_.dist_to_end
    //            << ", lik id: " << nearest_link_id_
    //            << ", dr link id: " << nearest_dr_link_id_;
  } else {
    lock_on_road_result.set_sd_distance_to_link_start(0);
    lock_on_road_result.set_sd_distance_to_link_end(0);
  }
}

void DDMMModel::GenerateDebugImage(const uint64_t& time,
                                   const std::vector<float>& input_data,
                                   const deeproute::sd_map::SdLinks& local_sd_map,
                                   const deeproute::sd_map::SdLinks& filtered_local_sd_map,
                                   const cv::Mat& binary_image,
                                   const cv::Mat& seg_image,
                                   const std::vector<std::vector<cv::Point>> seg_links,
                                   const std::vector<std::vector<cv::Point>> topo_out_links,
                                   const std::vector<std::vector<cv::Point>> candidate_links,
                                   const std::string& hmm_link_id,
                                   cv::Mat& debug_image/*, uint64_t id_by_dis,
                                   uint64_t id_by_angle*/) {
  cv::Mat sd_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat sd_pts_add_ddmm_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3,
                              cv::Scalar(0, 0, 0));
  cv::Mat sd_pts_add_hmm_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3,
                             cv::Scalar(0, 0, 0));
  cv::Mat sd_ids_img(IMAGE_SIZE, IMAGE_SIZE, CV_16UC1);
  cv::Mat sd_sin_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1, uint8_t(0));
  cv::Mat sd_cos_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1, uint8_t(0));

  cv::Mat tra_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat tra_orders_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1);
  cv::Mat tra_gts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1);
  cv::Mat tra_sin_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1);
  cv::Mat tra_cos_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1);
  cv::Mat bev_img1(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat bev_img2(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat bev_img3(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat sd_lane_num_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat sd_cross_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3,
                           cv::Scalar(0, 0, 0));
  cv::Mat seg_links_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0, 0, 0));
  cv::Mat topo_out_links_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3,
                             cv::Scalar(0, 0, 0));
  cv::Mat candidate_links_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3,
                              cv::Scalar(0, 0, 0));

  tra_pts_img = cv::Mat::zeros(tra_pts_img.size(), tra_pts_img.type());

  int idx = 0;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE;
    int col = i % IMAGE_SIZE;
    sd_pts_img.at<cv::Vec3b>(row, col) = cv::Vec3b(0, 0, input_data[i] * 255);
  }

  // generate sd ids image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 160;
    int col = i % IMAGE_SIZE;
    sd_ids_img.at<uint16_t>(row, col) =
        static_cast<uint16_t>(input_data[i] * 511);
  }
  // generate sd sin image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 320;
    int col = i % IMAGE_SIZE;
    sd_sin_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] * 255);
  }
  // generate sd cos image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 480;
    int col = i % IMAGE_SIZE;
    sd_cos_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] * 255);
  }

  // generate tra pts image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 640;
    int col = i % IMAGE_SIZE;
    tra_pts_img.at<cv::Vec3b>(row, col) = cv::Vec3b(
        input_data[i] * 255, input_data[i] * 255, input_data[i] * 255);
  }

  // generate tra orders image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 800;
    int col = i % IMAGE_SIZE;
    tra_orders_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] + 200);
  }

  // generate tra gts image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 960;
    int col = i % IMAGE_SIZE;
    tra_gts_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] * 255);
  }

  // generate tra sin image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1120;
    int col = i % IMAGE_SIZE;
    tra_sin_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] * 255);
  }

  // generate tra cos image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1280;
    int col = i % IMAGE_SIZE;
    tra_cos_img.at<uint8_t>(row, col) =
        static_cast<uint8_t>(input_data[i] * 255);
  }

  // generate bev1 image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1440;
    int col = i % IMAGE_SIZE;
    bev_img1.at<cv::Vec3b>(row, col) = cv::Vec3b(input_data[i] * 255, 0, 0);
  }

  // generate bev2 image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1600;
    int col = i % IMAGE_SIZE;
    bev_img2.at<cv::Vec3b>(row, col) = cv::Vec3b(0, input_data[i] * 255, 0);
  }

  // generate bev3 image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1760;
    int col = i % IMAGE_SIZE;
    bev_img3.at<cv::Vec3b>(row, col) = cv::Vec3b(0, 0, input_data[i] * 255);
  }

  // generate sd_lane_num_img image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 1920;
    int col = i % IMAGE_SIZE;
    sd_lane_num_img.at<cv::Vec3b>(row, col) =
        cv::Vec3b(0, 0, input_data[i] * 600);
  }

  // generate cross pts image
  idx += 160 * 160;
  for (int i = idx + 0; i < idx + 160 * 160; i++) {
    int row = i / IMAGE_SIZE - 2080;
    int col = i % IMAGE_SIZE;
    sd_cross_pts_img.at<cv::Vec3b>(row, col) =
        cv::Vec3b(0, 0, input_data[i] * 255);
  }

  // draw seg links image
  for (const auto& seg_link : seg_links) {
    for (size_t j = 0; j < seg_link.size() - 1; j++) {
      cv::line(seg_links_img, seg_link[j], seg_link[j + 1],
               cv::Scalar(0, 0, 255), 2);  // red
    }
  }

  // draw topo out links image
  for (const auto& topo_out_link : topo_out_links) {
    for (size_t j = 0; j + 1 < topo_out_link.size(); j++) {
      cv::line(topo_out_links_img, topo_out_link[j], topo_out_link[j + 1],
               cv::Scalar(255, 255, 0), 2);  // red
    }
  }

  // draw candidate links image
  for (const auto& candidate_link_pts : candidate_links) {
    for (size_t j = 0; j + 1 < candidate_link_pts.size(); j++) {
      cv::line(candidate_links_img, candidate_link_pts[j],
               candidate_link_pts[j + 1], cv::Scalar(255, 0, 0), 2);  // red
    }
  }

  Eigen::Vector2d curr_utm_position =
      gnss_time_and_poses_queue_ddmm_.back().second.GetTranslation().head(2);
  double azimuth =
      gnss_time_and_poses_queue_ddmm_.back().second.GetRollPitchYaw()(2);
  Eigen::Matrix2d vehicle_to_utm_R;
  vehicle_to_utm_R << cos(azimuth), sin(azimuth), -sin(azimuth), cos(azimuth);
  const auto& links = filtered_local_sd_map.links();
  bool hmm_link_in_links = false;
  for (int link_idx = 0; link_idx < links.size(); link_idx++) {
    std::vector<cv::Point> cord_list;
    const uint64_t link_id = filtered_local_sd_map.links(link_idx).navinfo_id();

    for (int i = 0; i < filtered_local_sd_map.links(link_idx).points().size();
         i++) {
      const double shpt_utm_x =
          filtered_local_sd_map.links(link_idx).points(i).lon();
      const double shpt_utm_y =
          filtered_local_sd_map.links(link_idx).points(i).lat();
      const int delt_x =
          int((shpt_utm_x - curr_utm_position(0)) / RESOLUTION_RATIO);
      const int delt_y =
          int((shpt_utm_y - curr_utm_position(1)) / RESOLUTION_RATIO);
      const Eigen::Vector2d delt_utm(delt_x, delt_y);
      const Eigen::Vector2d delt_veh = vehicle_to_utm_R * delt_utm;

      const int img_x_cord = delt_veh(0) + IMAGE_SIZE / 2;
      const int img_y_cord = delt_veh(1) + IMAGE_SIZE / 2;
      cord_list.push_back(cv::Point(img_x_cord, img_y_cord));
    }

    if (std::to_string(link_id) != std::to_string(nearest_link_id_)) {
      for (size_t j = 0; j + 1 < cord_list.size(); j++) {
        cv::line(sd_pts_add_ddmm_img, cord_list[j], cord_list[j + 1],
                 cv::Scalar(0, 0, 255), 1);  // red
      }
    } else if (std::to_string(link_id) == std::to_string(nearest_link_id_)) {
      MLOG(INFO) << "DDMM DEBUG, plot nearest link: " << nearest_link_id_;
      for (size_t j = 0; j + 1 < cord_list.size(); j++) {
        cv::line(sd_pts_add_ddmm_img, cord_list[j], cord_list[j + 1],
                 cv::Scalar(0, 255, 0), 2);  // green
      }
    }

    if (std::to_string(link_id) != hmm_link_id) {
      for (size_t j = 0; j + 1 < cord_list.size(); j++) {
        cv::line(sd_pts_add_hmm_img, cord_list[j], cord_list[j + 1],
                 cv::Scalar(0, 0, 255), 1);
      }
    } else {
      for (size_t j = 0; j + 1 < cord_list.size(); j++) {
        cv::line(sd_pts_add_hmm_img, cord_list[j], cord_list[j + 1],
                 cv::Scalar(0, 255, 0), 1);
      }
      hmm_link_in_links = true;
    }
  }

  if (!hmm_link_in_links) {
    MLOG(INFO) << "DDMM DEBUG LOM!=LOR: hmm_link_id = " << hmm_link_id
               << ",ddmm link id = " << nearest_link_id_;
  } else {
    MLOG(INFO) << "DDMM DEBUG LOM==LOR: hmm_link_id = " << hmm_link_id
               << ",ddmm link id = " << nearest_link_id_;
  }

  cv::Mat seg_image_temp;
  seg_image.copyTo(seg_image_temp);
  cv::cvtColor(seg_image_temp, seg_image_temp, cv::COLOR_GRAY2BGR);

  cv::Mat rasmap_nn_img;
  cv::Mat bev_1_gray, bev_2_gray, bev_3_gray;
  cv::cvtColor(bev_img1, bev_1_gray, cv::COLOR_BGR2GRAY);
  cv::cvtColor(bev_img2, bev_2_gray, cv::COLOR_BGR2GRAY);
  cv::cvtColor(bev_img3, bev_3_gray, cv::COLOR_BGR2GRAY);
  std::vector<cv::Mat> channels = {bev_1_gray, bev_2_gray, bev_3_gray};
  cv::merge(channels, rasmap_nn_img);

  double alpha = 0.2;
  cv::Mat rasmap_nn_and_sdmap_img;
  cv::addWeighted(sd_pts_add_hmm_img, alpha, rasmap_nn_img, 1.0 - alpha, 0.0,
                  rasmap_nn_and_sdmap_img);

  cv::Mat left_image;
  cv::add(sd_pts_img, tra_pts_img, left_image);
  std::vector<cv::Mat> seg_mask_list = {
      cv::Mat::zeros(seg_image.size(), seg_image.type()),
      cv::Mat::zeros(seg_image.size(), seg_image.type()), seg_image * 10};
  cv::Mat seg_mask_bgr;
  cv::merge(seg_mask_list, seg_mask_bgr);
  cv::add(left_image, seg_mask_bgr, left_image);

  cv::Mat original_sdmap_image =
      ConvertSdMapToImage(local_sd_map, gnss_time_and_poses_queue_ddmm_);

  cv::Mat seg_link_img_combined;
  cv::add(seg_links_img * 255, tra_pts_img, seg_link_img_combined);
  cv::add(seg_link_img_combined, sd_pts_img, seg_link_img_combined);

  cv::Mat topo_out_links_img_combined;
  cv::add(topo_out_links_img * 255, tra_pts_img, topo_out_links_img_combined);
  cv::add(topo_out_links_img_combined, sd_pts_img, topo_out_links_img_combined);

  cv::Mat candidate_image;
  cv::add(candidate_links_img * 255, tra_pts_img, candidate_image);
  cv::add(candidate_image, sd_pts_img, candidate_image);

  std::vector<cv::Mat> gt_channels = {
      tra_gts_img, cv::Mat::zeros(tra_gts_img.size(), tra_gts_img.type()),
      cv::Mat::zeros(tra_gts_img.size(), tra_gts_img.type())};
  cv::Mat tra_gts_img_bgr;
  cv::merge(gt_channels, tra_gts_img_bgr);
  cv::add(left_image, tra_gts_img_bgr, left_image);

  left_image.rowRange(0, kEgoCarPixelPosition.y - kLomDistThreshold) =
      cv::Scalar(0, 255, 0);
  left_image.rowRange(kEgoCarPixelPosition.y + kLomDistThreshold,
                      left_image.cols) = cv::Scalar(0, 255, 0);

  cv::Mat separator(IMAGE_SIZE, 10, CV_8UC3, cv::Scalar(255, 255, 255));
  cv::hconcat(original_sdmap_image, separator, debug_image);
  cv::hconcat(debug_image, left_image, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, seg_link_img_combined, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, topo_out_links_img_combined, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, candidate_image, debug_image);
  cv::hconcat(debug_image, separator, debug_image);

  cv::hconcat(debug_image, sd_pts_add_ddmm_img, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, sd_pts_add_hmm_img, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, seg_image_temp, debug_image);
  cv::hconcat(debug_image, separator, debug_image);
  cv::hconcat(debug_image, rasmap_nn_and_sdmap_img, debug_image);
}  // namespace localization

std::unique_ptr<DDMMModel> CreateDDMMModel(const DdmmConfig& config) {
  return std::make_unique<DDMMModel>(config);
}

}  // namespace localization
}  // namespace deeproute

#endif
