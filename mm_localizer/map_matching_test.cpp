// Copyright 2019 DeepRoute.ai. All Rights Reserved.

#include "mm_localizer/map_matching.h"

#include <gtest/gtest.h>
#include <network/type.hpp>

#include "proto/lock_on_road_config.pb.h"

#include "common/log.h"
#include "lam_common/types.h"
#include "lane_estimator/hmm.h"
#include "lane_estimator/lane_estimator.h"
#include "mm_localizer/utils.h"
#include "third_party/eigen_checks/gtest.h"

namespace deeproute {
namespace localization {

namespace {}  // namespace

namespace {

FMM::NETWORK::CustomGraph CreateTestGraph() {
  FMM::NETWORK::CustomGraph graph;

  FMM::NETWORK::Edge e1;
  e1.id = 0;
  e1.source = 100;
  e1.target = 101;
  Vector2dVector path1{{0, 0}, {1, 1}};
  e1.geom = ParseTrajetoryToLineString(path1);
  graph.push_back(e1);

  FMM::NETWORK::Edge e2;
  e2.id = 1;
  e2.source = 101;
  e2.target = 102;
  Vector2dVector path2{{1, 1}, {2, 0}};
  e2.geom = ParseTrajetoryToLineString(path2);
  graph.push_back(e2);

  FMM::NETWORK::Edge e3;
  e3.id = 2;
  e3.source = 102;
  e3.target = 103;
  Vector2dVector path3{{2, 0}, {3, 1}};
  e3.geom = ParseTrajetoryToLineString(path3);
  graph.push_back(e3);

  FMM::NETWORK::Edge e4;
  e4.id = 3;
  e4.source = 103;
  e4.target = 104;
  Vector2dVector path4{{3, 1}, {4, -1}};
  e4.geom = ParseTrajetoryToLineString(path4);
  graph.push_back(e4);
  return graph;
}

FMM::NETWORK::CustomGraph CreateTestGraph2() {
  FMM::NETWORK::CustomGraph graph;

  FMM::NETWORK::Edge e1;
  e1.id = 100;
  e1.source = 0;
  e1.target = 1;
  Vector2dVector path1{{0, 0}, {1, 0}};
  e1.geom = ParseTrajetoryToLineString(path1);
  graph.push_back(e1);

  FMM::NETWORK::Edge e2;
  e2.id = 101;
  e2.source = 1;
  e2.target = 2;
  Vector2dVector path2{{1, 0}, {2, 0}};
  e2.geom = ParseTrajetoryToLineString(path2);
  graph.push_back(e2);

  FMM::NETWORK::Edge e3;
  e3.id = 102;
  e3.source = 1;
  e3.target = 3;
  Vector2dVector path3{{1, 0}, {2, -1}};
  e3.geom = ParseTrajetoryToLineString(path3);
  graph.push_back(e3);

  FMM::NETWORK::Edge e4;
  e4.id = 103;
  e4.source = 1;
  e4.target = 4;
  Vector2dVector path4{{1, 0}, {1, -1}};
  e4.geom = ParseTrajetoryToLineString(path4);
  graph.push_back(e4);

  FMM::NETWORK::Edge e5;
  e5.id = 104;
  e5.source = 2;
  e5.target = 5;
  Vector2dVector path5{{2, 0}, {3, 0}};
  e5.geom = ParseTrajetoryToLineString(path5);
  graph.push_back(e5);

  FMM::NETWORK::Edge e6;
  e6.id = 105;
  e6.source = 3;
  e6.target = 6;
  Vector2dVector path6{{2, -1}, {3, -1}};
  e6.geom = ParseTrajetoryToLineString(path6);
  graph.push_back(e6);

  FMM::NETWORK::Edge e7;
  e7.id = 106;
  e7.source = 4;
  e7.target = 7;
  Vector2dVector path7{{1, -1}, {1, -2}};
  e7.geom = ParseTrajetoryToLineString(path7);
  graph.push_back(e7);

  return graph;
}

}  // namespace

// TEST(MapMatchingTest, Test1) {
//   const FMM::NETWORK::CustomGraph custom_map = CreateTestGraph();

//   const auto mm = CreateStMapMatchingModel();

//   mm->SetGlobalPlannedRoutes(custom_map);

//   Vector2dVector traj = {Vector2d(0.1, 0.1), Vector2d(0.9, 0.9),
//                                 Vector2d(2.1, -0.1), Vector2d(3.2, 1.1),
//                                 Vector2d(4.1, -1.2)};

//   constexpr double kVmax = 5;
//   constexpr double kRadius = 10;
//   constexpr double kGpsError = 10;
//   constexpr double kHeadingError = 10;
//   constexpr double kTransistionError = 10;
//   MapMatchingResult mm_result;

//   std::vector<double> headings(traj.size(), 0);
//   PointCandidates last_pose_referenced_candidates;
//   const bool result = mm->Match(traj, headings, kVmax, kRadius, 8, kGpsError,
//                                 kHeadingError, kTransistionError, &mm_result,
//                                 &last_pose_referenced_candidates);
//   EXPECT_EQ(result, true);

//   int index = 0;
//   for (const auto& link : mm_result.cpath) {
//     std::cout << "Link id: " << link << std::endl;

//     EXPECT_EQ(link, index);
//     index += 1;
//   }
// }

TEST(MapMatchingTest, Test2) {
  /**
   * @brief
   *
   *    0   100    1   101  2    104   5
   *    o----------o--------o----------o
   *               | \
   *          103  |   \
   *               |     \  102
   *               |       \     105
   *             4 o         \---------o
   *               |         3         6
   *          106  |
   *               |
   *               |
   *             7 o
   *
   */
  const FMM::NETWORK::CustomGraph custom_map = CreateTestGraph2();

  const auto mm = CreateStMapMatchingModel();

  mm->SetGlobalPlannedRoutes(custom_map);

  Vector2dVector traj = {Vector2d(0, -0.5), Vector2d(0.5, -0.5),
                         Vector2d(1, -0.5), Vector2d(1.5, -0.5),
                         Vector2d(2, -0.5)};

  constexpr double kVmax = 2;
  constexpr double kRadius = 2;
  constexpr double kGpsError = 1;
  constexpr double kHeadingError = 0.25;
  constexpr double kTransistionError = 1;
  MapMatchingResult mm_result;

  std::vector<double> headings(traj.size(), 0);
  PointCandidates last_pose_referenced_candidates;
  const bool result = mm->Match(traj, headings, kVmax, kRadius, 8, kGpsError,
                                kHeadingError, kTransistionError, &mm_result,
                                &last_pose_referenced_candidates);
  EXPECT_EQ(result, true);

  for (const auto& link : mm_result.cpath) {
    std::cout << "Link id: " << link << std::endl;
  }

  Vector2dVector gts;
  gts.push_back(Vector2d(0, 0));
  gts.push_back(Vector2d(0.5, 0));
  gts.push_back(Vector2d(1, 0));
  gts.push_back(Vector2d(1.5, 0));
  gts.push_back(Vector2d(2, 0));
  int idx = 0;
  for (const auto& opt_pt : mm_result.opt_candidate_path) {
    Vector2d pt_res(boost::geometry::get<0>(opt_pt.c.point),
                    boost::geometry::get<1>(opt_pt.c.point));
    EXPECT_EQ(pt_res[0], gts[idx][0]);
    EXPECT_EQ(pt_res[1], gts[idx][1]);
    idx += 1;
  }

}  // namespace localization

}  // namespace localization
}  // namespace deeproute
