#include "log_types.h"
#include "data_adapter/projection_transformation.h"
#include "lam_common/utm_projection_convert.h"
#include "joint/ll_utils.h"

namespace deeproute {
namespace localization {

const std::string LogBase::flags_[LOG_COUNT] = {
    "@NGM",
    "@PATH",
    "@LOR",
    "@RTK",
};

LogBase::LogBase(const LogType type) :type_(type) {

}

LogBase::~LogBase() {

}

void LogBase::UtmToLatLon(const proto::Projection& projection, double x, double y,
                 double& lon, double& lat) {
  ProjectionTransformation proj(projection);
  proj.UtmToLatLon(x, y, &lat, &lon);
}

void LogBase::LatLonToUtm(const proto::Projection& projection, double lon, double lat,
                 double& x, double& y) {
  UtmProjectionConvert utm_projector(projection);
  utm_projector.LatLonToUtm(lon, lat, &x, &y);
}

const char* LogBase::FormatContent() {
    content_.clear();
    ToString(LogBase::flags_[type_]);
    ToString(uint64_t(tick_time_));
    // ToString(local_tick_time_);
    return content_.c_str();
}

LogForNgm::LogForNgm() :LogBase(LogType::LOG_TYPE_NGM), mm_result_(nullptr), rtk_(nullptr), projection_(nullptr) {

}

LogForNgm::~LogForNgm() {

}

const char* LogForNgm::FormatContent() {
    LogBase::FormatContent();
    ToString(pos_[0]);
    ToString(pos_[1]);
    double lat = 0.0;
    double lon = 0.0;
    UtmToLatLon(*projection_, pos_[0], pos_[1], lon, lat);
    ToString(lon);
    ToString(lat);
    ToString(pos_azi_);
    double rtk_acc = std::sqrt((*rtk_)->position_covariance()[0] + (*rtk_)->position_covariance()[1]);
    ToString(gnss_type_);
    ToString((*rtk_)->type());
    ToString(rtk_acc);
    std::int32_t match_success = 1;
    if (mm_result_ == nullptr || mm_result_->opt_candidate_path.empty()) {
      match_success = 0;
      ToString(match_success);
      ToString(0);
      ToString(0);
      ToString(0.0);
      ToString(0);
      ToString(0);
      ToString(0);
      ToString(0.0);
      ToString(0.0);
      ToString(0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(0.0);
      ToString(mm_result_ == nullptr ? 0 : (int32_t)mm_result_->match_status);
      ToString(0);
      ToString(0.0);
      ToString(0.0);
    } else {
      match_success = 1;
      ToString(match_success);
      const FMM::MM::Candidate &match_candiate = mm_result_->opt_candidate_path.back().c;
      /// 11
      ToString(match_candiate.edge->id);
      ToString(match_candiate.edge->index);
      ToString(match_candiate.edge->length);
      ToString(match_candiate.edge->source);
      ToString(match_candiate.edge->target);

      const FMM::MM::CandidateInfo &info =  match_candiate.candidate_infos[match_candiate.candidate_index];
      /// 16
      ToString(match_candiate.index);
      ToString(info.offset);
      ToString(info.dist);
      ToString(info.point_index);
      double x = boost::geometry::get<0>(info.point);
      ToString(x);
      double y = boost::geometry::get<1>(info.point);
      ToString(y);
      UtmToLatLon(*projection_, x, y, lon, lat);
      /// 22
      ToString(lon);
      /// 23
      ToString(lat);
      ToString(info.edge_heading);
      /// 25
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].dist_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].heading_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].continue_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].rank_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].dist_on_route_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].azi_on_route_weight);
      ToString(match_candiate.candidate_weights[match_candiate.candidate_index].on_route_weight);
      /// 32
      ToString(mm_result_ == nullptr ? 0 : (int32_t)mm_result_->match_status);
      ToString(info.is_point_block_);
      ToString(info.road_azi.from);
      ToString(info.road_azi.to);
    }
    ToString("\n");
    return content_.c_str();
}

LogForPath::LogForPath() :LogBase(LogType::LOG_TYPE_PATH), route_response_(nullptr), projection_(nullptr) {

}

LogForPath::~LogForPath() {

}

const char* LogForPath::FormatContent() {
    if (route_response_ == nullptr || projection_ == nullptr) {
        return nullptr;
    }
    LogBase::FormatContent();
    const deeproute::navinfo::Result& result = route_response_->result(0);
    const deeproute::navinfo::Route& route = result.route();
    ToString(route_response_->request_id());
    ToString(route.navi_head().code());
    ToString(route.navi_head().ver());
    ToString(route.dis());
    ToString(route.dur());
    auto size = route.segm().size();
    ToString(size);
    for (const auto& segment : route.segm()) {
        ToString(segment.ni_id());
        ToString(segment.dis());
        ToString(segment.traffic_light());
        ToString(segment.spl());
        ToString(segment.f_lane_num());
        ToString(segment.crossing());
        ToString(segment.bm());
        ToString(segment.arr());
        ToString(segment.pr());
        ToString(segment.tunnel());
        ToString(segment.lane_num());
        ToString(segment.usage());
        FormatLaneInfo(segment);
        FormatPoints(segment);
    }
    ToString("\n");
    return content_.c_str();
}

void LogForPath::FormatLaneInfo(const deeproute::navinfo::Segment& seg) {
    auto lane_size = seg.lanes().size();
    ToString(lane_size);
    for (const auto & lane : seg.lanes()) {
        ToString(lane.arr());
        ToString(lane.la());
        ToString(lane.ra());
        ToString(lane.bm());
        ToString(lane.bus_bm());
        ToString(lane.ln());
    }
}

void LogForPath::FormatPoints(const deeproute::navinfo::Segment& seg) {
    auto point_size = seg.shape_points().size();
    ToString(point_size);
    UtmProjectionConvert utm_projector(*projection_);
    for (const auto& ll : seg.shape_points()) {
        double wgs_lon;
        double wgs_lat;
        Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
        double x;
        double y;
        utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);
        ToString(x);
        ToString(y);
        ToString(wgs_lon);
        ToString(wgs_lat);
    }
}

LogForLOR::LogForLOR() :LogBase(LogType::LOG_TYPE_LOR), lock_on_road_result_(nullptr) {

}

LogForLOR::~LogForLOR() {

}

const char* LogForLOR::FormatContent() {
    if (lock_on_road_result_ == nullptr) {
        return nullptr;
    }
    LogBase::FormatContent();
    ToString(lock_on_road_result_->sd_link_id());
    ToString(lock_on_road_result_->request_id());
    ToString(lock_on_road_result_->sd_distance_to_link_start());
    ToString(lock_on_road_result_->sd_distance_to_link_end());
    ToString((int32_t)lock_on_road_result_->status());
    ToString(lock_on_road_result_->matched_position_wgs84().x());
    ToString(lock_on_road_result_->matched_position_wgs84().y());
    ToString(lock_on_road_result_->matched_position_wgs84().z());
    ToString(lock_on_road_result_->matched_heading());
    ToString("\n");
    return content_.c_str();
}

LogForRTK::LogForRTK() :LogBase(LogType::LOG_TYPE_RTK), rtk_(nullptr) {

}

LogForRTK::~LogForRTK() {

}

const char* LogForRTK::FormatContent() {
    if (rtk_ == nullptr) {
        return nullptr;
    }
    double wgs_lon = 106.4057869;
    double wgs_lat =  29.8056515;
    double dlon, dlat;
    Wgs84ToGcj02(wgs_lon, wgs_lat, &dlon, &dlat);
    LogBase::FormatContent();
    ToString(rtk_->has_position());
    ToString(rtk_->position().x());
    ToString(rtk_->position().y());
    ToString(rtk_->position().z());
    ToString(rtk_->has_position_llh());
    if (rtk_->has_position_llh()) {
        ToString(rtk_->position_llh().lon());
        ToString(rtk_->position_llh().lat());
        ToString(rtk_->position_llh().height());
    }
    FormatRepeatElement(rtk_->position_covariance());
    FormatRepeatElement(rtk_->angular_velocity_covariance());
    FormatRepeatElement(rtk_->linear_acceleration_covariance());
    ToString(rtk_->type());
    ToString("\n");
    return content_.c_str();
    // std::string content = rtk_->Utf8DebugString();
    // std::string content1 = rtk_->SerializeAsString();
}


std::shared_ptr<LogBase> LogFactory::make(const LogType type) {
    std::shared_ptr<LogBase> log_type = nullptr;
    switch (type) {
        case LOG_TYPE_NGM:
            log_type = std::make_shared<LogForNgm>(LogForNgm());
            break;
        case LOG_TYPE_PATH:
            log_type = std::make_shared<LogForPath>(LogForPath());
            break;
        case LOG_TYPE_LOR:
            log_type = std::make_shared<LogForLOR>(LogForLOR());
            break;
        case LOG_TYPE_RTK:
            log_type = std::make_shared<LogForRTK>(LogForRTK());
            break;
        case LOG_COUNT:
            break;
    }
    return log_type;
}

LogManager::LogManager() {
    Init();
}

LogManager::~LogManager() {

}

void LogManager::Init() {
    logs_.resize(LOG_COUNT);
    for (int32_t type = 0; type < LOG_COUNT; type++) {
        logs_[type] = std::move(LogFactory::make((LogType)type));
    }
}

}  // namespace localization
}  // namespace deeproute
