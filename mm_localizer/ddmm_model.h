#pragma once

#ifdef DR_NVIDIA

#include <cuda_runtime.h>
#include <dr_api.h>

#include <memory>

#include <network/type.hpp>

#include "drivers/gnss/ins.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "lock_on_road/lock_on_road_debug.pb.h"
#include "map/sd_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "common/types.h"
#include "ddmm_data_generate.h"

namespace deeproute {
namespace localization {

enum DDMMStatus { UNINIT, NORMAL, LOST_MAP, ERROR };

struct LomSelectedLink {
  uint64_t time;
  uint64_t sd_link_id;
  uint64_t dr_link_id;
  Vector2dVector link_points_2d_utm;
  Vector2d geo_ref_pt_utm;
  Vector2d vehicle_pos_utm;
  double link_length;
  double dist_to_start;
  double dist_to_end;
};

class DDMMModel {
 public:
  ~DDMMModel() {
    cudaFree(input_data_dev_);
    cudaFree(reg_output_data_dev_);
    cudaFree(seg_output_data_dev_);
  };
  explicit DDMMModel(const DdmmConfig& config);

  bool Match(const uint64_t& time, const drivers::gnss::Ins& rtk,
             const deeproute::sd_map::SdLinks& local_sd_map,
             const perception::NnFrame& rasmap_nn,
             const std::string& hmm_link_id,
             const LaneIndexEstimationResult& lane_index_estimation,
             LockOnRoadResult& lock_on_road_result, cv::Mat& debug_image,
             LockOnRoadDdmmDebugInfo& debug_info);

  bool DebugMatch(const LockOnRoadDdmmDebugInfo& debug_input,
                  LockOnRoadResult& lor_result, cv::Mat& debug_image);

  bool ValidFrame() { return current_frame_need_inference_; };
  bool TestPerformance(const std::vector<float>& input_data);

 private:
  bool PrepareData(const deeproute::sd_map::SdLinks& local_sd_map,
                   const perception::NnFrame& rasmap_nn);
  bool GetDDMMResult(
      const uint64_t& time, const std::vector<float>& input_data,
      const deeproute::sd_map::SdLinks& local_sd_map,
      const deeproute::sd_map::SdLinks& filtered_local_sd_map,
      const std::unordered_map<uint64_t, double>& dr_link_id_to_utm_heading_map,
      const std::string& hmm_link_id, std::vector<float>& seg_output_data,
      cv::Mat& debug_image);

  void SetLockOnRoadResult(const uint64_t& time,
                           const deeproute::sd_map::SdLinks& local_sd_map,
                           LockOnRoadResult& lock_on_road_result);

  void GenerateDebugImage(
      const uint64_t& time, const std::vector<float>& input_data,
      const deeproute::sd_map::SdLinks& local_sd_map,
      const deeproute::sd_map::SdLinks& filtered_local_sd_map,
      const cv::Mat& binary_image, const cv::Mat& seg_image,
      const std::vector<std::vector<cv::Point>> seg_links,
      const std::vector<std::vector<cv::Point>> topo_out_links,
      const std::vector<std::vector<cv::Point>> candidate_links,
      const std::string& hmm_link_id, cv::Mat& debug_image);

  void SetInferenceFlagAndTrajWindow(const drivers::gnss::Ins& rtk);

  // void SetRoutingResponse(const FMM::NETWORK::CustomGraph& routing_response)
  // {
  //   for (const auto& segment : routing_response) {
  //     Vector2dVector seg_pts;
  //     for (int i = 0; i < segment.geom.get_num_points(); i++) {
  //       Vector2d seg_pt = {segment.geom.get_x(i), segment.geom.get_y(i)};
  //       seg_pts.push_back(seg_pt);
  //     }

  //     routing_link_id_to_link_points_2d_utm_[segment.id] = seg_pts;
  //   }
  // }

  // Vector2dVector GetRoutingResponseLinkPoints2DUtm(const uint64_t& link_id) {
  //   return routing_link_id_to_link_points_2d_utm_[link_id];
  // }

  std::unique_ptr<DRInfer::IEngine> engine_ = nullptr;
  std::unique_ptr<DDMMDataGen> data_gen_ = nullptr;
  boost::circular_buffer<
      std::pair<::common::TimeMicro, ::common::Transformation3>>
      gnss_time_and_poses_queue_ddmm_;
  std::vector<uint64_t> matched_dr_links_;
  std::vector<uint64_t> matched_links_;
  bool current_frame_need_inference_ = false;
  DDMMStatus status_;
  uint64_t nearest_link_id_ = 0;
  uint64_t nearest_dr_link_id_ = 0;
  double utm_x_, utm_y_;
  std::unordered_map<uint64_t, std::pair<double, double>> time_to_utm_x_y_;
  cv::Mat latest_debug_image_;
  float* input_data_dev_;
  float* reg_output_data_dev_;
  float* seg_output_data_dev_;
  DdmmConfig config_;
  std::string reg_out_name_;
  std::string seg_out_name_;
  cudaStream_t stream_;
  LomSelectedLink selected_link_info_;

  // std::unordered_map<uint64_t, Vector2dVector>
  //     routing_link_id_to_link_points_2d_utm_;

  // debug related
  bool ddmm_debug_mode_ = false;
  bool use_dla_ = false;
};

std::unique_ptr<DDMMModel> CreateDDMMModel(const DdmmConfig& config);

}  // namespace localization
}  // namespace deeproute

#endif
