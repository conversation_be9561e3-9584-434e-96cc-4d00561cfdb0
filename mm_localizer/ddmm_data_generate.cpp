#include "mm_localizer/ddmm_data_generate.h"

#include <snappy.h>

#include <algorithm>
#include <set>
#include <string>
#include <vector>

#include <Eigen/Core>
#include <boost/filesystem.hpp>
#include <boost/filesystem/operations.hpp>
#include <opencv2/core.hpp>
#include <opencv2/core/hal/interface.h>
#include <opencv2/core/types.hpp>
#include <opencv2/imgcodecs.hpp>
#include <opencv2/imgproc.hpp>

#include "map/sd_horizon_provider.pb.h"

#include "common/log.h"

namespace deeproute {
namespace localization {
namespace {

double CalculateSlopeAngle(const Eigen::Vector2d& first_pt,
                           const Eigen::Vector2d& sencond_pt) {
  const Eigen::Vector2d slope = sencond_pt - first_pt;
  double slope_angle = std::atan2(slope(1), slope(0));
  return slope_angle;
}

// bool SetInferenceDataFloat(const cv::Mat& image,
//                            std::vector<float>& inference_data) {
//   for (int i = 0; i < image.rows; i++) {            // 1 C
//     for (int j = 0; j < image.cols; j++) {          // 160 H
//       for (int c = 0; c < image.channels(); c++) {  // 160 W

//         float value = image.at<cv::Vec<float, IMAGE_SIZE>>(i, j)[c];
//         inference_data.emplace_back(value);
//       }
//     }
//   }
//   return true;
// }

bool ParseNnFrameToMat(const perception::NnFrame& frame, cv::Mat& bev) {
  const std::set<std::string> kLayerNames = {"boundary_crosswalk_confidence",
                                             "stopline_curb_confidence",
                                             "centerline_confidence"};
  std::unordered_map<std::string, cv::Mat> bev_map;
  MLOG(INFO) << "frame nn size: " << frame.nn_size();
  for (int i = 0; i < frame.nn_size(); i++) {
    MLOG(INFO) << "frame layer name: " << frame.nn(i).name() << ", i: " << i;
    const auto& layer = frame.nn(i);
    if (kLayerNames.find(layer.name()) == kLayerNames.end()) {
      continue;
    }
    // layer shape: 1 channel height width, road mask has 2 channels
    const int channels = layer.shape(1);
    const int rows = layer.shape(2);
    const int cols = layer.shape(3);
    std::string layer_nn_data;
    bool uncompress_status = snappy::Uncompress(
        layer.nn_data().c_str(), layer.nn_data().length(), &layer_nn_data);

    if (!uncompress_status) {
      MLOG(WARN) << "Cannot uncompress mat: " << layer.name();
      continue;
    }
    std::vector<float> pixel_values;
    pixel_values.reserve(channels * rows * cols);
    for (size_t j = 0; j < layer_nn_data.length(); j++) {
      pixel_values.push_back(float(layer_nn_data.at(j)) / 32.);
    }

    if (channels == 1) {
      cv::Mat nn_layer_img = cv::Mat(pixel_values).clone();
      nn_layer_img = nn_layer_img.reshape(channels, rows);
      bev_map[layer.name()] = nn_layer_img;
    }
  }

  bool is_valid = true;
  // sanity check
  if (bev_map.find("boundary_crosswalk_confidence") == bev_map.end() ||
      bev_map.find("centerline_confidence") == bev_map.end() ||
      bev_map.find("stopline_curb_confidence") == bev_map.end()) {
    MLOG(WARN)
        << "[RASMAP_NN] rasmap key not found, boundary_crosswalk_confidence"
        << (bev_map.find("boundary_crosswalk_confidence") == bev_map.end())
        << ", centerline_confidence: "
        << (bev_map.find("centerline_confidence") == bev_map.end())
        << ", stopline_curb_confidence: "
        << (bev_map.find("stopline_curb_confidence") == bev_map.end());
    bev = cv::Mat(IMAGE_SIZE, IMAGE_SIZE, CV_32FC3, cv::Scalar(0.));
    is_valid = false;
  }

  if (is_valid) {
    cv::Mat original_mat;

    cv::merge(std::vector<cv::Mat>({bev_map["boundary_crosswalk_confidence"],
                                    bev_map["centerline_confidence"],
                                    bev_map["stopline_curb_confidence"]}),
              original_mat);

    int original_height = original_mat.rows;
    int original_width = original_mat.cols;
    int x = 0;
    int y = 0;
    int w = round(140 * original_width / 200);
    int h = original_height;

    cv::Rect roi(x, y, w, h);
    cv::Mat cropped_mat = original_mat(roi);
    int crop_height = cropped_mat.rows;
    int crop_width = cropped_mat.cols;

    float scale_width = 140.f / w;   // 140m / 216 pixel
    float scale_height = 120.f / h;  // 120m / 240 pexel

    int resize_width = int(crop_width * scale_width);
    int resize_height = int(crop_height * scale_height);

    cv::Mat resized_mat;
    cv::resize(cropped_mat, resized_mat, cv::Size(resize_width, resize_height));

    // Calculate the padding required on each side
    int padding_left = int((IMAGE_SIZE - resize_width) / 2);
    int padding_right = IMAGE_SIZE - resize_width - padding_left;
    int padding_top = int((IMAGE_SIZE - resize_height) / 2);
    int padding_bottom = IMAGE_SIZE - resize_height - padding_top;

    cv::copyMakeBorder(resized_mat, bev, padding_top, padding_bottom,
                       padding_left, padding_right, cv::BORDER_CONSTANT,
                       cv::Scalar(0));

    MLOG(INFO) << "bev height = " << bev.rows << ", width = " << bev.cols;

    // TODO ,add test bev
  }

  return is_valid;
}

bool SetInferenceDataFloat_V2(const cv::Mat& image,
                              std::vector<float>& inference_data) {
  inference_data.insert(inference_data.end(), image.ptr<float>(),
                        image.ptr<float>() + image.total());
  return true;
}

// bool CheckPtInImage(const cv::Point& pt1) {
//   bool pt_in_image = true;
//   if (pt1.x < 0 || pt1.x > IMAGE_SIZE || pt1.y < 0 || pt1.y > IMAGE_SIZE) {
//     pt_in_image = false;
//   }
//   return pt_in_image;
// }

}  // namespace

DDMMDataGen::DDMMDataGen() {
  inference_data_.reserve(INPUT_CHANNEL * IMAGE_SIZE * IMAGE_SIZE);
  inference_data_.clear();
  id_to_pts_map_.clear();

  char* ddmm_debug = std::getenv("DDMM_DEBUG");
  if (ddmm_debug == nullptr) {
    MLOG(WARN) << "DDMM DEBUG ENV VARIABLE NOT SET";
    ddmm_debug_mode_ = false;
  } else {
    ddmm_debug_mode_ = std::string(ddmm_debug) == "1" ? true : false;
    MLOG(WARN) << "DDMM DEBUG ENV VARIABLE SET TO " << ddmm_debug_mode_;
  }

  char* ddmm_debug_dir = std::getenv("DDMM_DEBUG_DIR");
  if (ddmm_debug_dir == nullptr) {
    MLOG(WARN) << "DDMM DEBUG ENV DDMM_DEBUG_DIR NOT SET";
    ddmm_debug_out_dir_ = "";
  } else {
    ddmm_debug_out_dir_ = std::string(ddmm_debug_dir);
    MLOG(WARN) << "DDMM DEBUG ENV DDMM_DEBUG_DIR SET TO "
               << ddmm_debug_out_dir_;
  }
}

bool DDMMDataGen::ProcessSdmap(const deeproute::sd_map::SdLinks& local_sd_map) {
  const auto& links = local_sd_map.links();

  for (int link_idx = 0; link_idx < links.size(); link_idx++) {
    uint64_t ni_id = local_sd_map.links(link_idx).navinfo_id();
    auto iter = id_to_two_way_flag_.find(ni_id);
    if (iter == id_to_two_way_flag_.end()) {
      id_to_two_way_flag_.insert(std::make_pair(ni_id, false));
    } else {
      iter->second = true;
    }
  }
  return true;
}

bool DDMMDataGen::GenerateFeatrueMapData(const perception::NnFrame& rasmap_nn,
                                         cv::Mat& bev_img) {
  cv::Mat nn_img;
  bool is_data_valid = true;
  if (!ParseNnFrameToMat(rasmap_nn, nn_img)) {
    MLOG(WARN) << "Parse rasmap_nn error.";
    is_data_valid = false;
    return is_data_valid;
  }
  // bev_img = nn_img.reshape(160, 3);
  bev_img = nn_img;

  MLOG(INFO) << "DEBUG bev img shape: " << bev_img.size() << " " << bev_img.cols
             << " " << bev_img.rows << " " << bev_img.channels();

  return is_data_valid;
}

// cv::Mat ConvertSdMapToImage(
//     const deeproute::sd_map::SdLinks& local_sd_map,
//     const boost::circular_buffer<
//         std::pair<::common::TimeMicro, ::common::Transformation3>>&
//         gnss_time_and_poses_queue_ddmm) {
//   Eigen::Vector2d curr_utm_position =
//       gnss_time_and_poses_queue_ddmm.back().second.GetTranslation().head(2);
//   double azimuth =
//       gnss_time_and_poses_queue_ddmm.back().second.GetRollPitchYaw()(2);
//   Eigen::Matrix2d vehicle_to_utm_R;
//   vehicle_to_utm_R << cos(azimuth), sin(azimuth), -sin(azimuth),
//   cos(azimuth); const auto& links = local_sd_map.links();

//   cv::Mat pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0));
//   for (int link_idx = 0; link_idx < links.size(); link_idx++) {
//     std::vector<cv::Point> cord_list;

//     for (int i = 0; i < local_sd_map.links(link_idx).points().size(); i++) {
//       const double shpt_utm_x = local_sd_map.links(link_idx).points(i).lon();
//       const double shpt_utm_y = local_sd_map.links(link_idx).points(i).lat();
//       const int delt_utm_x =
//           int((shpt_utm_x - curr_utm_position(0)) / RESOLUTION_RATIO);
//       const int delt_utm_y =
//           int((shpt_utm_y - curr_utm_position(1)) / RESOLUTION_RATIO);
//       Eigen::Vector2d delt_utm(delt_utm_x, delt_utm_y);
//       Eigen::Vector2d delt_veh = vehicle_to_utm_R * delt_utm;

//       // TODO(shr): check this part.
//       const int img_x_cord = delt_veh(0) + IMAGE_SIZE / 2;
//       const int img_y_cord = delt_veh(1) + IMAGE_SIZE / 2;
//       cord_list.push_back(cv::Point(img_x_cord, img_y_cord));
//     }

//     for (size_t j = 0; j < cord_list.size() - 1; j++) {
//       // cv::line(pts_img, cord_list[j], cord_list[j + 1],
//       // cv::Scalar(float(255)),
//       //          1);
//       cv::arrowedLine(pts_img, cord_list[j], cord_list[j + 1],
//                       cv::Scalar(255, 0, 0), 1);
//     }
//   }

//   // cv::cvtColor(pts_img, pts_img, cv::COLOR_GRAY2BGR);

//   return pts_img;
// }

namespace {
std::vector<cv::Point> UniformSample(const std::vector<cv::Point>& point_list,
                                     double max_distance) {
  std::vector<cv::Point> sampled_points;
  for (size_t i = 0; i + 1 < point_list.size(); ++i) {
    sampled_points.push_back(point_list[i]);
    double segment_length = cv::norm(point_list[i + 1] - point_list[i]);
    int num_samples =
        std::max(1, static_cast<int>(segment_length / max_distance));
    double delta_x = (point_list[i + 1].x - point_list[i].x) / num_samples;
    double delta_y = (point_list[i + 1].y - point_list[i].y) / num_samples;
    for (int j = 1; j < num_samples; ++j) {
      int new_x = static_cast<int>(point_list[i].x + j * delta_x);
      int new_y = static_cast<int>(point_list[i].y + j * delta_y);
      sampled_points.push_back(cv::Point(new_x, new_y));
    }
  }
  sampled_points.push_back(point_list.back());
  return sampled_points;
}
}  // namespace
cv::Mat ConvertSdMapToImage(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const boost::circular_buffer<
        std::pair<::common::TimeMicro, ::common::Transformation3>>&
        gnss_time_and_poses_queue_ddmm) {
  Eigen::Vector2d curr_utm_position =
      gnss_time_and_poses_queue_ddmm.back().second.GetTranslation().head(2);
  double azimuth =
      gnss_time_and_poses_queue_ddmm.back().second.GetRollPitchYaw()(2);
  Eigen::Matrix2d vehicle_to_utm_R;
  vehicle_to_utm_R << cos(azimuth), sin(azimuth), -sin(azimuth), cos(azimuth);
  const auto& links = local_sd_map.links();

  cv::Mat pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC3, cv::Scalar(0));
  int color_index = 0;
  for (int link_idx = 0; link_idx < links.size(); link_idx++) {
    std::vector<cv::Point> cord_list;
    for (int i = 0; i < local_sd_map.links(link_idx).points().size(); i++) {
      const double shpt_utm_x = local_sd_map.links(link_idx).points(i).lon();
      const double shpt_utm_y = local_sd_map.links(link_idx).points(i).lat();
      const int delt_utm_x =
          int((shpt_utm_x - curr_utm_position(0)) / RESOLUTION_RATIO);
      const int delt_utm_y =
          int((shpt_utm_y - curr_utm_position(1)) / RESOLUTION_RATIO);
      Eigen::Vector2d delt_utm(delt_utm_x, delt_utm_y);
      Eigen::Vector2d delt_veh = vehicle_to_utm_R * delt_utm;

      const int img_x_cord = delt_veh(0) + IMAGE_SIZE / 2;
      const int img_y_cord = delt_veh(1) + IMAGE_SIZE / 2;
      cord_list.push_back(cv::Point(img_x_cord, img_y_cord));
    }

    const auto uniform_sampled_pts = UniformSample(cord_list, 10);

    // 使用 RGB 循环颜色
    cv::Scalar color = cv::Scalar(0, 0, 0);
    if (color_index % 3 == 0) {
      color[2] = 255;  // R
    } else if (color_index % 3 == 1) {
      color[1] = 255;  // G
    } else {
      color[0] = 255;  // B
    }
    color_index++;

    for (size_t j = 0; j + 1 < uniform_sampled_pts.size(); j++) {
      if (cv::Rect(0, 0, 160, 160).contains(uniform_sampled_pts[j]) &&
          cv::Rect(0, 0, 160, 160).contains(uniform_sampled_pts[j + 1])) {
        cv::arrowedLine(pts_img, uniform_sampled_pts[j],
                        uniform_sampled_pts[j + 1], color, 1, cv::LINE_AA, 0,
                        0.5);
      }
    }

    // break;
  }

  return pts_img;
}

namespace {

// bool JudgeLinkValidityByRasmap(const cv::Mat& rasmap,
//                                const std::vector<cv::Point>& link_points,
//                                cv::Mat& debug_image) {
//   std::vector<cv::Mat> rasmap_channels;
//   cv::split(rasmap, rasmap_channels);
//   cv::Mat centerline_channel = rasmap_channels[2].clone();
//   centerline_channel.convertTo(centerline_channel, CV_8UC1);
//   centerline_channel = centerline_channel * 255;

//   double max_confidence = 0;
//   cv::minMaxLoc(centerline_channel, nullptr, &max_confidence);
//   cv::threshold(centerline_channel, centerline_channel, max_confidence * 0.05,
//                 255, cv::THRESH_BINARY);
//   MLOG(INFO) << "DDMM DEBUG max_confidence: " << max_confidence;

//   std::vector<cv::Point> valid_points;
//   cv::findNonZero(centerline_channel, valid_points);

//   if (valid_points.size() < 3) {
//     return false;  // Not enough valid points for convex hull
//   }

//   std::vector<cv::Point> centerline_hull;
//   cv::convexHull(valid_points, centerline_hull, false, true);

//   // Plot contour on the debug image
//   cv::cvtColor(centerline_channel, centerline_channel, cv::COLOR_GRAY2BGR);
//   std::vector<std::vector<cv::Point>> contours = {centerline_hull};
//   cv::drawContours(centerline_channel, contours, 0, cv::Scalar(0, 0, 255), 2);

//   debug_image = centerline_channel;

//   // Check if any link point is inside the convex hull
//   for (const auto& point : link_points) {
//     if (cv::pointPolygonTest(centerline_hull, point, false) > 0) {
//       return true;  // Link point is inside the convex hull
//     }
//   }

//   return false;  // No link point is inside the convex hull
// }
}  // namespace

bool DDMMDataGen::GenerateSDmapTrainingData(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const boost::circular_buffer<
        std::pair<::common::TimeMicro, ::common::Transformation3>>&
        gnss_time_and_poses_queue_ddmm,
    const cv::Mat& bev_img, cv::Mat& pts_img, cv::Mat& ids_img,
    cv::Mat& sin_img, cv::Mat& cos_img, cv::Mat& lane_num_img,
    cv::Mat& cross_pts_img) {
  id_to_pts_map_.clear();
  dr_id_to_yaw_map_.clear();
  id_to_two_way_flag_.clear();
  inference_data_.clear();

  // inference_data_.resize(INPUT_CHANNEL * IMAGE_SIZE * IMAGE_SIZE);
  ProcessSdmap(local_sd_map);

  Eigen::Vector2d curr_utm_position =
      gnss_time_and_poses_queue_ddmm.back().second.GetTranslation().head(2);
  double azimuth =
      gnss_time_and_poses_queue_ddmm.back().second.GetRollPitchYaw()(2);
  Eigen::Matrix2d vehicle_to_utm_R;
  vehicle_to_utm_R << cos(azimuth), sin(azimuth), -sin(azimuth), cos(azimuth);
  // cv::Mat pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1, cv::Scalar(float(0.0)));
  // cv::Mat ids_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1, cv::Scalar(float(0.0)));
  // cv::Mat sin_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1, cv::Scalar(float(0.5)));
  // cv::Mat cos_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1, cv::Scalar(float(0.5)));

  // cv::Mat lane_num_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
  //                      cv::Scalar(float(0.0)));

  // cv::Mat cross_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
  //                       cv::Scalar(float(0.0)));

  const auto& links = local_sd_map.links();
  int valid_link_cnt = 0;

  for (int link_idx = 0; link_idx < links.size(); link_idx++) {
    const int f_lane_num = local_sd_map.links(link_idx).forward_lane_num();
    const int in_link_size =
        local_sd_map.links(link_idx).in_links_data().size();
    const int out_link_size =
        local_sd_map.links(link_idx).out_links_data().size();

    Eigen::Vector2d first_shpt_cord;
    Eigen::Vector2d last_shpt_cord;
    std::vector<cv::Point> cord_list;

    for (int i = 0; i < local_sd_map.links(link_idx).points().size(); i++) {
      const double shpt_utm_x = local_sd_map.links(link_idx).points(i).lon();
      const double shpt_utm_y = local_sd_map.links(link_idx).points(i).lat();
      const int delt_utm_x =
          int((shpt_utm_x - curr_utm_position(0)) / RESOLUTION_RATIO);
      const int delt_utm_y =
          int((shpt_utm_y - curr_utm_position(1)) / RESOLUTION_RATIO);
      Eigen::Vector2d delt_utm(delt_utm_x, delt_utm_y);
      Eigen::Vector2d delt_veh = vehicle_to_utm_R * delt_utm;

      // TODO(shr): check this part.
      const int img_x_cord = delt_veh(0) + IMAGE_SIZE / 2;
      const int img_y_cord = delt_veh(1) + IMAGE_SIZE / 2;
      cord_list.push_back(cv::Point(img_x_cord, img_y_cord));

      if (i == 0) {
        first_shpt_cord << img_x_cord, img_y_cord;
      }
      if (i == local_sd_map.links(link_idx).points().size() - 1) {
        last_shpt_cord << img_x_cord, img_y_cord;
      }
    }

    // -------------------------------------
    // USE RAS MAP TO JUDGE LINK VALIDITY is not a good idea since RAS MAP is
    // not usually aligned accurately with sdmap. cv::Mat debug_img; auto
    // temp_pt_list = links[0].points(); const bool link_valid =
    //     JudgeLinkValidityByRasmap(bev_img, cord_list, debug_img);
    // if(link_valid){
    //   MLOG(INFO) << "DDMM DEBUG CONVEX HULL VALID";
    //   const std::string channel_data = ddmm_debug_out_dir_ + "/channel_data";
    //   if (!boost::filesystem::exists(channel_data)) {
    //     boost::filesystem::create_directories(channel_data);
    //   }
    //   std::string save_path =
    //       channel_data + "/" + std::to_string(cord_list.size()) + ".jpg";
    //   cv::Mat combined;
    //   // cv::cvtColor(debug_img, debug_img, cv::COLOR_GRAY2BGR);
    //   debug_img.convertTo(debug_img, CV_32FC3);
    //   cv::hconcat(bev_img * 255, debug_img, combined);
    //   cv::imwrite(save_path, combined);

    // }
    // else {
    //   MLOG(INFO) << "DDMM DEBUG CONVEX HULL NOT VALID";
    //   continue;
    // }
    // -------------------------------------

    const double yaw = CalculateSlopeAngle(first_shpt_cord, last_shpt_cord);
    cv::Mat link_img(IMAGE_SIZE, IMAGE_SIZE, CV_8UC1, cv::Scalar(0));

    for (size_t j = 0; j + 1 < cord_list.size(); j++) {
      cv::line(pts_img, cord_list[j], cord_list[j + 1], cv::Scalar(float(1.0)),
               1);
      cv::line(link_img, cord_list[j], cord_list[j + 1], cv::Scalar(1), 1);
      cv::line(sin_img, cord_list[j], cord_list[j + 1],
               cv::Scalar(float((sin(yaw) + 1.0) / 2.0)), 1);
      cv::line(cos_img, cord_list[j], cord_list[j + 1],
               cv::Scalar(float((cos(yaw) + 1.0) / 2.0)), 1);
      cv::line(lane_num_img, cord_list[j], cord_list[j + 1],
               cv::Scalar(float(f_lane_num / 10.0)), 1);
    }

    if (in_link_size > 0) {
      cv::circle(cross_pts_img,
                 cv::Point(first_shpt_cord(0), first_shpt_cord(1)), 1,
                 cv::Scalar(float(1.0)), -1);
    }

    if (out_link_size > 0) {
      cv::circle(cross_pts_img, cv::Point(last_shpt_cord(0), last_shpt_cord(1)),
                 1, cv::Scalar(float(1.0)), -1);
    }

    std::vector<cv::Point> points;
    cv::findNonZero(link_img, points);

    if (!points.empty()) {
      valid_link_cnt++;
      id_to_pts_map_.insert(
          std::make_pair(local_sd_map.links(link_idx).navinfo_id(), points));
      dr_id_to_yaw_map_.insert(
          std::make_pair(local_sd_map.links(link_idx).dr_link_id(), yaw));
    }
    for (size_t j = 0; j + 1 < cord_list.size(); j++) {
      cv::line(ids_img, cord_list[j], cord_list[j + 1],
               cv::Scalar(float(valid_link_cnt / 511.0)), 1);
    }
  }

  MLOG(DEBUG) << "id_to_pts_map_ size = " << id_to_pts_map_.size();

  // sdmap_pts_img = pts_img.reshape(160, 1);
  // sdmap_ids_img = ids_img.reshape(160, 1);
  // sdmap_sin_img = sin_img.reshape(160, 1);
  // sdmap_cos_img = cos_img.reshape(160, 1);
  // sdmap_lane_num_img = lane_num_img.reshape(160, 1);
  // sdmap_cross_pts_img = cross_pts_img.reshape(160, 1);

  // SetInferenceDataFloat(sdmap_pts_img, inference_data_);
  // SetInferenceDataFloat(sdmap_ids_img, inference_data_);
  // SetInferenceDataFloat(sdmap_sin_img, inference_data_);
  // SetInferenceDataFloat(sdmap_cos_img, inference_data_);
  // cv::Mat pts_img1 = pts_img.reshape(160, 1);
  // cv::Mat ids_img1 = ids_img.reshape(160, 1);
  // cv::Mat sin_img1 = sin_img.reshape(160, 1);
  // cv::Mat cos_img1 = cos_img.reshape(160, 1);

  // SetInferenceDataFloat(pts_img1, inference_data_);
  // SetInferenceDataFloat(ids_img1, inference_data_);
  // SetInferenceDataFloat(sin_img1, inference_data_);
  // SetInferenceDataFloat(cos_img1, inference_data_);

  // SetInferenceDataFloat_V2(pts_img, inference_data_);
  // SetInferenceDataFloat_V2(ids_img, inference_data_);
  // SetInferenceDataFloat_V2(sin_img, inference_data_);
  // SetInferenceDataFloat_V2(cos_img, inference_data_);

  return true;
}

bool DDMMDataGen::GenerateTrajectoryTrainningData(
    const boost::circular_buffer<
        std::pair<::common::TimeMicro, ::common::Transformation3>>&
        gnss_time_and_poses_queue_ddmm,
    const std::unordered_map<uint64_t, std::pair<double, double>>&
        time_to_utm_x_y,
    cv::Mat& pts_img, cv::Mat& orders_img, cv::Mat& gts_img, cv::Mat& sin_img,
    cv::Mat& cos_img) {
  MLOG(DEBUG) << "gnss_time_and_poses_queue_ddmm size = "
              << gnss_time_and_poses_queue_ddmm.size();
  Eigen::Vector2d curr_utm_position =
      gnss_time_and_poses_queue_ddmm.back().second.GetTranslation().head(2);
  double azimuth =
      gnss_time_and_poses_queue_ddmm.back().second.GetRollPitchYaw()(2);
  Eigen::Matrix2d vehicle_to_utm_R;
  vehicle_to_utm_R << cos(azimuth), sin(azimuth), -sin(azimuth), cos(azimuth);
  MLOG(DEBUG) << "curr_utm_position = " << curr_utm_position.transpose();

  std::vector<cv::Point> cord_list;
  std::vector<cv::Point> history_cord_list;
  for (int i = static_cast<int>(gnss_time_and_poses_queue_ddmm.size() - 1);
       i >=
       std::max(0, static_cast<int>(gnss_time_and_poses_queue_ddmm.size()) -
                       TRAJECTORY_WINDOW_SIZE);
       i--) {
    Eigen::Vector2d tra_pt_utm =
        gnss_time_and_poses_queue_ddmm[i].second.GetTranslation().head(2);
    int delt_utm_x =
        int((tra_pt_utm(0) - curr_utm_position(0)) / RESOLUTION_RATIO);
    int delt_utm_y =
        int((tra_pt_utm(1) - curr_utm_position(1)) / RESOLUTION_RATIO);
    Eigen::Vector2d delt_utm(delt_utm_x, delt_utm_y);
    Eigen::Vector2d delt_veh = vehicle_to_utm_R * delt_utm;

    int img_x_cord = delt_veh(0) + IMAGE_SIZE / 2;
    int img_y_cord = delt_veh(1) + IMAGE_SIZE / 2;
    cord_list.push_back(cv::Point(img_x_cord, img_y_cord));
    // MLOG(DEBUG) << "img_x_cord = " << img_x_cord
    //            << ",img_y_cord = " << img_y_cord;

    if (i == (static_cast<int>(gnss_time_and_poses_queue_ddmm.size()) - 1)) {
      continue;
    }
    uint64_t pos_time = gnss_time_and_poses_queue_ddmm[i].first;
    auto it = time_to_utm_x_y.find(pos_time);
    if (it == time_to_utm_x_y.end()) {
      continue;
    }

    double history_utm_x = it->second.first;
    double history_utm_y = it->second.second;
    int his_delt_utm_x =
        int((history_utm_x - curr_utm_position(0)) / RESOLUTION_RATIO);
    int his_delt_utm_y =
        int((history_utm_y - curr_utm_position(1)) / RESOLUTION_RATIO);
    Eigen::Vector2d history_delt_utm(his_delt_utm_x, his_delt_utm_y);
    Eigen::Vector2d history_delt_veh = vehicle_to_utm_R * history_delt_utm;

    int his_img_x_cord = history_delt_veh(0) + IMAGE_SIZE / 2;
    int his_img_y_cord = history_delt_veh(1) + IMAGE_SIZE / 2;
    MLOG(DEBUG) << "his_img_x_cord = " << his_img_x_cord
                << ",his_img_y_cord = " << his_img_y_cord;
    history_cord_list.push_back(cv::Point(his_img_x_cord, his_img_y_cord));
  }

  for (size_t j = 0; j + 1 < cord_list.size(); j++) {
    cv::line(pts_img, cord_list[j], cord_list[j + 1], cv::Scalar(float(1.0)),
             1);
    cv::line(orders_img, cord_list[j], cord_list[j + 1],
             cv::Scalar(float((j + 1) / 100.0)), 1);

    Eigen::Vector2d pt1(cord_list[j + 1].x, cord_list[j + 1].y);
    Eigen::Vector2d pt2(cord_list[j].x, cord_list[j].y);
    double yaw = CalculateSlopeAngle(pt1, pt2);

    cv::line(sin_img, cord_list[j], cord_list[j + 1],
             cv::Scalar(float((sin(yaw) + 1.0) / 2.0)), 1);
    cv::line(cos_img, cord_list[j], cord_list[j + 1],
             cv::Scalar(float((cos(yaw) + 1.0) / 2.0)), 1);
  }

  // for (int j = 0;
  //      j < static_cast<int>(static_cast<int>(history_cord_list.size()) - 1);
  //      j++) {
  //   cv::line(gts_img, history_cord_list[j], history_cord_list[j + 1],
  //            cv::Scalar(float(1.0)), 1);
  // }

  return true;
}

bool DDMMDataGen::GenerateInferenceData(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const perception::NnFrame& rasmap_nn,
    const boost::circular_buffer<
        std::pair<::common::TimeMicro, ::common::Transformation3>>&
        gnss_time_and_poses_queue_ddmm,
    const std::unordered_map<uint64_t, std::pair<double, double>>&
        time_to_utm_x_y) {
  cv::Mat sdmap_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                        cv::Scalar(float(0.0)));
  cv::Mat sdmap_ids_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                        cv::Scalar(float(0.0)));
  cv::Mat sdmap_sin_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                        cv::Scalar(float(0.5)));
  cv::Mat sdmap_cos_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                        cv::Scalar(float(0.5)));

  cv::Mat sdmap_lane_num_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                             cv::Scalar(float(0.0)));

  cv::Mat sdmap_cross_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                              cv::Scalar(float(0.0)));

  cv::Mat traj_pts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                       cv::Scalar(float(0.0)));
  cv::Mat traj_orders_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                          cv::Scalar(float(0.0)));
  cv::Mat traj_gts_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                       cv::Scalar(float(0.0)));
  cv::Mat traj_sin_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                       cv::Scalar(float(0.5)));
  cv::Mat traj_cos_img(IMAGE_SIZE, IMAGE_SIZE, CV_32FC1,
                       cv::Scalar(float(0.5)));

  cv::Mat bev_img;
  bool featrue_map_valid = GenerateFeatrueMapData(rasmap_nn, bev_img);
  bool sdmap_data_valid = GenerateSDmapTrainingData(
      local_sd_map, gnss_time_and_poses_queue_ddmm, bev_img, sdmap_pts_img,
      sdmap_ids_img, sdmap_sin_img, sdmap_cos_img, sdmap_lane_num_img,
      sdmap_cross_pts_img);
  bool traj_data_valid = GenerateTrajectoryTrainningData(
      gnss_time_and_poses_queue_ddmm, time_to_utm_x_y, traj_pts_img,
      traj_orders_img, traj_gts_img, traj_sin_img, traj_cos_img);

  if (!(sdmap_data_valid && traj_data_valid && featrue_map_valid)) {
    MLOG(INFO) << "sdmap_data_valid = " << sdmap_data_valid
               << ",traj_data_valid = " << traj_data_valid
               << ",featrue_map_valid = " << featrue_map_valid;
    return false;
  }

  // # 0. SD 地图行点【0～1】
  // # 1. SD地图link index【0～n】
  // # 2. 各SD link的正弦【 - 1～1】
  // # 3. 各SD link的余弦【 - 1～1】
  // # 4. 地图轨迹位置点【0～1】
  // # 5. 地图轨迹序列【1～n】
  // # 6. 历史轨迹点对应的真值点【0 ~1
  // # 7. 轨迹序列的正弦【 - 1，1]
  // # 8. 轨迹序列的余弦【 - 1，1】
  // # 9. rasmap_nn 1 [0, 1]
  // # 10. rasmap_nn 2 [0, 1]
  // # 11. rasmap_nn 3 [0, 1]
  // # 12. sdmap lane_num[0, 10]
  // # 13. sdmap cross link pts[0, 1]

  /*channel 0*/ SetInferenceDataFloat_V2(sdmap_pts_img, inference_data_);
  /*channel 1*/ SetInferenceDataFloat_V2(sdmap_ids_img, inference_data_);
  /*channel 2*/ SetInferenceDataFloat_V2(sdmap_sin_img, inference_data_);
  /*channel 3*/ SetInferenceDataFloat_V2(sdmap_cos_img, inference_data_);

  /*channel 4*/ SetInferenceDataFloat_V2(traj_pts_img, inference_data_);
  /*channel 5*/ SetInferenceDataFloat_V2(traj_orders_img, inference_data_);
  /*channel 6*/ SetInferenceDataFloat_V2(traj_gts_img, inference_data_);
  /*channel 7*/ SetInferenceDataFloat_V2(traj_sin_img, inference_data_);
  /*channel 8*/ SetInferenceDataFloat_V2(traj_cos_img, inference_data_);

  // SetInferenceDataFloat(bev_img, inference_data_);
  std::vector<cv::Mat> channels(3);
  cv::split(bev_img, channels);
  /*channel 9*/ SetInferenceDataFloat_V2(channels[0], inference_data_);
  /*channel 10*/ SetInferenceDataFloat_V2(channels[1], inference_data_);
  /*channel 11*/ SetInferenceDataFloat_V2(channels[2], inference_data_);

  /*channel 12*/ SetInferenceDataFloat_V2(sdmap_lane_num_img, inference_data_);
  /*channel 13*/ SetInferenceDataFloat_V2(sdmap_cross_pts_img, inference_data_);

  if (ddmm_debug_mode_ && !ddmm_debug_out_dir_.empty()) {
    cv::Mat concatenated_image;
    cv::hconcat(sdmap_pts_img * 255, sdmap_ids_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, sdmap_sin_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, sdmap_cos_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, traj_pts_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, traj_orders_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, traj_gts_img * 255, concatenated_image);
    cv::hconcat(concatenated_image, traj_sin_img * 255, concatenated_image);

    cv::Mat concatenated_image2;
    cv::hconcat(traj_cos_img, sdmap_lane_num_img * 255, concatenated_image2);

    cv::hconcat(concatenated_image2, channels[0] * 255, concatenated_image2);
    cv::hconcat(concatenated_image2, channels[1] * 255, concatenated_image2);
    cv::hconcat(concatenated_image2, channels[2] * 255, concatenated_image2);
    cv::hconcat(concatenated_image2, sdmap_cross_pts_img * 255,
                concatenated_image2);
    cv::hconcat(concatenated_image2, sdmap_cross_pts_img * 255,
                concatenated_image2);
    cv::hconcat(concatenated_image2, sdmap_cross_pts_img * 255,
                concatenated_image2);

    cv::Mat combined;
    cv::vconcat(concatenated_image, concatenated_image2, combined);

    const std::string channel_data = ddmm_debug_out_dir_ + "/channel_data";
    if (!boost::filesystem::exists(channel_data)) {
      boost::filesystem::create_directories(channel_data);
    }

    std::string save_path = channel_data + "/" +
                            std::to_string(rasmap_nn.timestamp()) +
                            "_ddmm_inference_data.jpg";
    cv::imwrite(save_path, combined);
  }

  return true;
}

const std::vector<float>& DDMMDataGen::GetInferenceData() const {
  return inference_data_;
};

std::unique_ptr<DDMMDataGen> CreateDDMMDataGen() {
  return std::make_unique<DDMMDataGen>();
}

}  // namespace localization
}  // namespace deeproute
