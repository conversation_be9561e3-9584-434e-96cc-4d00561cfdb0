#pragma once

#include <memory>

#include <mm/fmm/fmm_algorithm.hpp>
#include <mm/mm_type.hpp>
#include <mm/stmatch/stmatch_algorithm.hpp>
#include <mm/transition_graph.hpp>
#include <network/network.hpp>

#include "drivers/gnss/ins.pb.h"
#include "lam_common/projection.pb.h"
#include "lock_on_road/lock_on_road_debug.pb.h"
#include "perception/perception_nn.pb.h"
#include "proto/lock_on_road_config.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "common/types.h"
#include "mm_localizer/utils.h"

namespace deeproute {
namespace localization {

using PointMatchingResult = FMM::MM::Candidate;
using MapMatchingResult = FMM::MM::MatchResult;
using TrajectoryCandidates = FMM::MM::Traj_Candidates;
using PointCandidates = FMM::MM::Point_Candidates;

class MapMatchingModel {
 public:
  virtual ~MapMatchingModel() = default;

  virtual void SetGlobalPlannedRoutes(
      const FMM::NETWORK::CustomGraph& edges) = 0;

  virtual std::vector<FMM::NETWORK::Edge> GetSubGraph() = 0;

  virtual bool Match(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                     const Vector2dVector& traj,
                     const proto::Projection& projection,
                     const std::vector<double>& headings,
                     const std::vector<double>& timestamps,
                     const std::vector<double>& vels, const double radius,
                     const int k, const double gps_error,
                     const double heading_error, const double trans_error,
                     const int32_t gnss_type, MapMatchingResult* mm_result,
                     PointCandidates* last_pose_referenced_candidates) = 0;

  virtual void AddRasmapNN(const perception::NnFrame& rasmap_nn) = 0;

  virtual void AddParallelRoadStatus(const int is_parallel_road) = 0;

  virtual void AddRoutingSceneType(
      const RoutingSceneType& prev_routing_scene_type) = 0;

  virtual void AddLinkPR(int pr) = 0;

  virtual void AddDdmmResult(const uint64_t time, const uint64_t sd_link_id,
                             const double pose_to_link_distance) = 0;

  virtual void SetPreviousResult(
      const std::shared_ptr<const LockOnRoadDebugInfo>&
          internal_debug_info) = 0;

  virtual void Reset() = 0;

  virtual void ResetRoutingMaskYawDecider() = 0;

  virtual int GraphSize() = 0;

  virtual void SetRoute(
      const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
          route_response,
      const FMM::NETWORK::CustomGraph& edges) = 0;
  virtual void SetRoute(const std::string& request_id,
                        const FMM::NETWORK::CustomGraph& edges) = 0;
};

std::unique_ptr<MapMatchingModel> CreateStMapMatchingModel();

std::unique_ptr<MapMatchingModel> CreateStMapMatchingModel(
    const MapMatchingConfig& config);

}  // namespace localization
}  // namespace deeproute
