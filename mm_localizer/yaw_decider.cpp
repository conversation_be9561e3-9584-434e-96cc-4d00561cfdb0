#include "yaw_decider.h"

#include "algorithm/geom_algorithm.hpp"
#include "common/log.h"
#include "common/types.h"

namespace deeproute {
namespace localization {

constexpr double kYawCostThreshold = 40.0;
constexpr double kDistThreshold = 35.0;
constexpr double kYawThreshold = M_PI / 2;
constexpr int kHistoryPosesQueueSize = 20;       // roughly 20m of history traj
constexpr int kHistoryRoutingMaskQueueSize = 4;  // roughly 0.5sec
constexpr int kHistoryAmapQueueSize = 2;
constexpr double kDegPerRad = 57.3;

YawDecider::YawDecider() : off_route_weight_(kYawCostThreshold) {
  history_rtk_poses_.set_capacity(kHistoryPosesQueueSize);
  history_routing_mask_.set_capacity(kHistoryRoutingMaskQueueSize);
  history_para_road_status_.set_capacity(kHistoryAmapQueueSize);
  history_junction_masks_.set_capacity(10);

  routing_mask_yaw_debug_ = false;
  char* ddmm_debug = std::getenv("DDMM_DEBUG");
  if (ddmm_debug == nullptr) {
    routing_mask_yaw_debug_ = false;
    MLOG(WARN) << "ROUTING MASK YAW DEBUG NOT ENABLED";
  } else {
    routing_mask_yaw_debug_ = std::string(ddmm_debug) == "1" ? true : false;
    MLOG(WARN) << "ROUTING MASK YAW DEBUG?: " << routing_mask_yaw_debug_;
  }

  char* ddmm_debug_dir = std::getenv("DDMM_DEBUG_DIR");
  if (ddmm_debug_dir == nullptr) {
    MLOG(WARN) << "ROUTING_MASK YAW DEBUG NOT ENABLED";
    routing_mask_debug_out_dir_ = "";
  } else {
    routing_mask_debug_out_dir_ =
        std::string(ddmm_debug_dir) + "/routing_mask_yaw_debug";
    MLOG(WARN) << "ROUTING_MASK YAW DEBUG:: " << routing_mask_debug_out_dir_;
  }
}

YawDecider::~YawDecider() {}

double Distance(const Vector2d& p1, const Vector2d& p2) {
  return std::sqrt((p1 - p2).squaredNorm());
}

double NormalizeHeading(double heading) {
  while (heading > 180.0) {
    heading -= 360.0;
  }
  while (heading < -180.0) {
    heading += 360.0;
  }
  return heading;
}

bool IsMakingTurn(
    const boost::circular_buffer<drivers::gnss::Ins>& trajectory_poses,
    double* heading_diff) {
  constexpr double kUTurnHeadingThreDeg = 80.0;

  if (trajectory_poses.size() < 2 || !trajectory_poses.full()) {
    return false;
  }

  std::vector<double> headings;
  for (const auto& pose : trajectory_poses) {
    const double heading_deg = pose.euler_angles().z() * kDegPerRad;
    headings.push_back(heading_deg);
  }

  double max_heading_diff = abs(headings[0] - headings[headings.size() - 1]);
  max_heading_diff = abs(NormalizeHeading(max_heading_diff));

  *heading_diff = max_heading_diff;
  return max_heading_diff > kUTurnHeadingThreDeg;
}

namespace {
double CalculateMaxPixelValue(const cv::Mat& image) {
  std::vector<float> pixel_values;
  pixel_values.assign(image.datastart, image.dataend);
  auto max_it = std::max_element(pixel_values.begin(), pixel_values.end());
  return *max_it;
}
}  // namespace
bool YawDecider::IsYaw(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                       const int32_t gnss_type, FMM::MM::MatchResult* mm_result,
                       FMM::MM::STMATCH* fmm_model) {
  (void)gnss_type;
  // only add rtk poses when its distance to the last rtk pose is larger than 1m
  if (history_rtk_poses_.empty()) {
    history_rtk_poses_.push_back(*rtk);
  } else {
    const auto& last_rtk = history_rtk_poses_.back();
    const double dist =
        Distance(Vector2d(last_rtk.position().x(), last_rtk.position().y()),
                 Vector2d(rtk->position().x(), rtk->position().y()));
    if (dist >= 1.0) {
      history_rtk_poses_.push_back(*rtk);
    }
  }

  if (mm_result == nullptr) {
    return false;
  }

  mm_result->match_status = FMM::MM::MATCH_STATUS_NULL;
  /// match failed, yaw
  if (mm_result->opt_candidate_path.empty()) {
    mm_result->match_status = FMM::MM::MATCH_STATUS_YAW;
    return true;
  }

  mm_result->match_status = FMM::MM::MATCH_STATUS_NORMAL;

  const FMM::MM::Candidate& match_candiate =
      mm_result->opt_candidate_path.back().c;
  const auto yaw_weight =
      match_candiate.candidate_weights[match_candiate.candidate_index]
          .on_route_weight;

  double max_heading_diff = 45.0;
  const bool is_making_turn =
      IsMakingTurn(history_rtk_poses_, &max_heading_diff);
  double yaw_weight_param = 1.0;
  double dist_weight_param = 1.0;
  double heading_weight_param = 1.0;
  if (is_making_turn) {
    yaw_weight_param = max_heading_diff / 45.0;
    dist_weight_param = max_heading_diff / 45.0;
    heading_weight_param = max_heading_diff / 45.0;
  }

  const double adapted_off_route_weight = off_route_weight_ * yaw_weight_param;
  const double adapted_dist_weight = kDistThreshold * dist_weight_param;
  const double adapted_heading_weight = kYawThreshold * heading_weight_param;

  // MLOG(INFO) << "yaw_weight: "<<yaw_weight << ", adapted_off_route_weight:
  // "<<adapted_off_route_weight << ", max_heading_diff:"<<max_heading_diff;

  if (yaw_weight >= adapted_off_route_weight) {
    const double heading_diff = FMM::ALGORITHM::calc_azi_diff_rad(
        match_candiate.edge_heading, match_candiate.pose_heading);
    MLOG(INFO) << "yaw_weight: " << yaw_weight
               << ", adap route_weight: " << adapted_off_route_weight
               << ", dist diff: " << match_candiate.dist
               << ", adap dist weight: " << adapted_dist_weight
               << ", heading diff: " << heading_diff
               << ", adap heading weight: " << adapted_heading_weight
               << ", is_making_turn: " << is_making_turn
               << ", max_heading_diff:" << max_heading_diff;

    if (match_candiate.dist >= adapted_dist_weight ||
        std::abs(heading_diff) >= adapted_heading_weight) {
      if (!IsPassover(mm_result, fmm_model)) {
        mm_result->match_status = FMM::MM::MATCH_STATUS_YAW;
        return true;
      } else {
        mm_result->match_status = FMM::MM::MATCH_STATUS_PASSOVER;
      }
    }
  }

  if (!enable_routing_mask_yaw_judgement_) {
    return false;
  }
  if (!history_para_road_status_.full()) {
    MLOG(INFO) << "HISTORY PARALLEL ROAD STATUS NOT FULL.";
    return false;
  }

  // Road type priorities:
  //  0:高速公路
  //  1:城市快速路
  //  2:国道
  //  3:省道、城市主干道
  //  4:县道
  //  5:路况较好的乡镇道路
  //  6:乡镇道路
  //  7:POI连接线，类似于小区路，但可以穿行
  //  8:小区路，不能穿行
  //  9:九级辅路：非机动车辆通行的辅路我们定义为九级辅路
  //  10:轮渡
  //  11:步行路
  // AS discussed with QI YANG AND QINCHENG, only keep 13456
  constexpr int kHighwayPr = 0;
  constexpr int kNationalRoadPr = 2;
  constexpr int kLowPriorityPrThreshold = 7;
  MLOG(INFO) << "PR: " << link_pr_;
  if (link_pr_ == kHighwayPr || link_pr_ == kNationalRoadPr ||
      link_pr_ >= kLowPriorityPrThreshold) {
    MLOG(INFO)
        << "YAW DECIDER: NOT DOING ROUTING MASK BASED YAW BECAUSE LINK PR: "
        << link_pr_;
    return false;
  }

  // if current frame contains junction mask, KEEP PREVIOUS YAW STATUS
  const float junction_mask_max_value = CalculateMaxPixelValue(junction_mask_);
  MLOG(INFO) << "JUNCTION MASK FOUND, ROUTING MASK MAX PIXEL VALUE: "
             << junction_mask_max_value;
  if (junction_mask_max_value > 10 ||
      prev_routing_scene_type_ == RoutingSceneType::JUNCTION) {
    MLOG(INFO) << "JUNCTION MASK FOUND, DO NOT DO YAW DETECTION! ROUTING MASK "
                  "MAX PIXEL VALUE: "
               << junction_mask_max_value;
    contains_junction_mask_ = true;

    if (routing_mask_yaw_status_) {
      mm_result->match_status = FMM::MM::MATCH_STATUS_YAW;
      return true;
    } else {
      return false;
    }

  } else {
    contains_junction_mask_ = false;
    MLOG(INFO) << "JUNCTION MASK NOT FOUND, DO YAW DETECTION!";
  }

  // if left parallel road, clear all status
  if (IsLeavingParallelRoad()) {
    road_scene_ = "leaving_parallel";
    MLOG(INFO) << "SCENE DETECTION: LEFT PARALLEL ROAD!";

    routing_mask_yaw_status_ = false;
    history_routing_mask_.clear();
    history_para_road_status_.clear();

    MLOG(INFO) << "YAW DECIDER: LEFT PARALLEL ROAD!";
    return false;
  }

  // // if left parallel road, clear all status
  // if (IsLeavingParallelRoad()) {
  //   road_scene_ = "not_on_parallel";
  //   MLOG(INFO) << "SCENE DETECTION: LEFT PARALLEL ROAD!";

  //   routing_mask_yaw_status_ = false;
  //   history_routing_mask_.clear();
  //   history_para_road_status_.clear();

  //   MLOG(INFO) << "YAW DECIDER: LEFT PARALLEL ROAD!";
  //   return false;
  // }

  if (IsOnParallelRoad() || IsEnteringParallelRoad()) {
    road_scene_ = "enter/on_parallel";
  }

  if (road_scene_ == "enter/on_parallel" && routing_mask_yaw_status_) {
    MLOG(INFO)
        << "YAW DECIDER: Enter/On Parallel Road and is Yawed, keep YAWED";
    mm_result->match_status = FMM::MM::MATCH_STATUS_YAW;
    routing_mask_yaw_status_ = true;
    return true;
  }

  // if current link is not MAIN ROAD / SECONDARY ROAD
  if (prev_routing_scene_type_ != RoutingSceneType::MAIN_ROAD &&
      prev_routing_scene_type_ != RoutingSceneType::SECONDARY_ROAD &&
      prev_routing_scene_type_ != RoutingSceneType::MAIN_SECONDARY_ENTRANCE) {
    MLOG(INFO) << "ROUTING SCENE DOES NOT CONTAIN MAIN SECONDARY FORMWAY, NOT "
                  "DOING ANYTHING, prev_routing_scene_type_: "
               << prev_routing_scene_type_;
    return false;
  } else {
    MLOG(INFO) << "ROUTING SCENE CONTAIN MAIN SECONDARY FORMWAY, ENTERED "
                  "ROUTING MASK YAW DETECTION LOGIC!";
  }

  // if entering or on parallel road: do routing mask yaw detection
  if (CheckRoutingMaskYaw()) {
    road_scene_ = "enter/on_parallel";

    MLOG(INFO)
        << "YAW DECIDER: Enter/On Parallel Road and is Yawed, detect YAWED";
    mm_result->match_status = FMM::MM::MATCH_STATUS_YAW;

    // put text YAW on image
    routing_mask_yaw_status_ = true;

    return true;
  } else {
    road_scene_ = "enter/on_parallel";

    MLOG(INFO) << "YAW DECIDER: Enter/On Parallel Road and is not Yawed, not "
                  "detect YAWED";
    routing_mask_yaw_status_ = false;

    return false;
  }
}

bool YawDecider::IsRtkGood(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                           const int32_t gnss_type) {
  if (gnss_type >= 32) {
    return true;
  }
  double rtk_acc =
      std::sqrt(rtk->position_covariance()[0] + rtk->position_covariance()[1]);
  return rtk_acc <= 3.0;
}

bool YawDecider::IsPassover(FMM::MM::MatchResult* mm_result,
                            FMM::MM::STMATCH* fmm_model) {
  FMM::NETWORK::CalcBlocks& calc_blocks = fmm_model->GetCalcBlock();
  const FMM::MM::CandidateInfo* candidate_block = calc_blocks.GetCandidateBlock(
      FMM::NETWORK::CANDIDATE_TYPE_NO_CONTINUE_WEIGHT);
  if (nullptr == candidate_block) {
    return false;
  }
  if (candidate_block->candidate_weight.on_route_weight >= off_route_weight_) {
    /// if not passover, reset
    calc_blocks.Reset();
    return false;
  }
  /// passover, change candidate block
  calc_blocks.ChangeCandidateBlockByPassover();
  calc_blocks.ConstructMatchedCandidatePath(*mm_result);
  return true;
}

namespace {

cv::Mat Dilate(cv::Mat& image) {
  const int dilation_size = 5;
  cv::Mat kernel = cv::getStructuringElement(
      cv::MORPH_RECT, cv::Size(2 * dilation_size + 1, 2 * dilation_size + 1),
      cv::Point(dilation_size, dilation_size));

  // Perform dilation
  cv::Mat dilated_image;
  cv::dilate(image, dilated_image, kernel);
  return dilated_image;
}

}  // namespace

bool YawDecider::ParseNnFrameToRoutingMask(const perception::NnFrame& frame,
                                           cv::Mat& routing_mask,
                                           cv::Mat& junction_mask) {
  const std::string kRoutingMaskKey = "refline_instance_passable_road_mask";
  const std::string kJunctionMaskKey = "roadmask_direction";
  const std::set<std::string> kLayerNames = {
      kRoutingMaskKey, kJunctionMaskKey, "boundary_crosswalk_confidence",
      "stopline_curb_confidence", "centerline_confidence"};

  std::unordered_map<std::string, cv::Mat> bev_map;
  for (int i = 0; i < frame.nn_size(); i++) {
    const auto& layer = frame.nn(i);

    if (kLayerNames.find(layer.name()) == kLayerNames.end()) {
      continue;
    }

    // MLOG(INFO) << "layer name: " << layer.name() << ", idx: " << i
    //            << ", shape: "
    //            << ", " << layer.shape(1) << ", " << layer.shape(2) << ", "
    //            << layer.shape(3);

    // layer shape: 1 channel height width, road mask has 2 channels
    const int channels = layer.shape(1);
    const int rows = layer.shape(2);
    const int cols = layer.shape(3);
    std::string layer_nn_data;
    bool uncompress_status = snappy::Uncompress(
        layer.nn_data().c_str(), layer.nn_data().length(), &layer_nn_data);

    if (!uncompress_status) {
      MLOG(WARN) << "Cannot uncompress mat: " << layer.name();
      continue;
    }

    if (layer.name() != kJunctionMaskKey) {
      std::vector<float> pixel_values;
      pixel_values.reserve(channels * rows * cols);
      for (size_t j = 0; j < layer_nn_data.length(); j++) {
        pixel_values.push_back(float(layer_nn_data.at(j)) / 32.);
      }

      if (channels == 1) {
        cv::Mat nn_layer_img = cv::Mat(pixel_values).clone();
        nn_layer_img = nn_layer_img.reshape(channels, rows);
        bev_map[layer.name()] = nn_layer_img;
      }
    } else {
      std::vector<float> pixel_values;
      pixel_values.reserve(1 * rows * cols);
      for (size_t j = 73920; j < 73920 + 73920; j++) {
        pixel_values.push_back(float(layer_nn_data.at(j)) / 32.);
      }

      cv::Mat nn_layer_img = cv::Mat(pixel_values).clone();
      nn_layer_img = nn_layer_img.reshape(1, rows);
      bev_map[layer.name()] = nn_layer_img;
    }
  }

  bool is_valid = true;
  // sanity check
  if (auto it = bev_map.find(kRoutingMaskKey);
      it == bev_map.end() || it->second.empty()) {
    MLOG(WARN) << "[RASMAP_NN] rasmap key not found, " << kRoutingMaskKey;
    is_valid = false;
    return is_valid;
  }
  routing_mask = bev_map[kRoutingMaskKey];
  routing_mask = Dilate(routing_mask);

  if (bev_map.find(kJunctionMaskKey) == bev_map.end()) {
    MLOG(WARN) << "[RASMAP_NN] rasmap key not found, " << kJunctionMaskKey;
    junction_mask = cv::Mat::zeros(240, 308, CV_32FC1);
  } else {
    junction_mask = bev_map[kJunctionMaskKey];
  }

  if (routing_mask_yaw_debug_) {
    cv::Mat bev_debug;
    cv::merge(std::vector<cv::Mat>({bev_map["boundary_crosswalk_confidence"],
                                    bev_map["centerline_confidence"],
                                    bev_map["stopline_curb_confidence"]}),
              bev_debug);

    cv::Mat routing_mask_copy = routing_mask.clone();
    cv::cvtColor(routing_mask_copy, routing_mask_copy, cv::COLOR_GRAY2BGR);
    cv::circle(routing_mask_copy, cv::Point(92, 120), 5, cv::Scalar(0, 0, 255),
               -1);
    MLOG(INFO) << "routing mask shape: " << routing_mask_copy.size();
    cv::addWeighted(routing_mask_copy, 0.5, bev_debug, 1, 0.0,
                    routing_mask_copy);

    std::string debug_status = "normal";
    if (routing_mask_yaw_status_) {
      debug_status = "yaw";
    }

    cv::flip(routing_mask_copy, routing_mask_copy, 0);

    // put text on image
    cv::putText(routing_mask_copy, debug_status, cv::Point(10, 10),
                cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 2);
    cv::putText(routing_mask_copy, road_scene_, cv::Point(10, 30),
                cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255), 2);
    cv::putText(routing_mask_copy,
                "contain junc: " + std::to_string(contains_junction_mask_),
                cv::Point(10, 50), cv::FONT_HERSHEY_SIMPLEX, 0.5,
                cv::Scalar(0, 0, 255), 2);
    cv::putText(
        routing_mask_copy,
        "routing scen: " + RoutingSceneTypeToString(prev_routing_scene_type_),
        cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 0, 255),
        2);

    std::string path = routing_mask_debug_out_dir_ + "/" +
                       std::to_string(frame.timestamp()) + ".png";
    routing_mask_time_ = std::to_string(frame.timestamp());

    MLOG(INFO) << "ROUTING MASK DEBUG IMG PATH: " << path;

    cv::Mat routing_mask_debug_img = routing_mask_copy * 255;

    // print junction mask shape
    MLOG(INFO) << "junction mask shape: " << junction_mask.size();

    // convert to bgr
    cv::cvtColor(junction_mask, junction_mask, cv::COLOR_GRAY2BGR);
    cv::Mat junction_mask_debug_img = junction_mask.clone() * 255;

    // add junction_mask_ to the right of the image

    cv::addWeighted(routing_mask_debug_img, 0.5, junction_mask_debug_img, 0.5,
                    0.0, routing_mask_copy);

    // cv::circle(junction_mask_debug_img, cv::Point(92, 120), 5,
    //  cv::Scalar(0, 0, 255), -1);
    // cv::Mat combined_img;
    // cv::hconcat(routing_mask_debug_img, junction_mask_debug_img,
    // combined_img);

    cv::imwrite(path, routing_mask_copy);
  }

  return is_valid;
}

void YawDecider::AddLinkPr(int pr) { link_pr_ = pr; }

void YawDecider::AddParallelRoadStatus(int paral_status) {
  MLOG(INFO) << "ROUTING_MASK_DEBUG: ADD PARALLEL ROAD STATUS: "
             << paral_status;
  history_para_road_status_.push_back(paral_status);
}

namespace {
cv::Mat ImageMean(boost::circular_buffer<cv::Mat>& images) {
  if (images.empty()) {
    return cv::Mat();
  }

  cv::Mat accumulator = cv::Mat::zeros(images[0].size(), images[0].type());

  for (const auto& image : images) {
    cv::add(accumulator, image, accumulator);
  }

  cv::Mat mean_image;
  cv::divide(accumulator, static_cast<double>(images.size()), mean_image);

  return mean_image;
}
}  // namespace

void YawDecider::AddRasmapNN(const perception::NnFrame& rasmap_nn) {
  cv::Mat routing_mask, junction_mask;
  const auto ret =
      ParseNnFrameToRoutingMask(rasmap_nn, routing_mask, junction_mask);
  if (!ret) {
    MLOG(WARN) << "Cannot parse nn frame to routing mask";
    return;
  }
  history_junction_masks_.push_back(junction_mask);
  junction_mask_ = ImageMean(history_junction_masks_);

  MLOG(INFO) << "ROUTING_MASK_DEBUG: ADD ROUTING_MASK.";
  history_routing_mask_.push_back(routing_mask);
}

void YawDecider::AddRoutingSceneType(
    const RoutingSceneType& prev_routing_scene_type) {
  prev_routing_scene_type_ = prev_routing_scene_type;
}

bool YawDecider::IsOnParallelRoad() {
  if (!history_para_road_status_.full()) {
    return false;
  }

  // if all history paral road status is 1, then on parallel road
  for (const auto& paral_status : history_para_road_status_) {
    if (paral_status != 1) {
      return false;
    }
  }
  return true;
}

bool YawDecider::IsNotOnParallelRoad() {
  if (!history_para_road_status_.full()) {
    return false;
  }

  // if all history paral road status is 2, then not on parallel road
  for (const auto& paral_status : history_para_road_status_) {
    if (paral_status != 2) {
      return false;
    }
  }
  return true;
}

bool YawDecider::IsLeavingParallelRoad() {
  if (!history_para_road_status_.full()) {
    return false;
  }

  // if first half of history paral road status is 1, but the second half is
  // 2, then left parallel road
  if (history_para_road_status_.size() < 2) {
    return false;
  }
  for (size_t i = 0; i < history_para_road_status_.size() / 2; i++) {
    if (history_para_road_status_[i] != 1) {
      return false;
    }
  }

  for (size_t i = history_para_road_status_.size() / 2;
       i < history_para_road_status_.size(); i++) {
    if (history_para_road_status_[i] != 2) {
      return false;
    }
  }

  return true;
}

bool YawDecider::IsEnteringParallelRoad() {
  if (!history_para_road_status_.full()) {
    return false;
  }

  // if first half of history paral road status is 2, but the second half is
  // 1, then entered parallel road
  if (history_para_road_status_.size() < 2) {
    return false;
  }
  for (size_t i = 0; i < history_para_road_status_.size() / 2; i++) {
    if (history_para_road_status_[i] != 2) {
      return false;
    }
  }

  for (size_t i = history_para_road_status_.size() / 2;
       i < history_para_road_status_.size(); i++) {
    if (history_para_road_status_[i] != 1) {
      return false;
    }
  }

  return true;
}

bool YawDecider::CheckRoutingMaskYaw() {
  if (!history_routing_mask_.full()) {
    MLOG(INFO) << "History routing mask not full, neglect";
    return false;
  }

  if (!history_para_road_status_.full()) {
    MLOG(INFO) << "History para road status not full, neglect";
    return false;
  }

  // // if on parallel road, neglect this logic
  // if (IsOnParallelRoad()) {
  //   MLOG(INFO) << "On parallel road, neglect this logic";
  //   return false;
  // }

  // if left parallel road, neglect this logic
  if (IsLeavingParallelRoad()) {
    MLOG(INFO) << "Left parallel road, neglect this logic";
    return false;
  }

  MLOG(INFO) << "CHECK routing mask yaw!";

  // now we are sure that we are ENTERING parallel road

  // for all routing mask, check if ego car position is contained within the
  // mask
  int routing_mask_yaw_num = 0;
  for (size_t i = 0; i < history_routing_mask_.size(); i++) {
    cv::Mat routing_mask = history_routing_mask_[i] * 255;
    const int x = 92;
    const int y = 120;

    const float pixel_value = routing_mask.at<float>(y, x);

    if (pixel_value < 100) {
      double minVal, maxVal;
      cv::Point minLoc, maxLoc;
      cv::minMaxLoc(routing_mask, &minVal, &maxVal, &minLoc, &maxLoc);

      MLOG(INFO) << "Routing mask yaw detected! idx: " << i
                 << ", pixel value: " << static_cast<float>(pixel_value)
                 << ", image shape: " << routing_mask.size()
                 << ", max value: " << maxVal;

      if (maxVal == 0) {
        continue;
      }

      routing_mask_yaw_num++;

      // Plot ego position on a copy of the image to avoid altering the original
      // cv::Mat routing_mask_with_circle = routing_mask.clone();
      // cv::circle(routing_mask_with_circle, cv::Point(x, y), 5,
      // cv::Scalar(255),
      //            -1);
      // cv::imwrite(routing_mask_time_ + "_" + std::to_string(i) + "_yaw.png",
      //             routing_mask_with_circle);
    } else {
      MLOG(INFO) << "Routing mask yaw not detected! idx: " << i
                 << ", pixel value: " << static_cast<float>(pixel_value);
      // cv::imwrite(routing_mask_time_ + "_" + std::to_string(i) + "_normal_" +
      // std::to_string(pixel_value) + ".png",
      // routing_mask);
    }
  }

  if (routing_mask_yaw_num >= 3) {
    MLOG(INFO) << "Routing mask yaw detected!";
    return true;
  } else {
    MLOG(INFO) << "Routing mask yaw not detected! Routing mask yaw num: "
               << routing_mask_yaw_num << " while history routing mask size: "
               << history_routing_mask_.size();
    return false;
  }
}

// bool YawDecider::GetYawSceneType(){

// }

}  // namespace localization
}  // namespace deeproute
