#ifndef __LOG_UTILS_H__
#define __LOG_UTILS_H__

#include <memory>

#include "log_types.h"
#include "proto/lock_on_road_config.pb.h"


namespace deeproute {
namespace localization {
class LogUtil {
public:
    ~LogUtil();
    static LogUtil * GetInstance();
    static void ReleaseInstance();

    void Init(const MapMatchingConfig_LogConfig &log_config);

    void RecordNGM(const MapMatchingResult* mm_result, const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                  const proto::Projection& projection, const int32_t gnss_type, const double tick_time,
                  const Vector2d pos, const double pos_azi);
    void RecordPath(const deeproute::navinfo::SDRoutingResponse* route_response, const proto::Projection* projection);

    void RecordLOR(const LockOnRoadResult* lock_on_road_result);
    void RecordRTK(const drivers::gnss::Ins* rtk);
protected:
    void RecordLog(LogBase *log);
private:
    LogUtil();
    static std::unique_ptr<LogUtil> instance_;
    std::unique_ptr<LogManager> log_manager_;

    int ngm_log_step_ = 10;
};

}  // namespace localization
}  // namespace deeproute

#endif