load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "map_matching",
    srcs = ["map_matching.cpp"],
    hdrs = ["map_matching.h"],
    deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/routing:navinfo_routing_proto_cc",
        "@common//proto/perception:perception_nn_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_debug_proto_cc",
        ":log_utils",
        ":utils",
        ":yaw_decider",
        "//data_adapter:projection_transformation",
        "//proto:lock_on_road_config_cc_proto",
        "@common//common:types",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@snappy//:snappy",
    ],
)

cc_library(
    name = "log_record",
    srcs = ["log_record.cpp"],
    hdrs = ["log_record.h"],
    deps = [
        "@common//common:time",
        "@common//common:time_types",
        "//proto:lock_on_road_config_cc_proto",
        "@common//base/time",
        "@common//common:thread_pool",
    ],
)

cc_library(
    name = "yaw_decider",
    srcs = ["yaw_decider.cpp"],
    hdrs = ["yaw_decider.h"],
    deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:perception_nn_proto_cc",
        "@common//proto/routing:navinfo_routing_proto_cc",
        "@common//common:log",
        "@common//common:types",
        "@fmm",
        "@snappy//:snappy",
        "@opencv//:opencv",
        ":utils",
    ],
)

cc_library(
    name = "log_types",
    srcs = ["log_types.cpp"],
    hdrs = ["log_types.h"],
    deps = [
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "//data_adapter:projection_transformation",
        "//joint:ll_utils",
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/routing:navinfo_routing_proto_cc",
        "@common//common:types",
        "@fmm",
        "@lam_common//lam_common:utm_projection_convert",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
    ],
)

cc_library(
    name = "ddmm_shadow_mode",
    srcs = ["ddmm_shadow_mode.cpp"],
    hdrs = ["ddmm_shadow_mode.h"],
    deps = [
        "@common//proto/map:sd_map_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//common:event_log_handle",
        "@common//common:log",
    ],
)

cc_library(
    name = "utils",
    srcs = ["utils.cpp"],
    hdrs = ["utils.h"],
    deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/routing:navinfo_routing_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/map:sd_map_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//proto/map:sd_horizon_provider_proto_cc",
        "//geometry",
        "//joint:ll_utils",
        "@common//common:event_log_handle",
        "@common//common:log",
        "@common//common:time",
        "@common//common:types",
        "@common//common/road_map:hd_map_server_loading_utils",
        "@common//common/road_map:hd_map_spatial_lookup_server",
        "@common//common/road_map:hdmap_info",
        "@common//common/semantic_lmdb:semantic_map_conversion",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:proto_utils",
        "@lam_common//lam_common:utm_projection_convert",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@lam_common//lam_common/proto/lam_common/sd_map:sd_map_service_cc_proto",
        "@lam_common//lam_common/third_party/json",
    ],
)

cc_library(
    name = "ddmm_model",
    srcs = [
        "ddmm_data_generate.cpp",
        "ddmm_model.cpp",
        "ddmm_model_post.cpp",
    ],
    hdrs = [
        "ddmm_data_generate.h",
        "ddmm_model.h",
        "ddmm_model_post.h",
    ],
    deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:perception_nn_proto_cc",
        "@common//proto/map:sd_map_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_debug_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//proto/map:sd_horizon_provider_proto_cc",
        ":log_utils",
        ":utils",
        "//geometry",
        "//proto:lock_on_road_config_cc_proto",
        "@common//base:deeproute_path",
        "@common//common:event_log_handle",
        "@common//common:log",
        "@common//common:types",
        "@common//transform:transformation",
        "@fmm",
        "@inference_engine",
        "@lam_common//lam_common:logging",
        "@snappy",
    ] + select({
        "@deeproute_build_tools//:nvidia_setting": ["@local_cuda//:cuda_runtime"],
        "//conditions:default": [],
    }),
)

cc_library(
    name = "log_utils",
    srcs = ["log_utils.cpp"],
    hdrs = ["log_utils.h"],
    deps = [
        ":log_record",
        ":log_types",
        "//proto:lock_on_road_config_cc_proto",
    ],
)

cc_binary(
    name = "utils_test",
    srcs = ["utils_test.cpp"],
    deps = [
        ":utils",
        "@gtest//:gtest_main",
    ],
)
