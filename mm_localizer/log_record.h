#ifndef __LOG_RECORD_H__
#define __LOG_RECORD_H__

#include <fstream>
#include <string>

#include "proto/lock_on_road_config.pb.h"

#include "common/thread_pool.h"

namespace deeproute {
namespace localization {

class LogRecord {
 public:
  ~LogRecord();
  static LogRecord* GetInstance();
  static void ReleaseInstance();
  void Init(const MapMatchingConfig_LogConfig& log_config);
  void RecordFile(const std::string& content);

  void static InitSync(LogRecord* pParam,
                       const MapMatchingConfig_LogConfig log_config);
  void static RecordFileSync(LogRecord* pParam, const std::string content);
  void static WriteRemainLogSync(LogRecord* pParam, const std::string& content);

 protected:
  void OnInit(const MapMatchingConfig_LogConfig& log_config);
  void OnRecordFile(const std::string& content);
  void OnWriteLogRemain(const std::string& content);
  void OnWriteLog(const std::string& content);

 private:
  LogRecord();
  static LogRecord* instance_;
  std::ofstream output_;
  const std::size_t max_buffer_size_ = 10240;
  std::string buffer_str_;
  std::string file_path_;
  common::ThreadPool* write_thread_;
};

}  // namespace localization
}  // namespace deeproute

#endif
