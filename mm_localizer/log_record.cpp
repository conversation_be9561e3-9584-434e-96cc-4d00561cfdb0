#include "log_record.h"

#include "base/time/time.h"
#include "common/time_types.h"

namespace deeproute {
namespace localization {

LogRecord* LogRecord::instance_ = nullptr;

LogRecord::LogRecord() : write_thread_(nullptr) {}

LogRecord::~LogRecord() {
  if (write_thread_ != nullptr) {
    write_thread_->Enqueue(
        std::bind(&LogRecord::WriteRemainLogSync, this, buffer_str_));
    write_thread_->WaitUntilWorkComplete();
    delete write_thread_;
  }
}

LogRecord* LogRecord::GetInstance() {
  if (instance_ == nullptr) {
    instance_ = new LogRecord();
  }
  return instance_;
}

void LogRecord::ReleaseInstance() {
  if (instance_ != nullptr) {
    delete instance_;
    instance_ = nullptr;
  }
}

void LogRecord::Init(const MapMatchingConfig_LogConfig& log_config) {
  if (!log_config.enable_write_log() || log_config.log_path() == "") {
    return;
  }
  write_thread_ = new common::ThreadPool(1, "LOCK_ON_ROAD_LOG");
  write_thread_->Enqueue(std::bind(&LogRecord::InitSync, this, log_config));
}

void LogRecord::InitSync(LogRecord* pParam,
                         const MapMatchingConfig_LogConfig log_config) {
  if (pParam != nullptr) {
    pParam->OnInit(log_config);
  }
}

void LogRecord::OnInit(const MapMatchingConfig_LogConfig& log_config) {
  deeproute::base::Time now = deeproute::base::Time::Now();
  ::common::TimeMicro start1 = now.ToMillisecond();
  file_path_ = log_config.log_path();
  if (!file_path_.empty() && file_path_.back() != '/') {
    file_path_ += "/";
  }
  std::string file_name = file_path_ + std::to_string(start1) + ".log";
  output_.open(file_name, std::ios::out);
}

void LogRecord::RecordFile(const std::string& content) {
  if (write_thread_ == nullptr) {
    return;
  }
  write_thread_->Enqueue(std::bind(&LogRecord::OnRecordFile, this, content));
}

void LogRecord::RecordFileSync(LogRecord* pParam, const std::string content) {
  if (pParam != nullptr) {
    pParam->OnRecordFile(content);
  }
}

void LogRecord::OnRecordFile(const std::string& content) {
  buffer_str_ += content;
  if (buffer_str_.length() >= max_buffer_size_) {
    OnWriteLog(buffer_str_);
    buffer_str_.clear();
  }
}

void LogRecord::WriteRemainLogSync(LogRecord* pParam,
                                   const std::string& content) {
  if (pParam != nullptr) {
    pParam->OnWriteLogRemain(content);
  }
}

void LogRecord::OnWriteLogRemain(const std::string& content) {
  OnWriteLog(content);
  if (output_.is_open()) {
    output_.close();
  }
}

void LogRecord::OnWriteLog(const std::string& content) {
  if (!output_.is_open()) {
    return;
  }
  output_.write(content.c_str(), content.length());
  output_.flush();
}

}  // namespace localization
}  // namespace deeproute
