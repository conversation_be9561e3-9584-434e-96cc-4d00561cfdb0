#ifndef __UTILS_H__
#define __UTILS_H__

#include <cstdint>

#include <absl/status/statusor.h>
#include <network/type.hpp>

#include "lam_common/projection.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "map/sd_map.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "common/road_map/hd_map_lane_info_server.h"
#include "common/types.h"

namespace deeproute {
namespace localization {

enum RoutingSceneType { UNKNOWN = 0, MAIN_ROAD = 1, SECONDARY_ROAD = 2, MAIN_SECONDARY_ENTRANCE=3, JUNCTION = 4 };

std::string RoutingSceneTypeToString(RoutingSceneType type);

std::string ParseTrajectoryToWkt(const Vector2dVector& traj);

FMM::CORE::LineString ParseTrajetoryToLineString(const Vector2dVector& traj);

absl::StatusOr<FMM::NETWORK::CustomGraph> LoadOfflineRoutingResponse(
    const std::string& routing_response, const proto::Projection& projection);

absl::StatusOr<FMM::NETWORK::CustomGraph> LoadRoutingResponseProto(
    const std::string& routing_response_file,
    const proto::Projection& projection);

absl::StatusOr<FMM::NETWORK::CustomGraph> ConvertRoutingResponseToCustomGraph(
    const deeproute::navinfo::SDRoutingResponse& routing_response,
    const proto::Projection& projection);

bool ExtractLocalSdMapFromQueriedResult(
    const sd_map::SDMap& resp, const proto::Projection& projection,
    deeproute::sd_map::SdLinks* sd_links,
    std::unordered_map<uint64_t, LockOnRoadResult::LinkProperties>*
        ni_id_to_link_properties);

deeproute::sd_map::SdLinks ExtractLocalSdMapFromRoutingResponse(
    const deeproute::navinfo::Route& route,
    const proto::Projection& projection);

bool GetSameDirectionLanes(
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const Vector2d& xy, const double heading,
    std::vector<deeproute::hdmap::Lane>* left_lanes,
    std::vector<deeproute::hdmap::Lane>* right_lanes);

bool GetSameDirectionLanes(
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const Vector2d& xy, const double heading, int* left_lane_num,
    int* right_lane_num);

double Len();

double HaversineDistance(const Vector2d& lonlat1, const Vector2d& lonlat2);

void WriteSDMapLinksToGeoJSONFile(const sd_map::SDMap& sd_map_data,
                                  const std::string& filepath);

}  // namespace localization
}  // namespace deeproute

#endif
