#pragma once

#include "lock_on_road/lock_on_road.pb.h"
#include "map/sd_map.pb.h"

namespace deeproute {
namespace localization {

class DdmmShadowMode {
 public:
  DdmmShadowMode() {}

  void AddLocalSdMap(const deeproute::sd_map::SdLinks& sd_links);

  void AddLockOnRoadResult(const LockOnRoadResult& lor_result);

  void AddDdmmResult(const LockOnRoadResult& lor_result);

  inline LockOnRoadResult GetShadowModeResult() { return ddmm_result_; };

 private:
  std::map<int64_t, LockOnRoadResult> time_to_lor_result_;
  LockOnRoadResult ddmm_result_;

  std::set<std::string> local_sd_link_ids_;
  int trip_match_num_ = 0;
  int trip_mismatch_num_ = 0;
  int missing_sd_link_id_in_local_sd_map_num_ = 0;
  bool status_reported = false;
};

std::unique_ptr<DdmmShadowMode> CreateDdmmShadowMode();

}  // namespace localization
}  // namespace deeproute
