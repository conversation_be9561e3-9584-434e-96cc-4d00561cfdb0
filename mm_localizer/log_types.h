#ifndef __LOG_TYPES_H__
#define __LOG_TYPES_H__

#include <memory>
#include <string>

#include <mm/mm_type.hpp>

#include "drivers/gnss/ins.pb.h"
#include "lam_common/projection.pb.h"
#include "routing/navinfo_routing.pb.h"
#include "lock_on_road/lock_on_road.pb.h"

#include "common/types.h"

namespace deeproute {
namespace localization {

using MapMatchingResult = FMM::MM::MatchResult;

enum LogType {
    ///0-route match result
    LOG_TYPE_NGM = 0,
    ///1-route
    LOG_TYPE_PATH,
    ///2-lock on road topic
    LOG_TYPE_LOR,
    ///3-rtk pos
    LOG_TYPE_RTK,
    LOG_COUNT,
};

class LogBase {
 public:
  LogBase(const LogType type);
  virtual ~LogBase();

  virtual const char* FormatContent();

  LogType GetLogType() { return type_; }

 protected:
  template <typename T>
  void ToString(const T a_value, const int32_t n = 10) {
    std::ostringstream out;
    out.precision(n);
    out << std::fixed << a_value << " ";
    content_ += out.str();
  }

  void ToString(const std::string str) {
    content_ += str;
    if (str != "\n") {
      content_ += " ";
    }
  }

  void ToString(const char* str) {
    content_ += str;
    if (std::string(str) != "\n") {
      content_ += " ";
    }
  }

  void UtmToLatLon(const proto::Projection& projection, double x, double y,
                   double& lon, double& lat);
  void LatLonToUtm(const proto::Projection& projection, double lon, double lat,
                   double& x, double& y);

 public:
  double tick_time_;
  double local_tick_time_;

 protected:
  LogType type_;
  std::string content_;
  static const std::string flags_[LOG_COUNT];
};

class LogForNgm : public LogBase {
 public:
  LogForNgm();
  virtual ~LogForNgm();

  virtual const char* FormatContent();

 public:
  const MapMatchingResult* mm_result_;
  const std::shared_ptr<const drivers::gnss::Ins>* rtk_;
  const proto::Projection* projection_;
  int32_t gnss_type_;
  Vector2d pos_;
  double pos_azi_;
};

class LogForPath : public LogBase {
 public:
  LogForPath();
  virtual ~LogForPath();

  virtual const char* FormatContent();

 protected:
  void FormatLaneInfo(const deeproute::navinfo::Segment& seg);
  void FormatPoints(const deeproute::navinfo::Segment& seg);

 public:
  const deeproute::navinfo::SDRoutingResponse* route_response_;
  const proto::Projection* projection_;
};

class LogForLOR : public LogBase {
 public:
  LogForLOR();
  virtual ~LogForLOR();

  virtual const char* FormatContent();

 public:
  const LockOnRoadResult* lock_on_road_result_;
};

class LogForRTK : public LogBase {
public:
    LogForRTK();
    virtual ~LogForRTK();

    virtual const char* FormatContent();

    template<class T> 
    void FormatRepeatElement(const T& ele) {
      int32_t size = ele.size();
      ToString(size);
      for (int32_t index = 0; index < size; index++) {
        ToString(ele[index]);
      }
    }

public:
    const drivers::gnss::Ins* rtk_;
};

class LogFactory {
 public:
  static std::shared_ptr<LogBase> make(const LogType type);
};

class LogManager {
 public:
  LogManager();
  ~LogManager();

  void Init();

  LogBase* getLog(const LogType type) { return logs_[type].get(); }

 private:
  std::vector<std::shared_ptr<LogBase>> logs_;
};

}  // namespace localization
}  // namespace deeproute

#endif
