#include "mm_localizer/map_matching.h"

#include <Eigen/Core>
#include <boost/filesystem.hpp>
#include <mm/fmm/ubodt.hpp>
#include <network/route_manager.hpp>
#include <network/type.hpp>

#include "common/log.h"
#include "log_utils.h"
#include "mm_localizer/map_matching.h"
#include "mm_localizer/utils.h"
#include "yaw_decider.h"

namespace deeproute {
namespace localization {
namespace {

// double MovedDistance(const FMM::CORE::Trajectory& trajectory) {
//   const FMM::CORE::LineString geom = trajectory.geom;
//   if (geom.get_num_points() <= 2) {
//     return 0;
//   }

//   const int n = geom.get_num_points();
//   const double sx = geom.get_x(n - 2);
//   const double sy = geom.get_y(n - 2);

//   const double ex = geom.get_x(n - 1);
//   const double ey = geom.get_y(n - 1);
//   const double dx = ex - sx;
//   const double dy = ey - sy;

//   return std::sqrt(dx * dx + dy * dy);
// }

bool CheckLogLevelValidity(const std::string& level) {
  std::vector<std::string> Supported = {"info", "debug", "trace"};
  if (std::find(Supported.begin(), Supported.end(), level) == Supported.end()) {
    MLOG(FATAL) << "FMM supported log level includes info, debug and trace.";
    return false;
  }
  return true;
}

}  // namespace

class MapMatchingImpl : public MapMatchingModel {
 public:
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW

  explicit MapMatchingImpl();

  explicit MapMatchingImpl(const MapMatchingConfig& config);

 protected:
  MapMatchingConfig config_;
};

MapMatchingImpl::MapMatchingImpl() {}

MapMatchingImpl::MapMatchingImpl(const MapMatchingConfig& config)
    : config_{config} {}

class StMatchModel : public MapMatchingImpl {
 public:
  explicit StMatchModel(const MapMatchingConfig& config)
      : MapMatchingImpl(config), fmm_log_level_(config.fmm_log_level()) {
    CheckLogLevelValidity(fmm_log_level_);
    // MLOG(INFO) << "Using log level: " << fmm_log_level_;

    yaw_decider_.SetUseRoutingMaskYawJudgement(
        config.enable_routing_mask_yaw_judgement());
  }

  explicit StMatchModel() {}

  void SetGlobalPlannedRoutes(const FMM::NETWORK::CustomGraph& edges) override;

  std::vector<FMM::NETWORK::Edge> GetSubGraph() override { return sub_graph_; }

  void AddDdmmResult(const uint64_t time, const uint64_t sd_link_id,
                     const double pose_to_link_distance) override {
    yaw_decider_.AddDdmmResult(time, sd_link_id, pose_to_link_distance);
  };

  bool Match(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
             const Vector2dVector& traj, const proto::Projection& projection,
             const std::vector<double>& headings,
             const std::vector<double>& timestamps,
             const std::vector<double>& vels, const double radius, const int k,
             const double gps_error, const double heading_error,
             const double trans_error, int32_t gnss_type,
             MapMatchingResult* mm_result,
             PointCandidates* last_pose_referenced_candidates) override;

  void AddRasmapNN(const perception::NnFrame& rasmap_nn) override;

  void AddParallelRoadStatus(const int is_parallel_road) override;

  void AddRoutingSceneType(
      const RoutingSceneType& prev_routing_scene_type) override;

  void AddLinkPR(int pr) override;

  void SetPreviousResult(const std::shared_ptr<const LockOnRoadDebugInfo>&
                             internal_debug_info) override;

  void Reset() override;

  void ResetRoutingMaskYawDecider() override;

  int GraphSize() override;

  virtual void SetRoute(
      const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
          route_response,
      const FMM::NETWORK::CustomGraph& edges) override;
  virtual void SetRoute(const std::string& request_id,
                        const FMM::NETWORK::CustomGraph& edges) override;

 protected:
  std::unique_ptr<FMM::NETWORK::Network> network_ = nullptr;
  std::unique_ptr<FMM::NETWORK::NetworkGraph> graph_ = nullptr;
  std::unique_ptr<FMM::MM::STMATCH> fmm_model_ = nullptr;

  std::string fmm_log_level_ = "log";

  std::vector<FMM::NETWORK::Edge> sub_graph_;

  // FMM::CORE::Trajectory prev_traj_;
  MapMatchingResult prev_matching_result_;
  // PointCandidates prev_referenced_candidates_;
  YawDecider yaw_decider_;
};

void StMatchModel::Reset() {
  fmm_model_.reset(new FMM::MM::STMATCH(*network_, *graph_, fmm_log_level_));
}

void StMatchModel::ResetRoutingMaskYawDecider() { yaw_decider_.Reset(); }

void StMatchModel::SetPreviousResult(
    const std::shared_ptr<const LockOnRoadDebugInfo>& internal_debug_info) {
  prev_matching_result_ = MapMatchingResult();
  // prev_matching_result_.opath.clear();
  for (const auto& pose_debug_info : internal_debug_info->pose_debug_info()) {
    prev_matching_result_.opath.push_back(pose_debug_info.ras_lane_id());
  }
}

int StMatchModel::GraphSize() {
  if (graph_.get() == nullptr) {
    return 0;
  }
  // return sub_graph_.size();
  return graph_->get_network().get_edge_count();
}

void StMatchModel::SetGlobalPlannedRoutes(
    const FMM::NETWORK::CustomGraph& edges) {
  prev_matching_result_ = MapMatchingResult();
  // prev_referenced_candidates_.resize(0);

  network_.reset(new FMM::NETWORK::Network());
  network_->add_edges(edges);

  graph_.reset(new FMM::NETWORK::NetworkGraph(*network_));
  fmm_model_.reset(new FMM::MM::STMATCH(*network_, *graph_, fmm_log_level_));

  sub_graph_ = network_->get_edges();
  MLOG(INFO) << "Added graph edge size: " << sub_graph_.size();

  yaw_decider_.Reset();
};

void StMatchModel::SetRoute(
    const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
        route_response,
    const FMM::NETWORK::CustomGraph& edges) {
  FMM::NETWORK::RouteManager::GetInstance()->UpdateRoute(
      edges, route_response->request_id());
}

void StMatchModel::SetRoute(const std::string& request_id,
                            const FMM::NETWORK::CustomGraph& edges) {
  FMM::NETWORK::RouteManager::GetInstance()->UpdateRoute(edges, request_id);
}

bool StMatchModel::Match(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                         const Vector2dVector& traj,
                         const proto::Projection& projection,
                         const std::vector<double>& headings,
                         const std::vector<double>& timestamps,
                         const std::vector<double>& vels, const double radius,
                         const int k, const double gps_error,
                         const double heading_error, const double trans_error,
                         const int32_t gnss_type, MapMatchingResult* mm_result,
                         PointCandidates* last_pose_referenced_candidates) {
  if (GraphSize() <= 0) {
    mm_result->match_status = FMM::MM::MATCH_STATUS_NULL;
    return false;
  }
  const double factor = 1000;
  const double vmax = 1e6;  // this element is not used during map matching
  FMM::MM::STMATCHConfig config(k, radius, gps_error, heading_error,
                                trans_error, vmax, factor);
  CHECK(config.validate());
  config.print();

  FMM::CORE::LineString line = ParseTrajetoryToLineString(traj);
  FMM::CORE::Trajectory trajectory{0, line, timestamps, headings, vels};

  TrajectoryCandidates traj_candidates;
  const bool is_matching_success = fmm_model_->match_traj(
      trajectory, prev_matching_result_, config, &traj_candidates, mm_result);

  yaw_decider_.IsYaw(rtk, gnss_type, mm_result, fmm_model_.get());

  LogUtil::GetInstance()->RecordNGM(mm_result, rtk, projection, gnss_type,
                                    timestamps.back(), traj.back(),
                                    headings.back());

  if (!is_matching_success || mm_result->opath.empty()) {
    return false;
  }

  // *last_pose_referenced_candidates = traj_candidates.back();
  // prev_referenced_candidates_ = *last_pose_referenced_candidates;

  prev_matching_result_ = *mm_result;

  return true;
}

void StMatchModel::AddRasmapNN(const perception::NnFrame& rasmap_nn) {
  yaw_decider_.AddRasmapNN(rasmap_nn);
}

void StMatchModel::AddParallelRoadStatus(const int is_parallel_road) {
  yaw_decider_.AddParallelRoadStatus(is_parallel_road);
}

void StMatchModel::AddRoutingSceneType(
    const RoutingSceneType& prev_routing_scene_type) {
  yaw_decider_.AddRoutingSceneType(prev_routing_scene_type);
}

void StMatchModel::AddLinkPR(int pr) { yaw_decider_.AddLinkPr(pr); }

std::unique_ptr<MapMatchingModel> CreateStMapMatchingModel(
    const MapMatchingConfig& config) {
  return std::make_unique<StMatchModel>(config);
}

std::unique_ptr<MapMatchingModel> CreateStMapMatchingModel() {
  return std::make_unique<StMatchModel>();
}

///////////////////////////////////////////////////////////////////////////////////////////////////////

}  // namespace localization
}  // namespace deeproute
