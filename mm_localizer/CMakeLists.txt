add_library(localization_mm
        map_matching.cpp
        ddmm_model.cpp
        ddmm_model_post.cpp
        ddmm_shadow_mode.cpp
        ddmm_data_generate.cpp
        utils.cpp
        yaw_decider.cpp
        log_utils.cpp
        log_types.cpp
        log_record.cpp
)
target_link_libraries(localization_mm
        ${FMM_LIBRARY}
        ${Boost_LIBRARIES}
        common_road_map
        localization_data_adapter
        ${engine_lib}
        cudart
        common_event_log
        localization_geometry
        localization_data_adapter
        snappy
)

# add_executable(map_matching_test map_matching_test.cpp)
# target_link_libraries(map_matching_test
#         localization_hmm
#         localization_mm
#         lane_localization_proto
#         ${GTEST_BOTH_LIBRARIES}
#         lam_common_base
#         ${FMM_LIBRARY}
# )
# add_test(map_matching_test ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/map_matching_test)

add_executable(lock_on_road_utils_test utils_test.cpp)
target_link_libraries(lock_on_road_utils_test
        lane_localization_proto
        ${GTEST_BOTH_LIBRARIES}
        lam_common_base
        ${FMM_LIBRARY}
)

install(TARGETS
        localization_mm
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})
