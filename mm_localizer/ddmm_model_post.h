#pragma once
#include <Eigen/Dense>
#include <Eigen/StdVector>
#include <opencv2/opencv.hpp>

#include "map/sd_map.pb.h"

namespace deeproute {
namespace localization {

const double kSegMaskThreshold = 0.01;

const cv::Point kEgoCarPixelPosition(80, 80);

const double kLomDistThreshold = 25;

double AcosProtect(double value);

double ConstrainAngle(const double angle, const bool is_rad);

bool PostProcess(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const Eigen::Vector2d& trajectory_norm, const cv::Point& pred_uv,
    std::vector<uint64_t>& matched_dr_links,
    std::vector<uint64_t>& matched_links);

bool PostProcessByTopo(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Mat& seg_mask,
    const uint64_t lor_navinfo_link_id, const uint64_t prev_lom_link_id,
    std::vector<uint64_t>& matched_dr_links,
    std::vector<uint64_t>& matched_links,
    std::vector<std::vector<cv::Point>>* seg_mask_links,
    std::vector<std::vector<cv::Point>>* topo_out_links,
    std::vector<std::vector<cv::Point>>* overlapped_links,
    uint64_t* selected_link_id, uint64_t* selected_dr_link_id);

bool FindNearestLinkIdByDis(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    uint64_t& closest_id_by_dis, double& min_distance_by_dis);

bool FindNearestLinkIdByAngle(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_and_heading_cost,
    uint64_t& closest_id_by_angle, double& min_distance_by_angle,
    uint64_t& closest_dr_id_by_angle,
    uint64_t& closest_dr_id_by_dis_and_heading);

bool FindNearestLinkIdByAngle(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::vector<uint64_t>& candidate_link_ids,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_and_heading_cost,
    uint64_t& closest_id_by_angle, double& min_distance_by_angle,
    uint64_t& closest_dr_id_by_angle, uint64_t& closest_dr_id_by_dis_and_angle);

void UpdateEveryTopo(
    const uint64_t& inner_pre_link, const uint64_t& inner_after_link,
    std::unordered_map<uint64_t,
                       std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo);

bool GenerateTopo(
    const deeproute::sd_map::SdLinks& local_sd_map,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    std::unordered_map<uint64_t,
                       std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo);

bool GetLastDrLinkTopo(
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const uint64_t& last_dr_link_id, std::vector<uint64_t>& last_dr_link_topo);

bool GetLastDrLinkSimpleTopo(
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const uint64_t& last_dr_link_id, std::vector<uint64_t>& last_dr_link_topo);

uint64_t GetClosestDrLinkId(
    const uint64_t& closest_link_id,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict);

void ClearMatchedInfos(std::vector<uint64_t>& matched_links,
                       std::vector<uint64_t>& matched_dr_links);

void UpdateMatchedInfos(std::vector<uint64_t>& matched_links,
                        std::vector<uint64_t>& matched_dr_links,
                        uint64_t closest_link_id, uint64_t closest_dr_link_id);

}  // namespace localization
}  // namespace deeproute
