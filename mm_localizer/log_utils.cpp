#include "log_utils.h"
#include "log_record.h"

namespace deeproute {
namespace localization {

std::unique_ptr<LogUtil> LogUtil::instance_ = nullptr;

LogUtil::LogUtil() :log_manager_(nullptr) {

}

LogUtil::~LogUtil() {
    LogRecord::ReleaseInstance();
}

LogUtil* LogUtil::GetInstance() {
    if (instance_ == nullptr) {
        instance_ = std::unique_ptr<LogUtil>(new LogUtil());
    }
    return instance_.get();
}

void LogUtil::ReleaseInstance() {
    if (instance_ != nullptr) {
        instance_.reset();
    }
}

void LogUtil::Init(const MapMatchingConfig_LogConfig &log_config) {
    if (nullptr != log_manager_.get()) {
        log_manager_.reset();
    }

    log_manager_ = std::make_unique<LogManager>(LogManager());
    LogRecord::GetInstance()->Init(log_config);
}

void LogUtil::RecordNGM(const MapMatchingResult* mm_result, const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                  const proto::Projection& projection, const int32_t gnss_type, const double tick_time,
                  const Vector2d pos, const double pos_azi) {
    ngm_log_step_ += 1;
    LogForNgm* ngm = (LogForNgm*)log_manager_->getLog(LOG_TYPE_NGM);
    if (ngm == nullptr) {
        return;
    }
    ngm->mm_result_ = mm_result;
    ngm->rtk_ = &rtk;
    ngm->projection_ = &projection;
    ngm->gnss_type_ = gnss_type;
    ngm->tick_time_ = tick_time * 1000 * 1000;
    ngm->local_tick_time_ = tick_time * 1000 * 1000;
    ngm->pos_ = pos;
    ngm->pos_azi_ = pos_azi;

    if (ngm_log_step_ % 100 == 0){
        RecordLog(ngm);
        ngm_log_step_ = 0;
    }
}

void LogUtil::RecordPath(const deeproute::navinfo::SDRoutingResponse* route_response, const proto::Projection* projection) {
    LogForPath* path = (LogForPath*)log_manager_->getLog(LOG_TYPE_PATH);
    if (path == nullptr) {
        return;
    }
    path->tick_time_ = 0;
    path->local_tick_time_ = 0;
    path->route_response_ = route_response;
    path->projection_ = projection;
    RecordLog(path);
}


 void LogUtil::RecordLOR(const LockOnRoadResult* lock_on_road_result) {
    LogForLOR *lor = (LogForLOR*)log_manager_->getLog(LOG_TYPE_LOR);
    lor->tick_time_ = lock_on_road_result->time_us();
    lor->local_tick_time_ = lock_on_road_result->time_us();
    lor->lock_on_road_result_ = lock_on_road_result;
    RecordLog(lor);
 }

 void LogUtil::RecordRTK(const drivers::gnss::Ins* rtk) {
    LogForRTK *log = (LogForRTK*)log_manager_->getLog(LOG_TYPE_RTK);
    log->tick_time_ = rtk->measurement_time();
    log->local_tick_time_ = log->tick_time_;
    log->rtk_ = rtk;
    RecordLog(log);
 }

void LogUtil::RecordLog(LogBase *log) {
    const char *content = log->FormatContent();
    if (content == nullptr || strlen(content) == 0) {
        return;
    }
    MLOG(INFO) << content;
    LogRecord::GetInstance()->RecordFile(content);
}

}  // namespace localization
}  // namespace deeproute