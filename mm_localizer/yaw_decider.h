#pragma once

#include <snappy.h>
#include <sys/types.h>

#include <cstdint>
#include <set>
#include <string>

#include <boost/circular_buffer.hpp>
#include <mm/mm_type.hpp>
#include <mm/stmatch/stmatch_algorithm.hpp>
#include <mm/transition_graph.hpp>
#include <opencv2/opencv.hpp>

#include "drivers/gnss/ins.pb.h"
#include "perception/perception_nn.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "common/log.h"
#include "mm_localizer/utils.h"

namespace deeproute {
namespace localization {

struct DdmmResult {
  uint64_t time;
  uint64_t sd_link_id;
  double pose_to_link_distance;
};

constexpr int kDdmmQueueSize = 10;

class DdmmMeasurements {
 public:
  DdmmMeasurements() {
    history_ddmm_measurements_queue_.set_capacity(kDdmmQueueSize);
  };
  ~DdmmMeasurements() = default;

  void AddDdmmResult(const uint64_t time, const uint64_t sd_link_id,
                     const double pose_to_link_distance) {
    MLOG(INFO) << "DDMM MEASUREMENTS TIME: " << time
               << ", sd_link_id: " << sd_link_id
               << ", pose_to_link_distance: " << pose_to_link_distance;

    history_ddmm_measurements_queue_.push_back(
        DdmmResult{time, sd_link_id, pose_to_link_distance});
  }

  bool IsHistoryDdmmResultTrustworthy(double* mean_dist, uint64_t* sd_link_id) {
    if (!history_ddmm_measurements_queue_.full()) {
      return false;
    }

    // if all history sd link id is not the same
    std::set<uint64_t> sd_link_id_set;
    for (const auto& ddmm_result : history_ddmm_measurements_queue_) {
      sd_link_id_set.insert(ddmm_result.sd_link_id);
    }
    if (sd_link_id_set.size() != 1) {
      return false;
    }
    *sd_link_id = *sd_link_id_set.begin();

    // if mean history dist less than 10m, trust ddmm
    double sum_dist = 0.0;
    for (const auto& ddmm_result : history_ddmm_measurements_queue_) {
      sum_dist += ddmm_result.pose_to_link_distance;
    }
    *mean_dist = sum_dist / history_ddmm_measurements_queue_.size();
    if (*mean_dist > 15.0) {
      return false;
    }

    return true;
  }

 private:
  boost::circular_buffer<DdmmResult> history_ddmm_measurements_queue_;
};

class YawDecider {
 public:
  YawDecider();
  virtual ~YawDecider();

  void AddDdmmResult(const uint64_t time, const uint64_t sd_link_id,
                     const double pose_to_link_distance) {
    MLOG(INFO) << "YAW DECIDER TIME: " << time << ", sd_link_id: " << sd_link_id
               << ", pose_to_link_distance: " << pose_to_link_distance;
    ddmm_measurements_.AddDdmmResult(time, sd_link_id, pose_to_link_distance);
  }

  bool IsYaw(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
             const int32_t gnss_type, FMM::MM::MatchResult* mm_result,
             FMM::MM::STMATCH* fmm_model);

  // bool GetYawSceneType();

  void AddLinkPr(int pr);

  void AddParallelRoadStatus(int paral_status);

  void AddRasmapNN(const perception::NnFrame& rasmap_nn);

  void AddRoutingSceneType(const RoutingSceneType& link_info);

  void Reset() {
    history_para_road_status_.clear();
    history_routing_mask_.clear();
    routing_mask_yaw_status_ = false;
    road_scene_ = "-1";
  }

  void SetUseRoutingMaskYawJudgement(const bool key) {
    enable_routing_mask_yaw_judgement_ = key;
  }

 protected:
  bool CheckRoutingMaskYaw();

  bool ParseNnFrameToRoutingMask(const perception::NnFrame& frame,
                                 cv::Mat& routing_mask, cv::Mat& junction_mask);
  bool IsOnParallelRoad();

  bool IsNotOnParallelRoad();

  bool IsLeavingParallelRoad();

  bool IsEnteringParallelRoad();

  bool IsRtkGood(const std::shared_ptr<const drivers::gnss::Ins>& rtk,
                 const int32_t gnss_type);
  bool IsPassover(FMM::MM::MatchResult* mm_result, FMM::MM::STMATCH* fmm_model);

 private:
  /// base yaw weight from route
  const double off_route_weight_;

  bool enable_routing_mask_yaw_judgement_ = false;

  boost::circular_buffer<drivers::gnss::Ins> history_rtk_poses_;
  boost::circular_buffer<int> history_para_road_status_;
  boost::circular_buffer<cv::Mat> history_routing_mask_;
  boost::circular_buffer<cv::Mat> history_junction_masks_;

  cv::Mat junction_mask_ = cv::Mat::zeros(240, 308, CV_8UC3);

  bool routing_mask_yaw_status_ = false;
  std::string road_scene_ = "-1";
  bool contains_junction_mask_ = false;
  RoutingSceneType prev_routing_scene_type_;
  int link_pr_ = -1;

  // cv::Mat routing_mask_ = cv::Mat::zeros(500, 500, CV_8UC3);
  // cv::Mat bev_ = cv::Mat::zeros(500, 500, CV_8UC3);
  std::string routing_mask_time_ = "-1";

  bool routing_mask_yaw_debug_ = false;
  std::string routing_mask_debug_out_dir_ = "";

 private:
  DdmmMeasurements ddmm_measurements_;
};

}  // namespace localization
}  // namespace deeproute
