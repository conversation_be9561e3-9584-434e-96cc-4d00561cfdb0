import math

def haversine_distance(lat1, lon1, lat2, lon2):
    # Radius of the Earth in meters
    earth_radius = 6371000.0

    # Convert latitude and longitude from degrees to radians
    lat1 = math.radians(lat1)
    lon1 = math.radians(lon1)
    lat2 = math.radians(lat2)
    lon2 = math.radians(lon2)

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    distance = earth_radius * c

    return distance

    longitude_deg: 113.8810761
    latitude_deg: 22.5608197
    
# Example usage:
lat1 = 22.5608197  # Latitude of Point 1
lon1 = 113.8810761  # Longitude of Point 1
lat2 = 22.5618197  # Latitude of Point 2
lon2 = 113.8810761   # Longitude of Point 2

distance = haversine_distance(lat1, lon1, lat2, lon2)
print(f"Distance: {distance} meters")