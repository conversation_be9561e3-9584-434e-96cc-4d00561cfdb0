#pragma once
#include <memory>

#include <boost/circular_buffer.hpp>
#include <opencv2/opencv.hpp>

#include "map/sd_map.pb.h"
#include "perception/perception_nn.pb.h"

#include "common/time_types.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

constexpr int BATCH_SIZE = 1;
constexpr int INPUT_CHANNEL = 14;
constexpr int IMAGE_SIZE = 160;
constexpr int RESOLUTION_RATIO = 1;
constexpr int TRAJECTORY_WINDOW_SIZE = 20;
constexpr int SEG_OUTPUT_CHANNEL = 1;
constexpr int REG_DIM1 = 1;
constexpr int REG_DIM2 = 2;

cv::Mat ConvertSdMapToImage(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const boost::circular_buffer<
        std::pair<::common::TimeMicro, ::common::Transformation3>>&
        gnss_time_and_poses_queue_ddmm);

class DDMMDataGen {
 public:
  virtual ~DDMMDataGen() = default;
  explicit DDMMDataGen();
  bool GenerateInferenceData(
      const deeproute::sd_map::SdLinks& local_sd_map,
      const perception::NnFrame& rasmap_nn,
      const boost::circular_buffer<
          std::pair<::common::TimeMicro, ::common::Transformation3>>&
          gnss_time_and_poses_queue_ddmm,
      const std::unordered_map<uint64_t, std::pair<double, double>>&
          time_to_utm_x_y);
  const std::vector<float>& GetInferenceData() const;

  const std::unordered_map<uint64_t, std::vector<cv::Point>>& GetLinkPtsMap()
      const {
    return id_to_pts_map_;
  };

  const std::unordered_map<uint64_t, double>& GetLinkYawsMap() const {
    return dr_id_to_yaw_map_;
  };

  const std::unordered_map<uint64_t, bool>& GetLinkFlagMap() const {
    return id_to_two_way_flag_;
  };

 private:
  bool ProcessSdmap(const deeproute::sd_map::SdLinks& local_sd_map);

  bool GenerateSDmapTrainingData(
      const deeproute::sd_map::SdLinks& local_sd_map,
      const boost::circular_buffer<
          std::pair<::common::TimeMicro, ::common::Transformation3>>&
          gnss_time_and_poses_queue_ddmm,
      const cv::Mat& bev_img, cv::Mat& sdmap_pts_img, cv::Mat& sdmap_ids_img,
      cv::Mat& sdmap_sin_img, cv::Mat& sdmap_cos_img,
      cv::Mat& sdmap_lane_num_img, cv::Mat& sdmap_cross_pts_img);

  bool GenerateTrajectoryTrainningData(
      const boost::circular_buffer<
          std::pair<::common::TimeMicro, ::common::Transformation3>>&
          gnss_time_and_poses_queue_ddmm,
      const std::unordered_map<uint64_t, std::pair<double, double>>&
          time_to_utm_x_y,
      cv::Mat& traj_pts_img, cv::Mat& traj_orders_img, cv::Mat& traj_gts_img,
      cv::Mat& traj_sin_img, cv::Mat& traj_cos_img);

  bool GenerateFeatrueMapData(const perception::NnFrame& rasmap_nn,
                              cv::Mat& bev_img);

  std::vector<float> inference_data_;
  std::unordered_map<uint64_t, std::vector<cv::Point>> id_to_pts_map_;
  std::unordered_map<uint64_t, double> dr_id_to_yaw_map_;
  std::unordered_map<uint64_t, bool> id_to_two_way_flag_;

  bool ddmm_debug_mode_ = false;
  std::string ddmm_debug_out_dir_;
};

std::unique_ptr<DDMMDataGen> CreateDDMMDataGen();

}  // namespace localization
}  // namespace deeproute
