#include "mm_localizer/ddmm_shadow_mode.h"

#include "common/event_log_handle.h"
#include "common/log.h"

namespace deeproute {
namespace localization {

void DdmmShadowMode::AddLocalSdMap(const deeproute::sd_map::SdLinks& sd_links) {
  for (const auto& link : sd_links.links()) {
    local_sd_link_ids_.insert(std::to_string(link.navinfo_id()));
  }
}

void DdmmShadowMode::AddLockOnRoadResult(const LockOnRoadResult& lor_result) {
  time_to_lor_result_[lor_result.time_us()] = lor_result;
}

void DdmmShadowMode::AddDdmmResult(const LockOnRoadResult& ddmm_result) {
  ddmm_result_ = ddmm_result;

  // if no corresponding lock on road result found, neglect
  if (time_to_lor_result_.find(ddmm_result.time_us()) ==
      time_to_lor_result_.end()) {
    MLOG(INFO) << "No corresponding lock on road result for ddmm result.";
    return;
  } else {
    MLOG_EVERY(INFO, 100) << "[DDMM shadow mode], link match found for: "
                          << ddmm_result.time_us()
                          << ", time_to_lor_result_ size: "
                          << time_to_lor_result_.size();

    // clear history lock on road results whose time is earlier than ddmm result
    auto it = time_to_lor_result_.begin();
    while (it != time_to_lor_result_.end()) {
      if (it->first < ddmm_result.time_us()) {
        it = time_to_lor_result_.erase(it);
      } else {
        ++it;
      }
    }
  }

  LockOnRoadResult& lor_result = time_to_lor_result_[ddmm_result.time_us()];

  // if corresponding lock on road result is not NORMAL, neglect
  if (lor_result.status() != LockOnRoadResult::NORMAL) {
    MLOG(INFO) << "Corresponding LOR result abnormal, disable shadow mode.";
    return;
  }

  // if corresponding ddmm result is not NORMAL, neglect
  if (ddmm_result_.status() != LockOnRoadResult::NORMAL) {
    MLOG(INFO) << "Corresponding DDMM result abnormal, disable shadow mode.";
    return;
  }

  // if corresponding lock on road link id is not found in queried local sd map,
  // neglect
  if (local_sd_link_ids_.find(lor_result.sd_link_id()) ==
      local_sd_link_ids_.end()) {
    MLOG(INFO) << "Corresponding LOR link id not found in local sd map, "
                  "disable shadow mode, lor link id: "
               << lor_result.sd_link_id();
    missing_sd_link_id_in_local_sd_map_num_ += 1;
    MLOG(INFO) << "missing_sd_link_id_in_local_sd_map_num_: "
               << missing_sd_link_id_in_local_sd_map_num_;
    return;
  }

  ddmm_result_.mutable_shadow_mode_result()->set_lock_on_road_link_id(
      lor_result.sd_link_id());
  ddmm_result_.mutable_shadow_mode_result()->set_ddmm_link_id(
      ddmm_result.sd_link_id());
  if (lor_result.sd_link_id() != ddmm_result.sd_link_id()) {
    ddmm_result_.mutable_shadow_mode_result()->set_status(
        LockOnRoadResult::ShadowMode::MISMATCH);
    trip_mismatch_num_ += 1;
  } else {
    ddmm_result_.mutable_shadow_mode_result()->set_status(
        LockOnRoadResult::ShadowMode::MATCH);
    trip_match_num_ += 1;
  }

  const int sum = trip_match_num_ + trip_mismatch_num_;
  const double mismatch_ratio =
      static_cast<double>(trip_mismatch_num_) / static_cast<double>(sum);
  MLOG_EVERY(INFO, 20) << "[DDMM shadow mode], Link mismatch ratio: "
                       << mismatch_ratio << ", failed: " << trip_mismatch_num_
                       << ", sum: " << sum;
  constexpr double kMismatchRatioThreshold = 0.9;
  if (mismatch_ratio < kMismatchRatioThreshold && !status_reported) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_RUNTIME_DDMM_PERFORMANCE_POOR);
    status_reported = true;
  }
}

std::unique_ptr<DdmmShadowMode> CreateDdmmShadowMode() {
  return std::make_unique<DdmmShadowMode>();
}

}  // namespace localization
}  // namespace deeproute
