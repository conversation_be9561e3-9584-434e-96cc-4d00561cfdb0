#include "ddmm_model_post.h"

#include <cstdint>
#include <iostream>
#include <set>
#include <string>
#include <unordered_map>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/StdVector>
#include <boost/filesystem.hpp>

#include "common/log.h"

namespace deeproute {
namespace localization {

constexpr double kDegreePerRad = 57.29577951308232;

double ConstrainAngle(const double angle, const bool is_rad) {
  double temp = angle;
  if (is_rad) {
    temp *= 57.3;
  }

  temp = fmod(temp + 180, 360);
  if (temp < 0) temp += 360;
  return std::abs(temp - 180);
}

uint64_t GetKeyByValue(
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    uint64_t value);

bool PostProcessMatchCase(
    const uint64_t& closest_link_id_by_dis,
    const uint64_t& closest_dr_link_id_by_angle,
    std::unordered_map<uint64_t, std::vector<uint64_t>> sd_link_to_dr_links,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links);

bool PostProcessAngleDisSmallCase(
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    const uint64_t& closest_link_id_by_angle,
    const uint64_t& closest_dr_link_id_by_angle,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links);

bool PostProcessOtherCases(
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    std::unordered_map<uint64_t, double> dr_link_id_to_traj_angle_dict,
    const uint64_t& closest_link_id_by_dis,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links);

double AcosProtect(double value) {
  return std::max(-1.0, std::min(1.0, value));
}

double CalculateDistance(const cv::Point& p1, const cv::Point& p2) {
  int dx = p1.x - p2.x;
  int dy = p1.y - p2.y;
  return std::sqrt(dx * dx + dy * dy);
}

bool PostProcess(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Point& pred_uv,
    std::vector<uint64_t>& matched_dr_links,
    std::vector<uint64_t>& matched_links) {
  const auto start_time = std::chrono::system_clock::now();
  MLOG(DEBUG) << "matched_links size = " << matched_links.size()
              << ",matched_dr_links size = " << matched_dr_links.size();
  std::unordered_map<uint64_t, std::vector<uint64_t>> sd_link_to_dr_links;
  std::unordered_map<uint64_t,
                     std::unordered_map<std::string, std::vector<uint64_t>>>
      dr_links_topo;

  std::unordered_map<uint64_t, double> dr_link_id_to_dis_by_dis_dict;
  std::unordered_map<uint64_t, double> dr_link_id_to_dis_by_angle_dict;
  std::unordered_map<uint64_t, double> dr_link_id_to_dist_and_angle_dict;
  std::unordered_map<uint64_t, double> dr_link_id_to_traj_angle_dict;

  double min_dis_by_dis = std::numeric_limits<double>::max();
  double min_dis_by_dis_by_angle = std::numeric_limits<double>::max();

  uint64_t closest_id_by_dis = 0;
  uint64_t closest_id_by_angle = 0;
  uint64_t closest_dr_id_by_angle = 0;
  uint64_t closest_dr_id_by_dist_and_heading = 0;

  // NOTE(SHR): sd_link_to_dr_links: navinfo_id -> [dr_link_id]
  GenerateTopo(local_sd_map, sd_link_to_dr_links, dr_links_topo);
  bool dis_flag = FindNearestLinkIdByDis(id_to_pts_map, sd_link_to_dr_links,
                                         pred_uv, dr_link_id_to_dis_by_dis_dict,
                                         closest_id_by_dis, min_dis_by_dis);

  bool angle_flag = FindNearestLinkIdByAngle(
      id_to_pts_map, sd_link_to_dr_links, dr_id_to_yaw_map, trajectory_yaw,
      pred_uv, dr_link_id_to_dis_by_angle_dict, dr_link_id_to_traj_angle_dict,
      dr_link_id_to_dist_and_angle_dict, closest_id_by_angle,
      min_dis_by_dis_by_angle, closest_dr_id_by_angle,
      closest_dr_id_by_dist_and_heading);

  if (!dis_flag || !angle_flag) {
    MLOG(INFO) << "dis_flag = " << dis_flag << ",angle_flag = " << angle_flag;
    return false;
  }
  MLOG(DEBUG) << "id_by_dis " << closest_id_by_dis
              << ",min_dis = " << min_dis_by_dis
              << "id_by_angle = " << closest_id_by_angle
              << ",dr_id_by_angle = " << closest_dr_id_by_angle
              << ",min_dis_by_angle = " << min_dis_by_dis_by_angle;
  if (closest_id_by_dis == closest_id_by_angle) {
    MLOG(DEBUG) << "case1";
    bool success = PostProcessMatchCase(
        closest_id_by_dis, closest_dr_id_by_angle, sd_link_to_dr_links,
        dr_links_topo, dr_link_id_to_dis_by_dis_dict, matched_links,
        matched_dr_links);
    if (success == false) {
      MLOG(ERROR) << "PostProcessMatchCase failed";
      return false;
    }
  } else if (min_dis_by_dis_by_angle <= 5) {
    MLOG(DEBUG) << "case2";
    bool success = PostProcessAngleDisSmallCase(
        dr_link_id_to_dis_by_dis_dict, dr_link_id_to_dis_by_angle_dict,
        dr_links_topo, sd_link_to_dr_links, closest_id_by_angle,
        closest_dr_id_by_angle, matched_links, matched_dr_links);
    if (success == false) {
      MLOG(ERROR) << "PostProcessAngleDisSmallCase failed";
      return false;
    }
  } else {
    MLOG(DEBUG) << "case3";
    bool success = PostProcessOtherCases(
        dr_link_id_to_dis_by_dis_dict, dr_links_topo, sd_link_to_dr_links,
        dr_link_id_to_traj_angle_dict, closest_id_by_dis, matched_links,
        matched_dr_links);

    if (success == false) {
      MLOG(ERROR) << "PostProcessOtherCases failed";
      return false;
    }
  }

  if (matched_dr_links.size() != matched_links.size()) {
    MLOG(INFO) << "should not happend, matched_dr_links size = "
               << matched_dr_links.size()
               << ",matched_links size = " << matched_links.size();
  } else {
    if (matched_dr_links.size() > 5) {
      matched_dr_links.erase(matched_dr_links.begin());
      matched_links.erase(matched_links.begin());
    }
  }

  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "post process using = "
             << std::chrono::duration<double>(end_time - start_time).count()
             << " seconds.";
  return true;
}

namespace {
std::vector<uint64_t> FindLinkIdsByPoints(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::vector<cv::Point>& points) {
  std::vector<uint64_t> correspondingIds;

  for (const auto& pair : id_to_pts_map) {
    const std::vector<cv::Point>& pointVector = pair.second;
    for (const cv::Point& point : points) {
      if (std::find(pointVector.begin(), pointVector.end(), point) !=
          pointVector.end()) {
        correspondingIds.push_back(pair.first);
        break;
      }
    }
  }

  return correspondingIds;
}

std::vector<uint64_t> FindCommonValues(const std::vector<uint64_t>& vec1,
                                       const std::vector<uint64_t>& vec2) {
  std::unordered_set<uint64_t> set(vec1.begin(), vec1.end());
  std::vector<uint64_t> common_values;

  // Sort the vectors
  std::vector<uint64_t> sorted_vec1 = vec1;
  std::vector<uint64_t> sorted_vec2 = vec2;
  std::sort(sorted_vec1.begin(), sorted_vec1.end());
  std::sort(sorted_vec2.begin(), sorted_vec2.end());

  // Find common values using set_intersection
  std::set_intersection(sorted_vec1.begin(), sorted_vec1.end(),
                        sorted_vec2.begin(), sorted_vec2.end(),
                        std::back_inserter(common_values));

  // for (const auto& seg_link_id : vec1) {
  //   MLOG(INFO) << "DDMM DEBUG candidate seglink id: " << seg_link_id;
  // }

  // for (const auto& topo_link_id : vec2) {
  //   MLOG(INFO) << "DDMM DEBUG candidate topo link id: " << topo_link_id;
  // }

  // for (const auto& common : common_values) {
  //   MLOG(INFO) << "DDMM DEBUG common link id: " << common;
  // }

  return common_values;
}

// std::vector<uint64_t> GetTopologicalLinks(
//     const std::unordered_map<
//         uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
//         dr_links_topo,
//     const std::unordered_map<uint64_t, std::vector<uint64_t>>&
//         sd_link_to_dr_links,
//     const uint64_t& sd_link_id, const std::string& direction) {
//   std::vector<uint64_t> dr_sd_link_ids;
//   if (sd_link_to_dr_links.find(sd_link_id) != sd_link_to_dr_links.end()) {
//     dr_sd_link_ids = sd_link_to_dr_links.at(sd_link_id);
//   }

//   std::vector<uint64_t> out_link_ids;
//   for (const auto& dr_sd_link_id : dr_sd_link_ids) {
//     out_link_ids = dr_links_topo.at(dr_sd_link_id).at(direction);
//     for (const auto& first_level_out_link : out_link_ids) {
//       // candidate_dr_link_ids.push_back(out_link);
//       auto second_level_out_links =
//           dr_links_topo.at(first_level_out_link).at(direction);
//       out_link_ids.insert(out_link_ids.end(), second_level_out_links.begin(),
//                           second_level_out_links.end());
//     }
//   }

//   return out_link_ids;
// }

std::vector<uint64_t> GetTopologicalLinks(
    std::unordered_map<uint64_t,
                       std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    const uint64_t& sd_link_id, const std::string& direction) {
  MLOG(INFO) << "DDMM seed sd link id: " << sd_link_id;
  std::vector<uint64_t> dr_sd_link_ids;
  if (sd_link_to_dr_links.find(sd_link_id) != sd_link_to_dr_links.end()) {
    dr_sd_link_ids = sd_link_to_dr_links[sd_link_id];
  } else {
    MLOG(INFO) << "DDMM sd link id not in sd link to dr link dict";
  }

  std::vector<uint64_t> out_link_ids;

  for (const auto& dr_sd_link_id : dr_sd_link_ids) {
    out_link_ids.push_back(dr_sd_link_id);
    const auto first_level_out_links = dr_links_topo[dr_sd_link_id][direction];

    for (const auto& first_level_out_link : first_level_out_links) {
      out_link_ids.push_back(first_level_out_link);

      auto second_level_out_links =
          dr_links_topo[first_level_out_link][direction];
      out_link_ids.insert(out_link_ids.end(), second_level_out_links.begin(),
                          second_level_out_links.end());
    }
  }

  return out_link_ids;
}

std::vector<uint64_t> GetDrlinkIdsFromNavinfoIds(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const std::vector<uint64_t>& navinfo_link_ids) {
  std::vector<uint64_t> dr_link_ids;
  for (const auto& link : local_sd_map.links()) {
    if (std::find(navinfo_link_ids.begin(), navinfo_link_ids.end(),
                  link.navinfo_id()) != navinfo_link_ids.end()) {
      dr_link_ids.push_back(link.dr_link_id());
    }
  }
  return dr_link_ids;
}

}  // namespace

bool PostProcessByTopo(
    const deeproute::sd_map::SdLinks& local_sd_map,
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Mat& seg_mask,
    const uint64_t lor_navinfo_link_id, const uint64_t prev_lom_link_id,
    std::vector<uint64_t>& matched_dr_links,
    std::vector<uint64_t>& matched_links,
    std::vector<std::vector<cv::Point>>* seg_mask_links,
    std::vector<std::vector<cv::Point>>* topo_out_links,
    std::vector<std::vector<cv::Point>>* overlapped_links,
    uint64_t* selected_link_id, uint64_t* selected_dr_link_id) {
  const auto start_time = std::chrono::system_clock::now();
  MLOG(DEBUG) << "matched_links size = " << matched_links.size()
              << ",matched_dr_links size = " << matched_dr_links.size();
  const auto dr_link_id_to_utm_heading_map = dr_id_to_yaw_map;

  std::unordered_map<uint64_t, std::vector<uint64_t>> sd_link_to_dr_links;
  std::unordered_map<uint64_t,
                     std::unordered_map<std::string, std::vector<uint64_t>>>
      dr_links_topo;

  // NOTE(SHR): sd_link_to_dr_links: navinfo_id -> [dr_link_id]
  // step 1: generate link topo
  // sample: dr_links_topo['LINK_ID']["in"] = []
  //         dr_links_topo['LINK_ID']["out"] = []
  GenerateTopo(local_sd_map, sd_link_to_dr_links, dr_links_topo);

  std::unordered_map<uint64_t, uint64_t> dr_link_id_to_sd_link_id;
  for (const auto& pair : sd_link_to_dr_links) {
    uint64_t sd_link = pair.first;
    const std::vector<uint64_t>& dr_links = pair.second;

    for (uint64_t dr_link : dr_links) {
      dr_link_id_to_sd_link_id[dr_link] = sd_link;
    }
  }

  // step 2
  // find non zero values in segmentation mask
  cv::Mat seg_mask_copy = seg_mask.clone();
  cv::Mat seg_non_zero_mask;
  double max_confidence = 255;
  cv::minMaxLoc(seg_mask_copy, nullptr, &max_confidence);
  // cv::threshold(seg_mask_copy, seg_mask_copy,
  //               max_confidence * kSegMaskThreshold, 255, cv::THRESH_BINARY);
  seg_non_zero_mask = seg_mask_copy.clone();
  cv::findNonZero(seg_mask_copy, seg_non_zero_mask);
  std::vector<cv::Point> seg_salient_points;
  for (int i = 0; i < seg_non_zero_mask.rows; i++) {
    cv::Point p = seg_non_zero_mask.at<cv::Point>(i);

    // if distance to ego car is less than 50, add to seg_salient_points
    const double distance_to_ego_car =
        CalculateDistance(p, kEgoCarPixelPosition);
    if (distance_to_ego_car > kLomDistThreshold) {
      continue;
    }

    seg_salient_points.push_back(p);
  }

  // step 3
  // find links that are covered by segmentation mask
  const auto candidate_seg_navinfo_link_ids =
      FindLinkIdsByPoints(id_to_pts_map, seg_salient_points);
  const auto candidate_seg_dr_link_ids =
      GetDrlinkIdsFromNavinfoIds(local_sd_map, candidate_seg_navinfo_link_ids);
  for (const auto& link_id : candidate_seg_navinfo_link_ids) {
    seg_mask_links->push_back(id_to_pts_map.at(link_id));
  }

  // step 4
  // get candidate drlink ids from candidate navinfo link ids
  // std::vector<uint64_t> candidate_dr_link_ids;

  // step 5
  // if LOM HMM link exists: find links that are topologically connected to LOR
  // HMM link if LOR HMM link does not exist:
  std::vector<uint64_t> out_dr_link_ids;

  if (prev_lom_link_id != 0) {
    MLOG(INFO) << "DDMM DEBUG POST PROCESS DEBUG: prev_lom_link_id: "
               << prev_lom_link_id;

    out_dr_link_ids = GetTopologicalLinks(dr_links_topo, sd_link_to_dr_links,
                                          prev_lom_link_id, "out");

  } else {
    MLOG(INFO) << "DDMM DEBUG POST PROCESS DEBUG: prev_lom_link_id: "
               << prev_lom_link_id << ", reinit.";

    // re-initialization
    // uint64_t closest_id_by_dis = 0;
    uint64_t closest_id_by_angle = 0;
    uint64_t closest_dr_id_by_angle = 0;
    uint64_t closest_dr_id_by_dist_and_angle = 0;
    double min_dis_by_dis_by_angle = std::numeric_limits<double>::max();

    std::unordered_map<uint64_t, double> dr_link_id_to_dis_by_angle_dict;
    std::unordered_map<uint64_t, double> dr_link_id_to_traj_angle_dict;
    std::unordered_map<uint64_t, double> dr_link_id_to_dist_and_angle_dict;

    const bool find_valid_link_ret = FindNearestLinkIdByAngle(
        id_to_pts_map, sd_link_to_dr_links, dr_link_id_to_utm_heading_map,
        trajectory_yaw, kEgoCarPixelPosition, dr_link_id_to_dis_by_angle_dict,
        dr_link_id_to_traj_angle_dict, dr_link_id_to_dist_and_angle_dict,
        closest_id_by_angle, min_dis_by_dis_by_angle, closest_dr_id_by_angle,
        closest_dr_id_by_dist_and_angle);
    if (!find_valid_link_ret) {
      return false;
    }

    MLOG(INFO) << "DDMM DEBUG closest_dr_id_by_dist_and_angle: "
               << closest_dr_id_by_dist_and_angle;

    // if closest link by angle is not found, return false
    if (dr_links_topo.find(closest_dr_id_by_dist_and_angle) ==
        dr_links_topo.end()) {
      MLOG(WARN) << "DDMM DEBUG, cannot find dr id by angle";
      return false;
    }

    out_dr_link_ids = GetTopologicalLinks(
        dr_links_topo, sd_link_to_dr_links,
        dr_link_id_to_sd_link_id.at(closest_dr_id_by_dist_and_angle), "out");
  }

  for (const auto& link_id : out_dr_link_ids) {
    if (dr_link_id_to_sd_link_id.find(link_id) ==
        dr_link_id_to_sd_link_id.end()) {
      continue;
    }

    const auto& sd_link_id = dr_link_id_to_sd_link_id[link_id];

    if (id_to_pts_map.find(sd_link_id) == id_to_pts_map.end()) {
      continue;
    }

    topo_out_links->push_back(
        id_to_pts_map.at(dr_link_id_to_sd_link_id[link_id]));
  }

  // step 6
  // find intersection of candidate_navinfo_link_ids and out_links
  auto common_link_ids =
      FindCommonValues(candidate_seg_navinfo_link_ids, out_dr_link_ids);
  if (common_link_ids.empty()) {
    MLOG(INFO) << "DDMM DEBUG no common links found.";
  } else {
    MLOG(INFO) << "DDMM DEBUG, found: " << common_link_ids.size()
               << " common links.";
  }

  // if no common links found, earth still needs to rotates, we need to trust
  // geometry, use closest link by dist and angle
  if (common_link_ids.empty()) {
    MLOG(INFO) << "DDMM DEBUG, NO COMMON LINKS FOUND! Use closest link by dist "
                  "and angle.";
    // common_link_ids = out_dr_link_ids;
    common_link_ids = candidate_seg_dr_link_ids;
    // common_link_ids.push_back(prev_lom_link_id);
  }

  for (const auto& common_dr_link_id : common_link_ids) {
    const auto sd_link_id = dr_link_id_to_sd_link_id[common_dr_link_id];

    if (id_to_pts_map.find(sd_link_id) == id_to_pts_map.end()) {
      continue;
    }
    overlapped_links->push_back(id_to_pts_map.at(sd_link_id));
  }

  matched_links = common_link_ids;
  matched_dr_links = common_link_ids;

  uint64_t closest_id_by_angle = 0;
  uint64_t closest_dr_id_by_angle = 0;
  uint64_t closest_dr_id_by_dist_and_angle = 0;
  double min_dis_by_dis_by_angle = std::numeric_limits<double>::max();
  std::unordered_map<uint64_t, double> dr_link_id_to_dis_by_angle_dict;
  std::unordered_map<uint64_t, double> dr_link_id_to_traj_angle_dict;
  std::unordered_map<uint64_t, double> dr_link_id_to_traj_dist_and_angle_dict;
  const bool find_valid_link_ret = FindNearestLinkIdByAngle(
      id_to_pts_map, common_link_ids, sd_link_to_dr_links,
      dr_link_id_to_utm_heading_map, trajectory_yaw, kEgoCarPixelPosition,
      dr_link_id_to_dis_by_angle_dict, dr_link_id_to_traj_angle_dict,
      dr_link_id_to_traj_dist_and_angle_dict, closest_id_by_angle,
      min_dis_by_dis_by_angle, closest_dr_id_by_angle,
      closest_dr_id_by_dist_and_angle);

  *selected_link_id = closest_dr_id_by_dist_and_angle;
  *selected_dr_link_id = closest_dr_id_by_dist_and_angle;

  if (!find_valid_link_ret) {
    MLOG(WARN) << "DDMM DEBUG: selected link failed!";
    return false;
  } else {
    MLOG(INFO) << "DDMM DEBUG: selected link id: " << *selected_link_id << ", "
               << *selected_dr_link_id;
  }
  // CHECK(*selected_link_id != 0);

  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "post process using = "
             << std::chrono::duration<double>(end_time - start_time).count()
             << " seconds.";

  return true;
}

bool PostProcessOtherCases(
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    std::unordered_map<uint64_t, double> dr_link_id_to_traj_angle_dict,
    const uint64_t& closest_link_id_by_dis,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links) {
  if (matched_dr_links.size() >= 1) {
    uint64_t last_dr_link_id = matched_dr_links.back();
    std::vector<uint64_t> last_dr_link_simple_topo;

    bool last_dr_link_in_links_topo =
        dr_links_topo.find(last_dr_link_id) != dr_links_topo.end();

    if (!last_dr_link_in_links_topo) {
      MLOG(DEBUG) << "no topo in last dr link id ";
      uint64_t closest_dr_link_id =
          GetClosestDrLinkId(closest_link_id_by_dis, sd_link_to_dr_links,
                             dr_link_id_to_traj_angle_dict);
      UpdateMatchedInfos(matched_links, matched_dr_links,
                         closest_link_id_by_dis, closest_dr_link_id);
    } else {
      bool success = GetLastDrLinkSimpleTopo(dr_links_topo, last_dr_link_id,
                                             last_dr_link_simple_topo);
      if (success == false) {
        MLOG(ERROR) << "GetLastDrLinkSimpleTopo fail";
        return false;
      }
      std::unordered_map<uint64_t, double> topo_link_to_dis_dict;
      for (const auto& topo_link : last_dr_link_simple_topo) {
        if (dr_link_id_to_dis_by_dis_dict.count(topo_link) > 0) {
          topo_link_to_dis_dict[topo_link] =
              dr_link_id_to_dis_by_dis_dict.at(topo_link);
        }
      }
      // MLOG(INFO) << "topo_link_to_dis_dict = " << topo_link_to_dis_dict;
      if (topo_link_to_dis_dict.size() >= 1) {
        std::vector<std::pair<uint64_t, double>> sorted_topo_link_to_dis(
            topo_link_to_dis_dict.begin(), topo_link_to_dis_dict.end());
        std::sort(
            sorted_topo_link_to_dis.begin(), sorted_topo_link_to_dis.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; });
        uint64_t dr_link_id = sorted_topo_link_to_dis.front().first;
        double dr_link_id_dis = sorted_topo_link_to_dis.front().second;
        MLOG(DEBUG) << "dr_link_id = " << dr_link_id
                    << ", dr_link_id_dis = " << dr_link_id_dis;
        if (dr_link_id_dis < 10) {
          MLOG(DEBUG) << ("simple topo dis < 10");
          uint64_t closest_link_id =
              GetKeyByValue(sd_link_to_dr_links, dr_link_id);
          UpdateMatchedInfos(matched_links, matched_dr_links, closest_link_id,
                             dr_link_id);
        } else {
          MLOG(DEBUG)
              << "dis_by_angle is big, topo by last dr is also big, using "
                 "id_by_dis";
          uint64_t closest_dr_link_id =
              GetClosestDrLinkId(closest_link_id_by_dis, sd_link_to_dr_links,
                                 dr_link_id_to_traj_angle_dict);
          UpdateMatchedInfos(matched_links, matched_dr_links,
                             closest_link_id_by_dis, closest_dr_link_id);
        }
      } else {
        MLOG(DEBUG) << "no topo in last dr link id ";
        uint64_t closest_dr_link_id =
            GetClosestDrLinkId(closest_link_id_by_dis, sd_link_to_dr_links,
                               dr_link_id_to_traj_angle_dict);
        UpdateMatchedInfos(matched_links, matched_dr_links,
                           closest_link_id_by_dis, closest_dr_link_id);
      }
    }
  } else {
    MLOG(DEBUG) << "other case ,len < 1";
    uint64_t closest_dr_link_id =
        GetClosestDrLinkId(closest_link_id_by_dis, sd_link_to_dr_links,
                           dr_link_id_to_traj_angle_dict);
    UpdateMatchedInfos(matched_links, matched_dr_links, closest_link_id_by_dis,
                       closest_dr_link_id);
  }
  return true;
}

bool PostProcessAngleDisSmallCase(
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    const uint64_t& closest_link_id_by_angle,
    const uint64_t& closest_dr_link_id_by_angle,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links) {
  if (matched_dr_links.size() >= 1) {
    uint64_t last_dr_link_id = matched_dr_links.back();
    bool equal_last_dr_link_id = false;
    if (closest_dr_link_id_by_angle == last_dr_link_id) {
      UpdateMatchedInfos(matched_links, matched_dr_links,
                         closest_link_id_by_angle, closest_dr_link_id_by_angle);
      equal_last_dr_link_id = true;
      MLOG(DEBUG) << "dis not equal angle, angle id equal last_dr_link_id";
    }
    bool last_dr_link_in_links_topo =
        dr_links_topo.find(last_dr_link_id) != dr_links_topo.end();

    if (!equal_last_dr_link_id) {
      if (dr_link_id_to_dis_by_angle_dict.find(last_dr_link_id) !=
          dr_link_id_to_dis_by_angle_dict.end()) {
        if (dr_link_id_to_dis_by_angle_dict.at(last_dr_link_id) < 5) {
          UpdateMatchedInfos(matched_links, matched_dr_links,
                             matched_links.back(), last_dr_link_id);
          MLOG(DEBUG) << "dis not equal angle, near with last_dr_link_id";
        }
      } else if (last_dr_link_in_links_topo) {
        std::unordered_map<uint64_t, double> topo_link_to_dis_dict;
        std::vector<uint64_t> last_dr_link_topo;
        bool success = GetLastDrLinkTopo(dr_links_topo, last_dr_link_id,
                                         last_dr_link_topo);
        if (success == false) {
          MLOG(ERROR) << "GetLastDrLinkTopo failed";
          return false;
        }
        for (const uint64_t& topo_link : last_dr_link_topo) {
          if (dr_link_id_to_dis_by_dis_dict.find(topo_link) !=
              dr_link_id_to_dis_by_dis_dict.end()) {
            topo_link_to_dis_dict[topo_link] =
                dr_link_id_to_dis_by_dis_dict.at(topo_link);
          }
        }
        if (topo_link_to_dis_dict.size() > 0) {
          std::vector<std::pair<uint64_t, double>> sorted_topo_link_to_dis(
              topo_link_to_dis_dict.begin(), topo_link_to_dis_dict.end());
          std::sort(
              sorted_topo_link_to_dis.begin(), sorted_topo_link_to_dis.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });
          uint64_t dr_link_id = sorted_topo_link_to_dis.front().first;
          double dr_link_id_dis = sorted_topo_link_to_dis.front().second;
          MLOG(DEBUG) << "dr_link_id = " << dr_link_id
                      << ", dr_link_id_dis = " << dr_link_id_dis;
          if (dr_link_id_dis < 10) {
            MLOG(DEBUG) << "dis not equal angle, min topo link ids is < 10";
            uint64_t closest_link_id =
                GetKeyByValue(sd_link_to_dr_links, dr_link_id);
            UpdateMatchedInfos(matched_links, matched_dr_links, closest_link_id,
                               dr_link_id);
          }
        } else {
          MLOG(DEBUG)
              << "dis not equal angle, topo dis big than 10, use default";
          UpdateMatchedInfos(matched_links, matched_dr_links,
                             closest_link_id_by_angle,
                             closest_dr_link_id_by_angle);
        }
      } else {
        MLOG(DEBUG) << "dis not equal angle, has no history topo info";
        UpdateMatchedInfos(matched_links, matched_dr_links,
                           closest_link_id_by_angle,
                           closest_dr_link_id_by_angle);
      }
    }
  } else {
    MLOG(DEBUG) << "dis not equal angle, has no history info";
    UpdateMatchedInfos(matched_links, matched_dr_links,
                       closest_link_id_by_angle, closest_dr_link_id_by_angle);
  }
  return true;
}

bool PostProcessMatchCase(
    const uint64_t& closest_link_id_by_dis,
    const uint64_t& closest_dr_link_id_by_angle,
    std::unordered_map<uint64_t, std::vector<uint64_t>> sd_link_to_dr_links,
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    std::vector<uint64_t>& matched_links,
    std::vector<uint64_t>& matched_dr_links) {
  MLOG(DEBUG) << "closest_link_id_by_angle == closest_link_id_by_dis";
  if (matched_dr_links.size() < 1) {
    UpdateMatchedInfos(matched_links, matched_dr_links, closest_link_id_by_dis,
                       closest_dr_link_id_by_angle);
    MLOG(DEBUG) << "dis equal angle, len matched_dr_links < 1";
  } else {
    uint64_t last_dr_link_id = matched_dr_links.back();
    // bool topo_is_continous = false;
    MLOG(DEBUG) << "last_dr_link_id = " << last_dr_link_id;
    std::vector<uint64_t> last_dr_link_topo;
    bool last_dr_link_in_links_topo =
        dr_links_topo.find(last_dr_link_id) != dr_links_topo.end();

    if (last_dr_link_in_links_topo) {
      bool success =
          GetLastDrLinkTopo(dr_links_topo, last_dr_link_id, last_dr_link_topo);
      if (success == false) {
        MLOG(ERROR) << "GetLastDrLinkTopo failed";
        return false;
      }
    }

    bool last_dr_link_id_in_flag =
        dr_link_id_to_dis_by_dis_dict.find(last_dr_link_id) !=
        dr_link_id_to_dis_by_dis_dict.end();
    double last_dr_link_dis = std::numeric_limits<double>::max();
    if (last_dr_link_id_in_flag) {
      last_dr_link_dis = dr_link_id_to_dis_by_dis_dict.at(last_dr_link_id);
    }
    MLOG(DEBUG) << "last_dr_link_id_in_flag = " << last_dr_link_id_in_flag;
    if (last_dr_link_in_links_topo == false) {
      UpdateMatchedInfos(matched_links, matched_dr_links,
                         closest_link_id_by_dis, closest_dr_link_id_by_angle);
      MLOG(DEBUG) << "dis equal angle ,last dr not in current topo";
    } else if (std::find(last_dr_link_topo.begin(), last_dr_link_topo.end(),
                         closest_dr_link_id_by_angle) !=
               last_dr_link_topo.end()) {
      UpdateMatchedInfos(matched_links, matched_dr_links,
                         closest_link_id_by_dis, closest_dr_link_id_by_angle);
      MLOG(DEBUG) << "dis equal angle ,in topo, closest_dr_link_id_by_angle = "
                  << closest_dr_link_id_by_angle;
    } else if (last_dr_link_id_in_flag) {
      MLOG(DEBUG) << "dis equal angle ,out topo";
      if (last_dr_link_dis <= 5) {
        UpdateMatchedInfos(matched_links, matched_dr_links,
                           matched_links.back(), last_dr_link_id);
        MLOG(DEBUG) << "set to last dr link, last_dr_link_id = "
                    << last_dr_link_id;
        MLOG(DEBUG) << "topo is next topo, last_dr_link_dis = "
                    << last_dr_link_dis;
      } else {
        std::unordered_map<uint64_t, double> topo_link_to_dis_dict;
        for (const auto topo_link : last_dr_link_topo) {
          if (dr_link_id_to_dis_by_dis_dict.find(topo_link) !=
              dr_link_id_to_dis_by_dis_dict.end()) {
            topo_link_to_dis_dict[topo_link] =
                dr_link_id_to_dis_by_dis_dict.at(topo_link);
          }
        }
        if (!topo_link_to_dis_dict.empty()) {
          std::vector<std::pair<uint64_t, double>> sorted_topo_link_to_dis(
              topo_link_to_dis_dict.begin(), topo_link_to_dis_dict.end());
          std::sort(
              sorted_topo_link_to_dis.begin(), sorted_topo_link_to_dis.end(),
              [](const auto& a, const auto& b) { return a.second < b.second; });
          uint64_t dr_link_id = sorted_topo_link_to_dis.front().first;
          double dr_link_id_dis = sorted_topo_link_to_dis.front().second;
          MLOG(DEBUG) << "dr_link_id = " << dr_link_id
                      << ", dr_link_id_dis = " << dr_link_id_dis;
          if (dr_link_id_dis < 10) {
            uint64_t closest_link_id =
                GetKeyByValue(sd_link_to_dr_links, dr_link_id);
            UpdateMatchedInfos(matched_links, matched_dr_links, closest_link_id,
                               dr_link_id);
            MLOG(DEBUG)
                << "set to new topo dr link, closest_dr_link_id_by_angle = "
                << closest_dr_link_id_by_angle;
          } else {
            MLOG(DEBUG) << "new topo dis is big";
            UpdateMatchedInfos(matched_links, matched_dr_links,
                               closest_link_id_by_dis,
                               closest_dr_link_id_by_angle);
          }
        } else {
          MLOG(DEBUG) << "new topo len is empty";
          UpdateMatchedInfos(matched_links, matched_dr_links,
                             closest_link_id_by_dis,
                             closest_dr_link_id_by_angle);
        }
      }
    } else {
      UpdateMatchedInfos(matched_links, matched_dr_links,
                         closest_link_id_by_dis, closest_dr_link_id_by_angle);
      MLOG(DEBUG)
          << "dr link not in topo and last dr link not in dr_link_to_dict";
    }
  }
  return true;
}

bool FindNearestLinkIdByDis(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_dis_dict,
    uint64_t& closest_id_by_dis, double& min_distance_by_dis) {
  for (const auto& pair : id_to_pts_map) {
    auto flag_iter = sd_link_to_dr_links.find(pair.first);
    if (flag_iter == sd_link_to_dr_links.end()) {
      MLOG(ERROR) << "sd_link_to_dr_links do not has ele, id = " << pair.first;
      return false;
    }
    const auto& dr_links = sd_link_to_dr_links.at(pair.first);
    for (const auto& point : pair.second) {
      double distance = CalculateDistance(pred_uv, point);
      dr_link_id_to_dis_by_dis_dict[dr_links[0]] = distance;
      if (dr_links.size() > 1) {
        dr_link_id_to_dis_by_dis_dict[dr_links[1]] = distance;
      }

      if (distance < min_distance_by_dis) {
        min_distance_by_dis = distance;
        closest_id_by_dis = pair.first;
        // auto yaw_iter = dr_id_to_yaw_map.find(pair.first);
        // Eigen::Vector2d norm(cos(yaw_iter->second), sin(yaw_iter->second));
        // angle0 =
        //     std::acos(AcosProtect(norm.dot(trajectory_norm) /
        //                           (norm.norm() * trajectory_norm.norm()))) *
        //     kDegreePerRad;
      }
    }
  }

  MLOG(DEBUG) << "closest_id_by_dis = " << closest_id_by_dis
              << ",min_distance_by_dis = " << min_distance_by_dis;
  std::string dr_link_id_to_dis_by_dis_dict_str = "";
  for (const auto& id_to_dist : dr_link_id_to_dis_by_dis_dict) {
    dr_link_id_to_dis_by_dis_dict_str += std::to_string(id_to_dist.first);
    dr_link_id_to_dis_by_dis_dict_str += ":";
    dr_link_id_to_dis_by_dis_dict_str += std::to_string(id_to_dist.second);
    dr_link_id_to_dis_by_dis_dict_str += "\n";
  }

  MLOG(DEBUG) << "\ndr_link_id_to_dis_by_dis_dict_str = \n"
              << dr_link_id_to_dis_by_dis_dict_str;

  return true;
}

bool FindNearestLinkIdByAngle(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_and_heading_cost,
    uint64_t& closest_id_by_angle, double& min_distance_by_angle,
    uint64_t& closest_dr_id_by_angle,
    uint64_t& closest_dr_id_by_dis_and_heading) {
  constexpr double kDegreePerRad = 57.29577951308232;

  double angle1 = 0.0;
  double angle2 = 0.0;

  for (const auto& pair : id_to_pts_map) {
    auto flag_iter = sd_link_to_dr_links.find(pair.first);
    if (flag_iter == sd_link_to_dr_links.end()) {
      MLOG(ERROR) << "sd_link_to_dr_links do not has ele, id = " << pair.first;
      return false;
    }
    const auto& dr_links = sd_link_to_dr_links.at(pair.first);
    uint64_t dr_link_id1 = dr_links[0];
    uint64_t dr_link_id2 = dr_link_id1;
    if (dr_links.size() > 1) {
      dr_link_id2 = dr_links[1];
    }

    const auto dr_id1_it = dr_id_to_yaw_map.find(dr_link_id1);
    const auto dr_id2_it = dr_id_to_yaw_map.find(dr_link_id2);
    if (dr_id1_it == dr_id_to_yaw_map.end() ||
        dr_id2_it == dr_id_to_yaw_map.end()) {
      MLOG(ERROR) << "dr id not find in dr_id_to_yaw_map id = " << dr_link_id1
                  << "," << dr_link_id2;
      return false;
    }
    double yaw1 = dr_id_to_yaw_map.at(dr_link_id1);
    double yaw2 = dr_id_to_yaw_map.at(dr_link_id2);

    double every_angle1 =
        ConstrainAngle(std::abs(trajectory_yaw - yaw1) * kDegreePerRad, false);

    double every_angle2 =
        ConstrainAngle(std::abs(trajectory_yaw - yaw2) * kDegreePerRad, false);

    // MLOG(INFO) << "DDMM trajectory yaw: " << trajectory_yaw
    //            << " DDMM link id = " << pair.first
    //            << ", every_angle1 = " << every_angle1
    //            << ", every_angle_2 = " << every_angle2;

    dr_link_id_to_traj_angle_dict[dr_link_id1] = every_angle1;
    dr_link_id_to_traj_angle_dict[dr_link_id2] = every_angle2;

    if (every_angle1 < 60 || every_angle2 < 60) {
      double link_min_dist = std::numeric_limits<double>::max();
      for (const auto& point : pair.second) {
        const double distance = CalculateDistance(pred_uv, point);
        dr_link_id_to_dis_by_angle_dict[dr_link_id1] = distance;
        dr_link_id_to_dis_by_angle_dict[dr_link_id2] = distance;

        if (distance < min_distance_by_angle) {
          min_distance_by_angle = distance;
          closest_id_by_angle = pair.first;
          angle1 = every_angle1;
          angle2 = every_angle2;
          if (angle1 < angle2) {
            closest_dr_id_by_angle = dr_link_id1;
          } else {
            closest_dr_id_by_angle = dr_link_id2;
          }
        }

        if (distance < link_min_dist) {
          link_min_dist = distance;
        }
      }

      const double cost1 = link_min_dist + 15 * (every_angle1 / kDegreePerRad);
      const double cost2 = link_min_dist + 15 * (every_angle2 / kDegreePerRad);
      dr_link_id_to_dis_and_heading_cost[dr_link_id1] = cost1;
      dr_link_id_to_dis_and_heading_cost[dr_link_id2] = cost2;
    }
  }

  // find the closest link by distance and heading cost
  double min_dis_and_heading_cost = std::numeric_limits<double>::max();
  for (const auto& pair : dr_link_id_to_dis_and_heading_cost) {
    if (pair.second < min_dis_and_heading_cost) {
      min_dis_and_heading_cost = pair.second;
      closest_dr_id_by_dis_and_heading = pair.first;
    }
    MLOG(INFO) << "DDMM DEBUG, dr link id: " << pair.first
               << ",  dist + heading cost: " << pair.second;
  }
  // MLOG(INFO) << "DDMM DEBUG min_dis_and_heading_cost: "
  //            << min_dis_and_heading_cost
  //            << ", link id: " << closest_dr_id_by_dis_and_heading;

  // MLOG(DEBUG) << "closest_dr_id_by_angle = " << closest_dr_id_by_angle
  //             << ",min_distance_by_angle = " << min_distance_by_angle;

  std::string dr_link_id_to_traj_angle_dict_str = "";
  for (const auto& pair : dr_link_id_to_traj_angle_dict) {
    dr_link_id_to_traj_angle_dict_str += std::to_string(pair.first);
    dr_link_id_to_traj_angle_dict_str += ":";
    dr_link_id_to_traj_angle_dict_str += std::to_string(pair.second);
    dr_link_id_to_traj_angle_dict_str += "\n";
  }

  MLOG(DEBUG) << "\ndr_link_id_to_traj_angle_dict \n"
              << dr_link_id_to_traj_angle_dict_str;

  std::string dr_link_id_to_dis_by_angle_dict_str = "";
  for (const auto& pair : dr_link_id_to_dis_by_angle_dict) {
    dr_link_id_to_dis_by_angle_dict_str += std::to_string(pair.first);
    dr_link_id_to_dis_by_angle_dict_str += ":";
    dr_link_id_to_dis_by_angle_dict_str += std::to_string(pair.second);
    dr_link_id_to_dis_by_angle_dict_str += "\n";
  }

  MLOG(DEBUG) << "\ndr_link_id_to_dis_by_angle_dict = \n"
              << dr_link_id_to_dis_by_angle_dict_str;

  return true;
}

bool FindNearestLinkIdByAngle(
    const std::unordered_map<uint64_t, std::vector<cv::Point>>& id_to_pts_map,
    const std::vector<uint64_t>& candidate_sd_link_ids,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_id_to_yaw_map,
    const double trajectory_yaw, const cv::Point& pred_uv,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_by_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict,
    std::unordered_map<uint64_t, double>& dr_link_id_to_dis_and_heading_cost,
    uint64_t& closest_id_by_angle, double& min_distance_by_angle,
    uint64_t& closest_dr_id_by_angle,
    uint64_t& closest_dr_id_by_dis_and_angle) {
  double angle1 = 0.0;
  double angle2 = 0.0;

  // double min_distance_by_dist_and_angle = std::numeric_limits<double>::max();

  for (const auto& candidate_link_id : candidate_sd_link_ids) {
    const auto flag_iter = sd_link_to_dr_links.find(candidate_link_id);
    if (flag_iter == sd_link_to_dr_links.end()) {
      MLOG(ERROR) << "sd_link_to_dr_links do not has ele, id = "
                  << candidate_link_id;
      return false;
    }

    const auto candidate_link_pts = id_to_pts_map.at(candidate_link_id);
    const auto& dr_links = sd_link_to_dr_links.at(candidate_link_id);
    const uint64_t dr_link_id1 = dr_links[0];
    uint64_t dr_link_id2 = dr_link_id1;
    if (dr_links.size() > 1) {
      dr_link_id2 = dr_links[1];
    }

    const auto dr_id1_it = dr_id_to_yaw_map.find(dr_link_id1);
    const auto dr_id2_it = dr_id_to_yaw_map.find(dr_link_id2);
    if (dr_id1_it == dr_id_to_yaw_map.end() ||
        dr_id2_it == dr_id_to_yaw_map.end()) {
      MLOG(INFO) << "dr id not find in dr_id_to_yaw_map id = " << dr_link_id1
                 << "," << dr_link_id2;
      return false;
    }
    const double yaw1 = dr_id_to_yaw_map.at(dr_link_id1);
    const double yaw2 = dr_id_to_yaw_map.at(dr_link_id2);

    Eigen::Vector2d norm1(cos(yaw1), sin(yaw1));
    Eigen::Vector2d norm2(cos(yaw2), sin(yaw2));

    // const double trajectory_yaw =
    //     std::atan2(trajectory_norm(1), trajectory_norm(0));

    const double every_angle1 =
        ConstrainAngle(std::abs(trajectory_yaw - yaw1) * kDegreePerRad, false);
    const double every_angle2 =
        ConstrainAngle(std::abs(trajectory_yaw - yaw2) * kDegreePerRad, false);

    // MLOG(INFO) << "DDMM trajectory heading: " << trajectory_yaw
    //            << ", link id: " << candidate_link_id
    //            << ", every_angle1 = " << every_angle1
    //            << ", every_angle_2 = " << every_angle2
    //            << ", every_angle1 = " << every_angle1;

    dr_link_id_to_traj_angle_dict[dr_link_id1] = every_angle1;
    dr_link_id_to_traj_angle_dict[dr_link_id2] = every_angle2;

    if (every_angle1 < 60 || every_angle2 < 60) {
      double link_min_dist = std::numeric_limits<double>::max();
      for (const auto& point : candidate_link_pts) {
        double distance = CalculateDistance(pred_uv, point);
        dr_link_id_to_dis_by_angle_dict[dr_link_id1] = distance;
        dr_link_id_to_dis_by_angle_dict[dr_link_id2] = distance;

        if (distance < min_distance_by_angle) {
          min_distance_by_angle = distance;
          closest_id_by_angle = candidate_link_id;
          angle1 = every_angle1;
          angle2 = every_angle2;
          if (angle1 < angle2) {
            closest_dr_id_by_angle = dr_link_id1;
          } else {
            closest_dr_id_by_angle = dr_link_id2;
          }
        }

        if (distance < link_min_dist) {
          link_min_dist = distance;
        }
      }

      const double cost1 = link_min_dist + 15 * (every_angle1 / kDegreePerRad);
      const double cost2 = link_min_dist + 15 * (every_angle2 / kDegreePerRad);
      dr_link_id_to_dis_and_heading_cost[dr_link_id1] = cost1;
      dr_link_id_to_dis_and_heading_cost[dr_link_id2] = cost2;

      // MLOG(INFO) << "DDMM closest_dr_id_by_angle = " <<
      // closest_dr_id_by_angle
      //            << ",min_distance_by_angle = " << min_distance_by_angle;
    }
  }

  double min_dis_and_heading_cost = std::numeric_limits<double>::max();
  for (const auto& pair : dr_link_id_to_dis_and_heading_cost) {
    if (pair.second < min_dis_and_heading_cost) {
      min_dis_and_heading_cost = pair.second;
      closest_dr_id_by_dis_and_angle = pair.first;
    }
    // MLOG(INFO) << "DDMM DEBUG, dr link id: " << pair.first
    //            << ",  dist + heading cost: " << pair.second;
  }
  // MLOG(INFO) << "DDMM DEBUG min_dis_and_heading_cost: "
  //            << min_dis_and_heading_cost
  //            << ", link id: " << closest_dr_id_by_dis_and_angle;

  std::string dr_link_id_to_traj_angle_dict_str = "";
  for (const auto& pair : dr_link_id_to_traj_angle_dict) {
    dr_link_id_to_traj_angle_dict_str += std::to_string(pair.first);
    dr_link_id_to_traj_angle_dict_str += ":";
    dr_link_id_to_traj_angle_dict_str += std::to_string(pair.second);
    dr_link_id_to_traj_angle_dict_str += "\n";
  }

  // MLOG(DEBUG) << "\n DDMM dr_link_id_to_traj_angle_dict \n"
  //             << dr_link_id_to_traj_angle_dict_str;

  std::string dr_link_id_to_dis_by_angle_dict_str = "";
  for (const auto& pair : dr_link_id_to_dis_by_angle_dict) {
    dr_link_id_to_dis_by_angle_dict_str += std::to_string(pair.first);
    dr_link_id_to_dis_by_angle_dict_str += ":";
    dr_link_id_to_dis_by_angle_dict_str += std::to_string(pair.second);
    dr_link_id_to_dis_by_angle_dict_str += "\n";
  }

  // MLOG(DEBUG) << "\ndr_link_id_to_dis_by_angle_dict = \n"
  //             << dr_link_id_to_dis_by_angle_dict_str;

  return true;
}
/*
 *      <-----Link A-------
 *      |
 *    Link B
 *      |
 *      |
 *    Link C
 *      |
 *      |
 *      ------Link D----->
 *
 *      for LINK B: Link A is inner_pre_link, Link C is inner_after_link
 */
void UpdateEveryTopo(
    const uint64_t& inner_pre_link, const uint64_t& inner_after_link,
    std::unordered_map<uint64_t,
                       std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo) {
  if (dr_links_topo.find(inner_after_link) != dr_links_topo.end()) {
    if (std::find(dr_links_topo[inner_after_link]["in"].begin(),
                  dr_links_topo[inner_after_link]["in"].end(),
                  inner_pre_link) ==
        dr_links_topo[inner_after_link]["in"].end()) {
      dr_links_topo[inner_after_link]["in"].push_back(inner_pre_link);
    }
  } else {
    std::vector<uint64_t> in = {inner_pre_link};
    std::vector<uint64_t> out;
    dr_links_topo[inner_after_link] = {{"in", in}, {"out", out}};
  }

  if (dr_links_topo.find(inner_pre_link) != dr_links_topo.end()) {
    if (std::find(dr_links_topo[inner_pre_link]["out"].begin(),
                  dr_links_topo[inner_pre_link]["out"].end(),
                  inner_after_link) ==
        dr_links_topo[inner_pre_link]["out"].end()) {
      dr_links_topo[inner_pre_link]["out"].push_back(inner_after_link);
    }
  } else {
    std::vector<uint64_t> in;
    std::vector<uint64_t> out = {inner_after_link};
    dr_links_topo[inner_pre_link] = {{"in", in}, {"out", out}};
  }
}

bool GenerateTopo(
    const deeproute::sd_map::SdLinks& local_sd_map,
    std::unordered_map<uint64_t, std::vector<uint64_t>>& sd_link_to_dr_links,
    std::unordered_map<uint64_t,
                       std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo) {
  const auto& links = local_sd_map.links();

  for (const auto& link : links) {
    uint64_t link_id = link.navinfo_id();
    uint64_t dr_link_id = link.dr_link_id();

    if (sd_link_to_dr_links.find(link_id) != sd_link_to_dr_links.end()) {
      sd_link_to_dr_links[link_id].push_back(dr_link_id);
    } else {
      sd_link_to_dr_links[link_id] = {dr_link_id};
    }

    if (link.in_links_data_size() > 0) {
      for (const auto& in_link : link.in_links_data()) {
        if (in_link.pass_directed_dr_link_ids_size() > 0) {
          uint64_t inner_pre_link = in_link.directed_dr_link_id();
          uint64_t inner_after_link = in_link.pass_directed_dr_link_ids(0);
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);

          if (in_link.pass_directed_dr_link_ids_size() > 1) {
            for (int i = 1; i < in_link.pass_directed_dr_link_ids_size(); ++i) {
              inner_pre_link = in_link.pass_directed_dr_link_ids(i - 1);
              inner_after_link = in_link.pass_directed_dr_link_ids(i);
              UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
            }
          }

          inner_pre_link = in_link.pass_directed_dr_link_ids(
              in_link.pass_directed_dr_link_ids_size() - 1);
          inner_after_link = dr_link_id;
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
        } else {
          uint64_t inner_pre_link = in_link.directed_dr_link_id();
          uint64_t inner_after_link = dr_link_id;
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
        }
      }
    }

    if (link.out_links_data_size() > 0) {
      for (const auto& out_link : link.out_links_data()) {
        if (out_link.pass_directed_dr_link_ids_size() > 0) {
          uint64_t inner_pre_link = dr_link_id;
          uint64_t inner_after_link = out_link.pass_directed_dr_link_ids(0);
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);

          if (out_link.pass_directed_dr_link_ids_size() > 1) {
            for (int i = 1; i < out_link.pass_directed_dr_link_ids_size();
                 ++i) {
              inner_pre_link = out_link.pass_directed_dr_link_ids(
                  out_link.pass_directed_dr_link_ids_size() - 1 - i);
              inner_after_link = out_link.pass_directed_dr_link_ids(
                  out_link.pass_directed_dr_link_ids_size() - i);
              UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
            }
          }

          inner_pre_link = out_link.pass_directed_dr_link_ids(
              out_link.pass_directed_dr_link_ids_size() - 1);
          inner_after_link = out_link.directed_dr_link_id();
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
        } else {
          uint64_t inner_pre_link = dr_link_id;
          uint64_t inner_after_link = out_link.directed_dr_link_id();
          UpdateEveryTopo(inner_pre_link, inner_after_link, dr_links_topo);
        }
      }
    }
  }
  std::string topo_str = "";
  for (const auto& dr_link_topo : dr_links_topo) {
    topo_str += std::to_string(dr_link_topo.first);
    topo_str += ":";
    for (const auto& link_topo : dr_link_topo.second) {
      topo_str += link_topo.first;
      topo_str += ":";
      for (const auto& topo_content : link_topo.second) {
        topo_str += std::to_string(topo_content);
        topo_str += ",";
      }
      topo_str += ";";
    }
    topo_str += "\n";
  }
  MLOG(DEBUG) << "\ntopo_str = \n" << topo_str;

  return true;
}

bool GetLastDrLinkTopo(
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const uint64_t& last_dr_link_id, std::vector<uint64_t>& last_dr_link_topo) {
  MLOG(DEBUG) << "last_dr_link_id = " << last_dr_link_id;
  std::vector<uint64_t> second_out_links;
  std::unordered_map<std::string, std::vector<uint64_t>> last_dr_links_topo;
  std::vector<uint64_t> in_links;
  std::vector<uint64_t> out_links;
  if (dr_links_topo.find(last_dr_link_id) != dr_links_topo.end()) {
    last_dr_links_topo = dr_links_topo.at(last_dr_link_id);
  } else {
    MLOG(INFO) << "last_dr_link_id not in topo,id = " << last_dr_link_id;
    return false;
  }

  if (last_dr_links_topo.find("in") != last_dr_links_topo.end()) {
    in_links = last_dr_links_topo.at("in");
  } else {
    MLOG(INFO) << "in not in last_dr_links_topo";
    return false;
  }

  if (last_dr_links_topo.find("out") != last_dr_links_topo.end()) {
    out_links = last_dr_links_topo.at("out");
  } else {
    MLOG(INFO) << "out not in last_dr_links_topo";
    return false;
  }

  last_dr_link_topo.push_back(last_dr_link_id);
  last_dr_link_topo.insert(last_dr_link_topo.end(), in_links.begin(),
                           in_links.end());
  last_dr_link_topo.insert(last_dr_link_topo.end(), out_links.begin(),
                           out_links.end());
  for (const auto& id : last_dr_link_topo) {
    MLOG(DEBUG) << "topo id = " << id;
  }
  for (const auto& in_link : in_links) {
    std::unordered_map<std::string, std::vector<uint64_t>> in_link_topo;
    if (dr_links_topo.find(in_link) != dr_links_topo.end()) {
      in_link_topo = dr_links_topo.at(in_link);
    } else {
      MLOG(INFO) << "link id not in dr_links_topo, id = " << in_link;
      continue;
    }
    std::vector<uint64_t> in_link_out_link;

    if (in_link_topo.find("out") != in_link_topo.end()) {
      in_link_out_link = in_link_topo.at("out");
    } else {
      MLOG(INFO) << "out not in in_link_topo, id = " << in_link;
      return false;
    }
    second_out_links.insert(second_out_links.end(), in_link_out_link.begin(),
                            in_link_out_link.end());
  }
  last_dr_link_topo.insert(last_dr_link_topo.end(), second_out_links.begin(),
                           second_out_links.end());

  for (const auto& id : last_dr_link_topo) {
    MLOG(DEBUG) << "topo id = " << id;
  }

  std::set<int> s(last_dr_link_topo.begin(), last_dr_link_topo.end());
  last_dr_link_topo.clear();
  last_dr_link_topo.assign(s.begin(), s.end());

  for (const auto& id : last_dr_link_topo) {
    MLOG(DEBUG) << "topo id = " << id;
  }

  if (last_dr_link_topo.empty()) {
    MLOG(ERROR) << "should not happen";
    return false;
  }
  return true;
}

bool GetLastDrLinkSimpleTopo(
    const std::unordered_map<
        uint64_t, std::unordered_map<std::string, std::vector<uint64_t>>>&
        dr_links_topo,
    const uint64_t& last_dr_link_id, std::vector<uint64_t>& last_dr_link_topo) {
  std::unordered_map<std::string, std::vector<uint64_t>> last_dr_links_topo;
  std::vector<uint64_t> in_links;
  std::vector<uint64_t> out_links;
  if (dr_links_topo.find(last_dr_link_id) != dr_links_topo.end()) {
    last_dr_links_topo = dr_links_topo.at(last_dr_link_id);
  } else {
    MLOG(INFO) << "last_dr_link_id not in topo,id = " << last_dr_link_id;
    return false;
  }

  if (last_dr_links_topo.find("in") != last_dr_links_topo.end()) {
    in_links = last_dr_links_topo.at("in");
  } else {
    MLOG(INFO) << "in not in last_dr_links_topo";
    return false;
  }

  if (last_dr_links_topo.find("out") != last_dr_links_topo.end()) {
    out_links = last_dr_links_topo.at("out");
  } else {
    MLOG(INFO) << "out not in last_dr_links_topo";
    return false;
  }
  last_dr_link_topo.push_back(last_dr_link_id);
  last_dr_link_topo.insert(last_dr_link_topo.end(), in_links.begin(),
                           in_links.end());
  last_dr_link_topo.insert(last_dr_link_topo.end(), out_links.begin(),
                           out_links.end());

  for (const auto& id : last_dr_link_topo) {
    MLOG(DEBUG) << "topo id = " << id;
  }

  std::set<int> s(last_dr_link_topo.begin(), last_dr_link_topo.end());
  last_dr_link_topo.clear();
  last_dr_link_topo.assign(s.begin(), s.end());

  if (last_dr_link_topo.empty()) {
    MLOG(ERROR) << "should not happen";
    return false;
  }
  return true;
}

uint64_t GetClosestDrLinkId(
    const uint64_t& closest_link_id,
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    const std::unordered_map<uint64_t, double>& dr_link_id_to_traj_angle_dict) {
  uint64_t closest_dr_link_id = 0;
  std::vector<uint64_t> dr_link_ids = sd_link_to_dr_links.at(closest_link_id);
  if (dr_link_ids.size() == 1) {
    closest_dr_link_id = dr_link_ids[0];
  } else if (dr_link_ids.size() > 1) {
    uint64_t dr_link_id1 = dr_link_ids[0];
    uint64_t dr_link_id2 = dr_link_ids[1];
    double angle1 = dr_link_id_to_traj_angle_dict.at(dr_link_id1);
    double angle2 = dr_link_id_to_traj_angle_dict.at(dr_link_id2);
    if (angle1 < angle2) {
      closest_dr_link_id = dr_link_id1;
    } else {
      closest_dr_link_id = dr_link_id2;
    }
  }
  return closest_dr_link_id;
}

void ClearMatchedInfos(std::vector<uint64_t>& matched_links,
                       std::vector<uint64_t>& matched_dr_links) {
  matched_links.clear();
  matched_dr_links.clear();
}

void UpdateMatchedInfos(std::vector<uint64_t>& matched_links,
                        std::vector<uint64_t>& matched_dr_links,
                        uint64_t closest_link_id, uint64_t closest_dr_link_id) {
  matched_links.push_back(closest_link_id);
  matched_dr_links.push_back(closest_dr_link_id);
}

uint64_t GetKeyByValue(
    const std::unordered_map<uint64_t, std::vector<uint64_t>>&
        sd_link_to_dr_links,
    uint64_t value) {
  uint64_t key = 0;
  for (const auto& pair : sd_link_to_dr_links) {
    const std::vector<uint64_t>& dr_link_ids = pair.second;
    if (std::find(dr_link_ids.begin(), dr_link_ids.end(), value) !=
        dr_link_ids.end()) {
      key = pair.first;
      break;
    }
  }
  return key;
}

}  // namespace localization
}  // namespace deeproute
