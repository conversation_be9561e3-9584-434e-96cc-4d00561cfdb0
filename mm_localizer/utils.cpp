#include "mm_localizer/utils.h"

#include <cstdint>
#include <fstream>
#include <string>
#include <vector>

#include <absl/status/status.h>
#include <absl/status/statusor.h>
#include <network/type.hpp>

#include "drivers/gnss/ins.pb.h"
#include "lam_common/projection.pb.h"
#include "lam_common/sd_map/sd_map_service.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "map/sd_horizon_provider.pb.h"
#include "map/sd_map.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "common/event_log_handle.h"
#include "common/json.hpp"
#include "common/log.h"
#include "common/time.h"
#include "common/types.h"
#include "geometry/geometry.h"
#include "joint/ll_utils.h"
#include "lam_common/proto_utils.h"
#include "lam_common/utm_projection_convert.h"

namespace deeproute {
namespace localization {
namespace {
uint64_t HashPoint(const double v_x, const double v_y) {
  const uint64_t x = static_cast<int16_t>(std::floor(v_x / 0.5));
  const uint64_t y = static_cast<int16_t>(std::floor(v_y / 0.5));
  return (x << 48) + (y << 32);
}

int GetLeftLaneNum(  // NOLINT(misc-no-recursion)
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const deeproute::hdmap::Lane& lane, std::vector<int32_t>* left_lane_ids) {
  if (lane.has_left_neighbor_forward_lane_id() &&
      lane.left_neighbor_forward_lane_id() != 0) {
    left_lane_ids->push_back(lane.left_neighbor_forward_lane_id());
    const deeproute::hdmap::Lane* left_lane =
        map_server->GetLaneById(lane.left_neighbor_forward_lane_id());
    CHECK(left_lane != nullptr) << "lane.DebugString(): " << lane.DebugString();
    return 1 + GetLeftLaneNum(map_server, *left_lane, left_lane_ids);
  }

  return 0;
}

int GetRightLaneNum(
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const deeproute::hdmap::Lane lane, std::vector<int32_t>* right_lane_ids) {
  if (lane.has_right_neighbor_forward_lane_id() &&
      lane.right_neighbor_forward_lane_id() != 0) {
    right_lane_ids->push_back(lane.right_neighbor_forward_lane_id());
    const deeproute::hdmap::Lane* right_lane =
        map_server->GetLaneById(lane.right_neighbor_forward_lane_id());
    CHECK(right_lane != nullptr)
        << "lane id: " << lane.right_neighbor_forward_lane_id();
    return 1 + GetRightLaneNum(map_server, *right_lane, right_lane_ids);
  }

  return 0;
}
}  // namespace

std::string RoutingSceneTypeToString(RoutingSceneType type) {
  switch (type) {
    case UNKNOWN:
      return "UNKNOWN";
    case MAIN_ROAD:
      return "MAIN_ROAD";
    case SECONDARY_ROAD:
      return "SECONDARY_ROAD";
    case MAIN_SECONDARY_ENTRANCE:
      return "MAIN_SECONDARY_ENTRANCE";
    case JUNCTION:
      return "JUNCTION";
    default:
      return "INVALID";
  }
}

bool GetSameDirectionLanes(
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const Vector2d& xy, const double heading,
    std::vector<deeproute::hdmap::Lane>* left_lanes,
    std::vector<deeproute::hdmap::Lane>* right_lanes) {
  deeproute::common::Id lane_id;
  ::common::Point2d p(xy[0], xy[1]);
  double s, l;
  map_server->GetNearestLaneWithAmbiguousHeading(p, heading, &lane_id, &s, &l);
  const auto lane = map_server->GetLaneById(lane_id);
  if (lane == nullptr) {
    MLOG(WARN) << "Lane ptr null!";
    return false;
  }
  const double dist_to_car = Distance(xy, lane->centerline());
  constexpr double kDisThres = 5;
  if (dist_to_car > kDisThres) {
    MLOG(WARN) << "Vehicle to HD map closest lane too much: " << dist_to_car
               << " > " << kDisThres;
    return false;
  }

  std::vector<int32_t> left_lane_ids;
  std::vector<int32_t> right_lane_ids;
  GetLeftLaneNum(map_server, *lane, &left_lane_ids);
  GetRightLaneNum(map_server, *lane, &right_lane_ids);

  for (const auto left_lane_id : left_lane_ids) {
    left_lanes->push_back(*map_server->GetLaneById(left_lane_id));
  }
  // left_lanes->push_back(*lane);

  for (const auto right_lane_id : right_lane_ids) {
    right_lanes->push_back(*map_server->GetLaneById(right_lane_id));
  }

  return true;
}

bool GetSameDirectionLanes(
    const std::shared_ptr<deeproute::common::HDMapLaneInfoServer>& map_server,
    const Vector2d& xy, const double heading, int* left_lane_num,
    int* right_lane_num) {
  deeproute::common::Id lane_id;
  ::common::Point2d p(xy[0], xy[1]);
  double s, l;
  map_server->GetNearestLaneWithAmbiguousHeading(p, heading, &lane_id, &s, &l);
  const auto lane = map_server->GetLaneById(lane_id);
  const double dist_to_car = Distance(xy, lane->centerline());
  constexpr double kDisThres = 5;
  if (dist_to_car > kDisThres) {
    MLOG(WARN) << "Vehicle to HD map closest lane too much: " << dist_to_car
               << " > " << kDisThres;
    return false;
  }

  *left_lane_num = 0;
  *right_lane_num = 0;

  deeproute::hdmap::Lane left_temp_lane = *lane;
  while (left_temp_lane.has_left_neighbor_forward_lane_id()) {
    left_temp_lane = *map_server->GetLaneById(
        left_temp_lane.left_neighbor_forward_lane_id());
    (*left_lane_num) += 1;
  }

  deeproute::hdmap::Lane right_temp_lane = *lane;
  while (right_temp_lane.has_right_neighbor_forward_lane_id()) {
    right_temp_lane = *map_server->GetLaneById(
        right_temp_lane.right_neighbor_forward_lane_id());
    (*right_lane_num) += 1;
  }

  return true;
}

std::string ParseTrajectoryToWkt(const Vector2dVector& traj) {
  std::string wkt_string = " LINESTRING (";
  for (const auto& wp : traj) {
    wkt_string += std::to_string(wp[0]) + " " + std::to_string(wp[1]) + ", ";
  }
  wkt_string.pop_back();
  wkt_string.pop_back();
  wkt_string += ")";

  return wkt_string;
}

FMM::CORE::LineString ParseTrajetoryToLineString(const Vector2dVector& traj) {
  return FMM::CORE::wkt2linestring(ParseTrajectoryToWkt(traj));
}

absl::StatusOr<FMM::NETWORK::CustomGraph> LoadOfflineRoutingResponse(
    const std::string& routing_response_txt_file,
    const proto::Projection& projection) {
  std::ifstream file(routing_response_txt_file);

  if (!file.is_open()) {
    return absl::InternalError(
        absl::StrCat("open file failed: [", routing_response_txt_file, "]"));
  }
  deeproute::navinfo::SDRoutingResponse routing_response;
  auto route = routing_response.add_result()->mutable_route();
  std::string line;

  while (std::getline(file, line)) {
    std::vector<std::string> strs;
    boost::split(strs, line, boost::is_any_of(" "));
    auto segment = route->add_segm();
    segment->set_ni_id(strs[0]);

    const int wp_size = (static_cast<int32_t>(strs.size()) - 1) / 2;

    Vector2dVector wps;
    for (int i = 1; i <= wp_size; i++) {
      double lat = std::stod(strs[2 * i - 1]);
      double lon = std::stod(strs[2 * i]);
      double gcj_lon;
      double gcj_lat;
      Wgs84ToGcj02(lon, lat, &gcj_lon, &gcj_lat);
      auto pt = segment->add_shape_points();
      pt->set_lat(gcj_lat);
      pt->set_lon(gcj_lon);
    }
  }
  file.close();

  return ConvertRoutingResponseToCustomGraph(routing_response, projection);
}

absl::StatusOr<FMM::NETWORK::CustomGraph> LoadRoutingResponseProto(
    const std::string& routing_response_file,
    const proto::Projection& projection) {
  deeproute::navinfo::SDRoutingResponse routing_response;
  if (!LoadProtoFile(routing_response_file, &routing_response)) {
    return absl::InternalError(
        absl::StrCat("load proto file failed:", routing_response_file));
  }
  return ConvertRoutingResponseToCustomGraph(routing_response, projection);
}

absl::StatusOr<FMM::NETWORK::CustomGraph> ConvertRoutingResponseToCustomGraph(
    const deeproute::navinfo::SDRoutingResponse& routing_response,
    const proto::Projection& projection) {
  if (routing_response.result_size() == 0) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_ROUTING_INVALID);

    return absl::InvalidArgumentError(
        absl::StrCat("routing_response.result_size() == 0 [",
                     routing_response.ShortDebugString(), "]"));
  }
  if (routing_response.result_size() != 1) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_ROUTING_INVALID);
    MLOG(WARN) << absl::StrCat(
        "None or more than 1 routing response get, we only take first routing "
        "response "
        "[",
        routing_response.ShortDebugString(), "]");
  }
  FMM::NETWORK::CustomGraph custom_graph;
  const deeproute::navinfo::Result& result = routing_response.result(0);
  const deeproute::navinfo::Route& route = result.route();

  UtmProjectionConvert utm_projector(projection);
  std::unordered_map<uint64_t, int> hash_map;
  int node_index = 0;
  int32_t index = 0;
  for (const auto& segment : route.segm()) {
    if (segment.shape_points_size() < 2) {
      common::ReportEvent(dr::common::LOCK_ON_ROAD,
                          dr::common::LOCK_ON_ROAD_INPUT_ROUTING_INVALID);
      return absl::InvalidArgumentError(absl::StrCat(
          "Segment with content ", segment.ShortDebugString(),
          " has less than two wayponts, neglect current routing response!"));
    }

    FMM::NETWORK::Edge edge;
    if (segment.has_dr_link_id()) {
      edge.id = static_cast<int64_t>(segment.dr_link_id());
    } else if (segment.has_ni_id()) {
      edge.id = static_cast<int64_t>(std::stoull(segment.ni_id()));
    } else {
      common::ReportEvent(dr::common::LOCK_ON_ROAD,
                          dr::common::LOCK_ON_ROAD_INPUT_ROUTING_INVALID);
      return absl::InvalidArgumentError(absl::StrCat(
          "Segment with content ", segment.ShortDebugString(),
          " contains invalid link id type, neglect current routing response!"));
    }

    Vector2dVector way_points;
    for (const auto& ll : segment.shape_points()) {
      double wgs_lon;
      double wgs_lat;
      Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
      double x;
      double y;
      utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);
      way_points.emplace_back(x, y);
    }
    edge.geom = ParseTrajetoryToLineString(way_points);
    edge.length = edge.geom.get_length();
    const auto skey = HashPoint(edge.geom.get_x(0), edge.geom.get_y(0));
    const auto ekey = HashPoint(edge.geom.get_last_x(), edge.geom.get_last_y());

    // Edge id and start end hashes: 1443720 skey:  6172453709042155520 ekey:
    // 6104900285862248448 Edge id and start end hashes: ******** skey:
    // 6172453709042155520 ekey: 6109967037306503168
    if (hash_map.count(skey) == 0) {
      node_index += 1;
      hash_map.insert(std::make_pair(skey, node_index));
      edge.source = hash_map[skey];
    } else {
      edge.source = hash_map[skey];
    }

    if (hash_map.count(ekey) == 0) {
      node_index += 1;
      hash_map.insert(std::make_pair(ekey, node_index));
      edge.target = hash_map[ekey];
    } else {
      edge.target = hash_map[ekey];
    }

    // MLOG(INFO) << "Edge id and start end hashes: " << edge.id
    //            << " skey: " << skey << " ekey: " << ekey;
    edge.index = index;
    index++;
    custom_graph.push_back(edge);
  }
  return custom_graph;
}

deeproute::sd_map::SdLinks ExtractLocalSdMapFromRoutingResponse(
    const deeproute::navinfo::Route& route,
    const proto::Projection& projection) {
  UtmProjectionConvert utm_projector(projection);

  deeproute::sd_map::SdLinks sdlinks =
      route.sd_links();  // 获取 SdLinks 对象的副本

  for (deeproute::sd_map::LinkData& link_data : *sdlinks.mutable_links()) {
    for (auto& points_gcj02 : *link_data.mutable_points()) {
      double wgs_lon;
      double wgs_lat;
      Gcj02ToWgs84(points_gcj02.lon(), points_gcj02.lat(), &wgs_lon, &wgs_lat);
      double x;
      double y;
      utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);
      points_gcj02.set_lon(x);
      points_gcj02.set_lat(y);
    }
  }

  sdlinks.set_link_frame(deeproute::sd_map::SdLinks::UTM);
  MLOG(INFO) << "sdlinks:" << sdlinks.DebugString();
  return sdlinks;
}

bool ExtractLocalSdMapFromQueriedResult(
    const sd_map::SDMap& resp, const proto::Projection& projection,
    deeproute::sd_map::SdLinks* sd_links,
    std::unordered_map<uint64_t, LockOnRoadResult::LinkProperties>*
        ni_id_to_link_properties) {
  ::common::Timer timer;
  sd_links->Clear();
  ni_id_to_link_properties->clear();

  UtmProjectionConvert utm_projector(projection);

  // deeproute::sd_map::SdLinks sd_links;
  *sd_links->mutable_links() = resp.links();

  // PRIOIRTY
  // 0	高速公路
  // 1	城市快速路
  // 2	国道
  // 3	省道、城市主干道
  // 4	县道
  // 5	路况较好的乡镇道路
  // 6	乡镇道路
  // 7	POI连接线，类似于小区路，但可以穿行
  // 8	小区路，不能穿行
  // 9	九级辅路：非机动车辆通行的辅路我们定义为九级辅路
  // 10	轮渡
  // 11	步行路
  for (auto& link_data : *sd_links->mutable_links()) {
    if (link_data.priority() >= 9) {
      continue;
    }

    LockOnRoadResult::LinkProperties link_properties;
    link_properties.set_priority(link_data.priority());
    link_properties.set_tunnel(link_data.tunnel());
    link_properties.set_elevated(link_data.elevated());
    (*ni_id_to_link_properties)[link_data.navinfo_id()] = link_properties;

    for (auto& points_gcj02 : *link_data.mutable_points()) {
      double wgs_lon;
      double wgs_lat;
      Gcj02ToWgs84(points_gcj02.lon(), points_gcj02.lat(), &wgs_lon, &wgs_lat);
      double x;
      double y;
      utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);

      points_gcj02.set_lon(x);
      points_gcj02.set_lat(y);
    }
  }

  sd_links->set_link_frame(deeproute::sd_map::SdLinks::UTM);
  MLOG(INFO) << "sdlinks size: " << sd_links->links_size();

  MLOG_TRACE("ExtractLocalSdMapFromQueriedResult", timer.EndMilli(true));
  return true;
}

double HaversineDistance(const Vector2d& lonlat1, const Vector2d& lonlat2) {
  double lon1 = lonlat1[0];
  double lat1 = lonlat1[1];
  double lon2 = lonlat2[0];
  double lat2 = lonlat2[1];

  // Convert latitude and longitude from degrees to radians
  lat1 = lat1 * M_PI / 180.0;
  lon1 = lon1 * M_PI / 180.0;
  lat2 = lat2 * M_PI / 180.0;
  lon2 = lon2 * M_PI / 180.0;

  // Haversine formula
  double dlat = lat2 - lat1;
  double dlon = lon2 - lon1;
  double a =
      std::sin(dlat / 2) * std::sin(dlat / 2) +
      std::cos(lat1) * std::cos(lat2) * std::sin(dlon / 2) * std::sin(dlon / 2);
  double c = 2 * std::atan2(std::sqrt(a), std::sqrt(1 - a));

  constexpr double kEarthRadiusMeters = 6371000.0;
  double distance = kEarthRadiusMeters * c;

  return distance;
}

void WriteSDMapLinksToGeoJSONFile(const sd_map::SDMap& sd_map_data,
                                  const std::string& filepath) {
  nlohmann::json geojson;
  geojson["type"] = "FeatureCollection";
  nlohmann::json features = nlohmann::json::array();

  for (const auto& link_data : sd_map_data.links()) {
    nlohmann::json feature;
    feature["type"] = "Feature";
    nlohmann::json geometry;
    geometry["type"] = "LineString";

    nlohmann::json coordinates;
    for (const auto& point : link_data.points()) {
      coordinates.push_back({point.lon(), point.lat()});
    }
    geometry["coordinates"] = coordinates;

    nlohmann::json properties;
    properties["navinfo_id"] = link_data.navinfo_id();
    feature["geometry"] = geometry;
    feature["properties"] = properties;

    features.push_back(feature);
  }

  // 将 features 添加到主对象中
  geojson["features"] = features;

  // 写入到文件
  std::ofstream file(filepath);
  file << geojson.dump(4);  // 使用4个空格缩进
}

}  // namespace localization
}  // namespace deeproute
