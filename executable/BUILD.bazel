package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")


cc_binary(
  name = "run_lor_on_bags",
  srcs = ["run_lor_on_bags.cpp"],
  local_defines = [ 'BUILD_WITH_ROS' ],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/map:sd_map_proto_cc",
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//proto/common/configs:vehicle_config_proto_cc",
        "//data_adapter:ins_adapter",
        "//localizer:localizer",
        "//mm_localizer:utils",
        "//proto:lock_on_road_config_cc_proto",
        "//joint/proto:config_region_cc_proto",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@lam_common//lam_common:proto_utils",
        "@lam_common//lam_common:utm_projection_convert",
        "@os_interface//:os_interface_no_ros",
        "@local_ros//:ros_bag",
        "@local_ros//:ros_limited",
        "@common//common/conversions:cvmat_conversions",
  ],
)

cc_binary(
  name = "ddmm_debug_performance_on_bag",
  srcs = ["ddmm_debug_performance_on_bag.cpp"],
  local_defines = [ 'BUILD_WITH_ROS' ],
  deps = [
        "@common//proto/lock_on_road:lock_on_road_debug_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:proto_utils",
        "//mm_localizer:ddmm_model",
        "@os_interface//:os_interface_no_ros",
        "@local_ros//:ros_bag",
        "@local_ros//:ros_limited",
        "//proto:lock_on_road_config_cc_proto",
  ],
)

cc_binary(
  name = "run_lor_on_case",
  srcs = ["run_lor_on_case.cpp"],
  local_defines = [ 'BUILD_WITH_ROS' ],
  deps = [
        "@lam_common//lam_common/proto_adapter:blc_adapter",
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/map:sd_map_proto_cc",
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//proto/common/configs:vehicle_config_proto_cc",
        "@common//common/configs:vehicle_config",
        "@common//common/conversions:cvmat_conversions",
        "@common//common:types",
        "//data_adapter:ins_adapter",
        "//joint/proto:config_region_cc_proto",
        "//joint:ll_utils",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@lam_common//lam_common:proto_utils",
        "@lam_common//lam_common:utm_projection_convert",
        "//localizer:localizer",
        "//mm_localizer:utils",
        "@os_interface//:os_interface_no_ros",
        "@local_ros//:ros_bag",
        "@local_ros//:ros_limited",
        "//proto:lock_on_road_config_cc_proto"
  ],
)

cc_binary(
  name = "generate_autotune_file",
  srcs = ["generate_autotune_file.cpp"],
  deps = [
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:proto_utils",
        "//mm_localizer:ddmm_model",
        "//proto:lock_on_road_config_cc_proto",
  ],
)

cc_binary(
  name = "ddmm_debug_on_bag",
  srcs = ["ddmm_debug_on_bag.cpp"],
  local_defines = [ 'BUILD_WITH_ROS' ],
  deps = [
        "@common//proto/drivers:sensor_image_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//dpbag:dpbag_interface",
        "@lam_common//lam_common:logging",
        "@lam_common//lam_common:proto_utils",
        "//mm_localizer:ddmm_model",
        "@os_interface//:os_interface_no_ros",
        "@local_ros//:ros_limited",
        "//proto:lock_on_road_config_cc_proto",
  ],
  target_compatible_with = [
        "@platforms//os:linux",
        "@platforms//cpu:x86_64",
  ],
)

