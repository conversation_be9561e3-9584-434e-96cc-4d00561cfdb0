#include <fstream>
#include <iostream>
#include <queue>

#include <absl/strings/substitute.h>
#include <boost/filesystem.hpp>
#include <boost/functional/hash.hpp>
#include <boost/process.hpp>
#include <gdal/gdal.h>
#include <gdal/ogr_api.h>
#include <gdal/ogr_core.h>
#include <gdal/ogr_feature.h>
#include <gdal/ogr_geometry.h>
#include <gdal/ogrsf_frmts.h>
#include <gflags/gflags.h>
#include <network/type.hpp>

#include "common/configs/vehicle_config.pb.h"
#include "drivers/gnss/gnss.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "joint/proto/config_region.pb.h"
#include "lam_common/map_data/pose.pb.h"
#include "lam_common/projection.pb.h"
#include "lam_common/sd_map/sd_map_service.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "benchmark/benchmark.h"
#include "common/json.hpp"
#include "common/log.h"
#include "common/time_based_interpolation.h"
#include "data_adapter/ins_adapter.h"
#include "data_adapter/projection_transformation.h"
#include "geometry/graph_shortest_path.h"
#include "joint/ll_utils.h"
#include "lam_common/proto_utils.h"
#include "lam_common/utm_projection_convert.h"
#include "localizer/localizer.h"
#include "mm_localizer/utils.h"
#include "os_interface/os_interface.h"

DEFINE_string(config_lock_on_road,
              "/opt/deeproute/map-engine/config/lock_on_road/"
              "config.cfg",
              "config_lock_on_road");
DEFINE_string(input_dir_path, "", "");
DEFINE_string(pose_path, "", "");
DEFINE_int32(prior_map_delta, 100, "");
DEFINE_string(prior_map_path, "/tmp/lor_log/prior_map.txt", "");
DEFINE_string(sd_map_geojson_path, "/tmp/lor_log/sdmap.geojson", "");
DEFINE_string(sd_map_shp_path, "/tmp/lor_log/sdmap.shp", "");
DEFINE_string(sd_map_path, "", "");
DEFINE_string(out_path, "", "");
DEFINE_string(out_root_path, "", "");
DEFINE_double(sample_distance, 2, "");
DEFINE_bool(enable_log, false, "");
DEFINE_int32(pose_start_index, 0, "");
DEFINE_int32(pose_end_index, 200000, "");
DEFINE_bool(overwrite_temp_shp_file, false, "");

using json = nlohmann::json;

namespace deeproute {
namespace localization {
namespace {

bool LoadPosesAndProjectionPoint(const std::string pose_path,
                                 std::vector<drivers::gnss::Ins>* poses,
                                 deeproute::proto::Projection* projection) {
  deeproute::proto::Trajectory traj;
  if (!deeproute::LoadProtoFile(pose_path, &traj)) {
    MLOG(FATAL) << "failed to pose at: " << pose_path;
    return false;
  }

  if (traj.poses_size() == 0) {
    MLOG(FATAL) << "trajectory is empty.";
    return false;
  }

  *projection = traj.meta_data().projection();

  drivers::gnss::Ins prev_ins;
  prev_ins.mutable_position()->set_x(0.0);
  prev_ins.mutable_position()->set_y(0.0);
  for (int i = 0; i < traj.poses_size(); ++i) {
    drivers::gnss::Ins ins;

    ins.set_measurement_time(traj.poses(i).time_us());

    ins.mutable_position()->set_x(traj.poses(i).position().x());
    ins.mutable_position()->set_y(traj.poses(i).position().y());
    ins.mutable_position()->set_z(traj.poses(i).position().z());

    Vector2d cur_position(traj.poses(i).position().x(),
                          traj.poses(i).position().y());
    Vector2d prev_position(prev_ins.position().x(), prev_ins.position().y());
    if (prev_position.norm() == 0.0) {
      ins.mutable_linear_velocity_flu()->set_x(0.0);
      ins.mutable_linear_velocity_flu()->set_y(0.0);
      ins.mutable_linear_velocity_flu()->set_z(0.0);
    } else {
      double dt =
          (traj.poses(i).time_us() - prev_ins.measurement_time()) / 1e-6;
      Vector2d velocity_flu = (cur_position - prev_position) / dt;
      ins.mutable_linear_velocity_flu()->set_x(velocity_flu.x());
      ins.mutable_linear_velocity_flu()->set_y(velocity_flu.y());
      ins.mutable_linear_velocity_flu()->set_z(0.0);
    }
    prev_ins = ins;

    ins.mutable_euler_angles()->set_x(traj.poses(i).roll_pitch_yaw().x());
    ins.mutable_euler_angles()->set_y(traj.poses(i).roll_pitch_yaw().y());
    ins.mutable_euler_angles()->set_z(traj.poses(i).roll_pitch_yaw().z());

    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().x() *
                                           traj.poses(i).position_std().x());
    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().y() *
                                           traj.poses(i).position_std().y());
    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().z() *
                                           traj.poses(i).position_std().z());

    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().x() *
        traj.poses(i).euler_angle_std().x());
    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().y() *
        traj.poses(i).euler_angle_std().y());
    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().z() *
        traj.poses(i).euler_angle_std().z());

    poses->push_back(ins);
  }

  MLOG(INFO) << "Loaded " << poses->size() << " poses.";

  return true;
}

bool LoadSdMap(const std::string sd_map_path,
               deeproute::sd_map::SDMap* sd_map) {
  if (!deeproute::LoadProtoFile(sd_map_path, sd_map)) {
    MLOG(FATAL) << "failed to load sd map at " << sd_map_path << ".";
    return false;
  }

  MLOG(INFO) << "Loaded sd map with " << sd_map->links_size() << " links.";
  return true;
}

int64_t HashLatLon(const double lat, const double lon) {
  int64_t hash = static_cast<int64_t>(round(lat * 1e6)) << 32 |
                 static_cast<int64_t>(round(lon * 1e6));
  return hash;
}
// int64_t HashLatLon(const double lat, const double lon) {
//   int64_t hash = static_cast<int64_t>(lat) << 32 | static_cast<int64_t>(lon);
//   return hash;
// }

double ComputeGeometryLength(const std::vector<Vector2d>& wps) {
  double length = 0.0;
  for (std::size_t i = 1; i < wps.size(); ++i) {
    const Vector2d& p1 = wps[i - 1];
    const Vector2d& p2 = wps[i];
    double dx = p2[0] - p1[0];
    double dy = p2[1] - p1[1];
    double segmentLength = std::sqrt(dx * dx + dy * dy);
    length += segmentLength;
  }
  return length;
}

json ConvertSdQueriedLinksToGeojson(
    const deeproute::sd_map::SDMap& queryLinksRep,
    const ProjectionTransformation& utm_projector) {
  json featureCollection;
  featureCollection["type"] = "FeatureCollection";
  json features;

  std::vector<double> link_lens;
  for (const auto& link : queryLinksRep.links()) {
    json feature;
    feature["type"] = "Feature";
    json geometry;
    geometry["type"] = "LineString";

    json coordinates;
    std::vector<Vector2d> wps;
    for (const auto& point : link.points()) {
      json coordinate;
      coordinate.push_back(point.lon());
      coordinate.push_back(point.lat());
      coordinates.push_back(coordinate);

      double wgs_lat, wgs_lon;
      Gcj02ToWgs84(point.lon(), point.lat(), &wgs_lon, &wgs_lat);
      double utm_x, utm_y;
      utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &utm_x, &utm_y);
      wps.push_back(Vector2d(utm_x, utm_y));
    }

    const double link_len = ComputeGeometryLength(wps);
    link_lens.push_back(link_len);

    geometry["coordinates"] = coordinates;
    feature["geometry"] = geometry;
    feature["properties"]["ID"] = link.dr_link_id();
    feature["properties"]["Start"] =
        HashLatLon(link.points(0).lat(), link.points(0).lon());
    feature["properties"]["End"] =
        HashLatLon(link.points(coordinates.size() - 1).lat(),
                   link.points(coordinates.size() - 1).lon());
    feature["properties"]["Length"] = link_len;
    // assign only the start and end point to wps
    feature["properties"]["Start_pt"] = std::to_string(link.points(0).lat()) +
                                        ", " +
                                        std::to_string(link.points(0).lon());
    feature["properties"]["End_pt"] =
        std::to_string(link.points(coordinates.size() - 1).lat()) + ", " +
        std::to_string(link.points(coordinates.size() - 1).lon());

    features.push_back(feature);
  }

  MLOG(INFO) << "Max link  len: "
             << *(std::max_element(link_lens.begin(), link_lens.end()));

  featureCollection["features"] = features;
  return featureCollection;
}

bool ConvertGeoJsonToShp(const std::string& geoJsonFilePath,
                         const std::string& shpFilePath,
                         const std::string& layerName) {
  GDALAllRegister();

  GDALDataset* geoJsonDataset = (GDALDataset*)GDALOpenEx(
      geoJsonFilePath.c_str(), GDAL_OF_VECTOR, nullptr, nullptr, nullptr);
  if (geoJsonDataset == nullptr) {
    // Handle error opening the GeoJSON file
    return false;
  }

  OGRLayer* geoJsonLayer = geoJsonDataset->GetLayer(0);

  GDALDriver* shpDriver =
      GetGDALDriverManager()->GetDriverByName("ESRI Shapefile");
  GDALDataset* shpDataset =
      shpDriver->Create(shpFilePath.c_str(), 0, 0, 0, GDT_Unknown, nullptr);
  OGRLayer* shpLayer =
      shpDataset->CreateLayer(layerName.c_str(), nullptr, wkbUnknown, nullptr);

  OGRFeatureDefn* geoJsonFeatureDefn = geoJsonLayer->GetLayerDefn();
  for (int i = 0; i < geoJsonFeatureDefn->GetFieldCount(); ++i) {
    OGRFieldDefn* fieldDefn = geoJsonFeatureDefn->GetFieldDefn(i);
    shpLayer->CreateField(fieldDefn);
  }

  OGRFeature* geoJsonFeature;
  geoJsonLayer->ResetReading();
  while ((geoJsonFeature = geoJsonLayer->GetNextFeature()) != nullptr) {
    shpLayer->CreateFeature(geoJsonFeature);
    OGRFeature::DestroyFeature(geoJsonFeature);
  }

  GDALClose(shpDataset);
  GDALClose(geoJsonDataset);

  return true;
}

google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH> Gcj02ToUtm(
    const google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH>&
        shape_points,
    const UtmProjectionConvert& utm_projector) {
  google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH> utm_segment;
  for (const auto& ll : shape_points) {
    double wgs_lon;
    double wgs_lat;
    Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
    double x;
    double y;
    utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);

    auto shape_point = utm_segment.Add();
    shape_point->set_lon(x);
    shape_point->set_lat(y);
  }
  return utm_segment;
}

deeproute::navinfo::SDRoutingResponse ConvertLinksToSdRoutingResponse(
    const deeproute::sd_map::SDMap& links,
    const deeproute::proto::Projection& projection) {
  UtmProjectionConvert utm_projector(projection);
  deeproute::navinfo::SDRoutingResponse sd_routing_response;
  deeproute::navinfo::Result result;
  deeproute::navinfo::Route route;

  for (const auto& link : links.links()) {
    deeproute::navinfo::Segment segm;
    segm.set_ni_id(std::to_string(link.dr_link_id()));

    segm.mutable_shape_points()->CopyFrom(link.points());
    segm.set_f_lane_num(link.forward_lane_num());
    segm.set_pr(link.priority());
    segm.set_tunnel(link.tunnel());
    segm.set_usage(link.usage());
    *route.add_segm() = segm;
  }
  result.mutable_route()->CopyFrom(route);
  *sd_routing_response.add_result() = result;
  sd_routing_response.set_request_id("1");

  return sd_routing_response;
}

Vector2d UtmToGcj02(const ProjectionTransformation& proj,
                    const Vector2d& utm_pose) {
  double lat, lon;
  proj.UtmToLatLon(utm_pose[0], utm_pose[1], &lat, &lon);

  double gcj_lon, gcj_lat;
  Wgs84ToGcj02(lon, lat, &gcj_lon, &gcj_lat);
  return Vector2d(gcj_lat, gcj_lon);
}

void RunLockOnRoadOnPosesAndMap(
    const std::vector<drivers::gnss::Ins>& poses,
    const deeproute::proto::Projection& projection,
    const deeproute::navinfo::SDRoutingResponse& sd_map,
    const std::string& topo_prior_map_path, const std::string output_root,
    std::vector<std::string>* visited_link_ids) {
  const auto start_time = std::chrono::system_clock::now();

  deeproute::localization::LockOnRoadConfig config_lock_on_road;
  if (!deeproute::LoadProtoFile(FLAGS_config_lock_on_road,
                                &config_lock_on_road)) {
    MLOG(FATAL) << "failed to load config lock on road.";
  }

  const int kPoseNum = poses.size();
  config_lock_on_road.mutable_map_matching_config()->set_history_length(
      kPoseNum);

  MLOG(INFO) << "pose num: " << kPoseNum;

  auto localizer =
      CreateLocalizer(config_lock_on_road, projection, kPriorMapMatching);

  ProjectionTransformation utm_proj(projection);

  auto sd_map_ptr = std::make_shared<deeproute::navinfo::SDRoutingResponse>();
  *sd_map_ptr = sd_map;

  localizer->HandlePriorTopoMap(topo_prior_map_path);
  MLOG(INFO) << "LOCK ON MAP COMPONENT TIMER: total time used for setting up "
                "lock on map: handle prior topo map: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - start_time)
                    .count()
             << " ms.";
  auto end_time = std::chrono::system_clock::now();

  auto gnss_ptr = std::make_shared<drivers::gnss::Gnss>();
  gnss_ptr->set_type(drivers::gnss::Gnss_Type_SINGLE);
  localizer->HandleGnssDataMeasurementMsg(gnss_ptr);

  localizer->HandleGlobalRoutingMsg(sd_map_ptr);
  MLOG(INFO) << "LOCK ON MAP COMPONENT TIMER: total time used for setting up "
                "lock on map: handle global routing: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - start_time)
                    .count()
             << " ms.";
  end_time = std::chrono::system_clock::now();

  if (!output_root.empty() && !boost::filesystem::exists(output_root)) {
    boost::filesystem::create_directories(output_root);
  }

  std::ofstream pose_file;
  std::ofstream matched_file;
  std::ofstream pose_to_matched;
  std::ofstream pose_to_matched_gcj02;
  std::ofstream utm_links_file;
  if (FLAGS_enable_log) {
    const std::string out_pose_path =
        boost::filesystem::path(output_root).append("pose_pts.txt").string();
    const std::string out_matched_path =
        boost::filesystem::path(output_root).append("matched_pts.txt").string();
    const std::string pose_to_matched_path =
        boost::filesystem::path(output_root)
            .append("pose_to_matched.txt")
            .string();
    const std::string pose_to_matched_path_gcj02 =
        boost::filesystem::path(output_root)
            .append("pose_to_matched_gcj02.txt")
            .string();
    const std::string utm_links =
        boost::filesystem::path(output_root).append("utm_links.txt").string();

    // create file to write poses
    pose_file.open(out_pose_path);
    matched_file.open(out_matched_path);
    pose_to_matched.open(pose_to_matched_path);
    pose_to_matched_gcj02.open(pose_to_matched_path_gcj02);
    utm_links_file.open(utm_links);

    const auto custom_graph = localizer->GetRoutingResponseGraph();
    for (const auto utm_link : custom_graph) {
      utm_links_file << utm_link.id << " ";

      for (int i = 0; i < utm_link.geom.get_num_points(); i++) {
        const double x = utm_link.geom.get_x(i);
        const double y = utm_link.geom.get_y(i);
        utm_links_file << x << " " << y << " ";
      }

      utm_links_file << "\n";
    }
    utm_links_file.close();
  }

  MLOG(INFO) << "LOCK ON MAP COMPONENT TIMER: total time used for setting up "
                "lock on map: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - start_time)
                    .count()
             << " ms.";
  end_time = std::chrono::system_clock::now();

  deeproute::proto::Trajectory result_trajectory;
  std::vector<double> gcj02_process_times, handle_rtk_times;
  for (size_t idx = 0; idx < poses.size(); idx++) {
    const auto pose = poses[idx];
    auto ins_ptr = std::make_shared<drivers::gnss::Ins>();
    *ins_ptr = pose;

    const auto before_handle_rtk = std::chrono::system_clock::now();
    localizer->HandleRtkInsMeasurementMsg(ins_ptr);
    const auto after_handle_rtk = std::chrono::system_clock::now();
    const auto time_for_handling_rtk =
        std::chrono::duration_cast<std::chrono::milliseconds>(after_handle_rtk -
                                                              before_handle_rtk)
            .count();
    handle_rtk_times.push_back(time_for_handling_rtk);

    if (FLAGS_enable_log) {
      auto gcj02_process_time_start = std::chrono::system_clock::now();
      const Vector2d adc_latlon = UtmToGcj02(
          utm_proj, Vector2d(ins_ptr->position().x(), ins_ptr->position().y()));
      pose_file << std::to_string(adc_latlon[0]) + " " +
                       std::to_string(adc_latlon[1]) + " " +
                       std::to_string(ins_ptr->position().z()) + "\n";
      auto gcj02_process_time_end = std::chrono::system_clock::now();
      gcj02_process_times.push_back(
          std::chrono::duration_cast<std::chrono::milliseconds>(
              std::chrono::system_clock::now() - gcj02_process_time_start)
              .count());
    }

    if (!localizer->SystemReady()) {
      continue;
    }
    MLOG(INFO) << "localizer system ready!";

    const auto& debug_infos = localizer->GetLatestLockOnLaneDebugInfo();
    const auto& lock_on_road_result = localizer->GetLatestLockOnRoadResult();

    MLOG(INFO) << "Debug info size: " << debug_infos.pose_debug_info_size();

    auto wrap_up_time_start = std::chrono::system_clock::now();
    if (debug_infos.pose_debug_info_size() == 0) {
      MLOG(INFO) << "lock on road failed.";
      continue;
    } else {
      const auto debug_info_size = debug_infos.pose_debug_info_size();
      for (size_t idx = 0; idx < debug_info_size; idx++) {
        const auto lor_debug_info = debug_infos.pose_debug_info()[idx];

        const auto world_to_matched_pt = ::common::Transformation3(
            lor_debug_info.matched_position().x(),
            lor_debug_info.matched_position().y(),
            lor_debug_info.position().z(), lor_debug_info.euler_angles().x(),
            lor_debug_info.euler_angles().y(),
            lor_debug_info.euler_angles().z());

        if (FLAGS_enable_log) {
          pose_to_matched
              << std::to_string(lor_debug_info.position().x()) + " " +
                     std::to_string(lor_debug_info.position().y()) + " " +
                     std::to_string(lor_debug_info.matched_position().x()) +
                     " " +
                     std::to_string(lor_debug_info.matched_position().y()) +
                     "\n";

          double pose_lat_wgs84, pose_lon_wgs84, matched_pose_lat_wgs84,
              matched_pose_lon_wgs84;
          utm_proj.UtmToLatLon(lor_debug_info.position().x(),
                               lor_debug_info.position().y(), &pose_lat_wgs84,
                               &pose_lon_wgs84);
          utm_proj.UtmToLatLon(lor_debug_info.matched_position().x(),
                               lor_debug_info.matched_position().y(),
                               &matched_pose_lat_wgs84,
                               &matched_pose_lon_wgs84);
          double pose_lat_gcj02, pose_lon_gcj02, matched_pose_lat_gcj02,
              matched_pose_lon_gcj02;
          Wgs84ToGcj02(pose_lon_wgs84, pose_lat_wgs84, &pose_lon_gcj02,
                       &pose_lat_gcj02);
          Wgs84ToGcj02(matched_pose_lon_wgs84, matched_pose_lat_wgs84,
                       &matched_pose_lon_gcj02, &matched_pose_lat_gcj02);
          pose_to_matched_gcj02
              << std::to_string(pose_lon_gcj02) + " " +
                     std::to_string(pose_lat_gcj02) + " " +
                     std::to_string(matched_pose_lon_gcj02) + " " +
                     std::to_string(matched_pose_lat_gcj02) + "\n";
        }

        const auto world_to_matched_pt_position =
            world_to_matched_pt.GetTranslation();
        const auto world_to_matched_pt_euler_angles =
            world_to_matched_pt.GetRollPitchYaw();

        deeproute::proto::Pose result_pose;
        result_pose.set_time_us(lor_debug_info.time());
        // result_pose.set_lock_on_road_status(lor_debug_info.status());
        result_pose.set_lock_on_road_status(
            deeproute::localization::LockOnRoadResult_Status_NORMAL);
        result_pose.set_sd_link_id(lor_debug_info.debug_sd_link_id());

        result_pose.mutable_position()->set_x(world_to_matched_pt_position.x());
        result_pose.mutable_position()->set_y(world_to_matched_pt_position.y());
        result_pose.mutable_position()->set_z(world_to_matched_pt_position.z());
        result_pose.mutable_roll_pitch_yaw()->set_x(
            world_to_matched_pt_euler_angles.x());
        result_pose.mutable_roll_pitch_yaw()->set_y(
            world_to_matched_pt_euler_angles.y());
        result_pose.mutable_roll_pitch_yaw()->set_z(
            world_to_matched_pt_euler_angles.z());

        // std related.
        result_pose.mutable_position_std()->set_x(
            std::sqrt(ins_ptr->position_covariance()[0]));
        result_pose.mutable_position_std()->set_y(
            std::sqrt(ins_ptr->position_covariance()[1]));
        result_pose.mutable_position_std()->set_z(
            std::sqrt(ins_ptr->position_covariance()[2]));
        result_pose.mutable_euler_angle_std()->set_x(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));
        result_pose.mutable_euler_angle_std()->set_y(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));
        result_pose.mutable_euler_angle_std()->set_z(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));

        if (FLAGS_enable_log) {
          const Vector2d matched_latlon = UtmToGcj02(
              utm_proj, Vector2d(lor_debug_info.matched_position().x(),
                                 lor_debug_info.matched_position().y()));
          matched_file << std::to_string(matched_latlon[0]) + " " +
                              std::to_string(matched_latlon[1]) + " " +
                              std::to_string(world_to_matched_pt_position.z()) +
                              "\n";
        }

        *result_trajectory.add_poses() = result_pose;

        visited_link_ids->push_back(result_pose.sd_link_id());
      }
    }

    auto wrap_up_time_end = std::chrono::system_clock::now();
    MLOG(INFO)
        << "LOCK ON MAP COMPONENT TIMER: total time used for wrapping up: "
        << std::chrono::duration_cast<std::chrono::seconds>(wrap_up_time_end -
                                                            wrap_up_time_start)
               .count()
        << " s.";
  }

  // save result to output path
  const auto writing_time_start = std::chrono::system_clock::now();
  if (!WriteProtoFile(FLAGS_out_path, result_trajectory)) {
    MLOG(FATAL) << "writting resul to " << FLAGS_out_path << " failed.";
  } else {
    MLOG(INFO) << "writting resul to " << FLAGS_out_path << " success.";
  }
  const std::string rr_path = boost::filesystem::path(output_root)
                                  .append("routing_response.txt")
                                  .string();
  WriteProtoFile(rr_path, *sd_map_ptr);
  MLOG(INFO)
      << "LOCK ON MAP COMPONENT TIMER: total time used writting file to disk: "
      << std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::now() - writing_time_start)
             .count()
      << " ms.";

  pose_file.close();
  matched_file.close();
  pose_to_matched.close();
  pose_to_matched_gcj02.close();

  // calculate sum and mean time of gcj02_process_times
  double sum_time = 0.0;
  for (const auto& time : gcj02_process_times) {
    sum_time += time;
  }
  MLOG(INFO) << "total time used for gcj02 process: " << sum_time << " ms.";
  MLOG(INFO) << "mean time of gcj02 process: "
             << sum_time / gcj02_process_times.size() << " ms.";

  // calculate sum and mean time of handle_rtk_times
  sum_time = 0.0;
  for (const auto& time : handle_rtk_times) {
    sum_time += time;
  }
  MLOG(INFO) << "total time used for handling rtk: " << sum_time << " ms.";
  MLOG(INFO) << "mean time of handle rtk: "
             << sum_time / handle_rtk_times.size() << " ms.";
}

void WritePosesToGeojson(const std::string& out_path,
                         const deeproute::proto::Projection& prj,
                         const std::vector<drivers::gnss::Ins>& poses) {
  std::ofstream all_pose_file(out_path);
  ProjectionTransformation utm_proj(prj);

  // Create the GeoJSON object
  nlohmann::json geojson;
  geojson["type"] = "FeatureCollection";

  nlohmann::json features;

  int id = 0;
  for (const auto& pose : poses) {
    // Create a feature object for each pose
    nlohmann::json feature;
    feature["type"] = "Feature";

    // Create a geometry object with the point coordinates
    nlohmann::json geometry;
    geometry["type"] = "Point";

    double lat, lon;
    utm_proj.UtmToLatLon(pose.position().x(), pose.position().y(), &lat, &lon);
    double lat_gcj02, lon_gcj02;
    // Wgs84ToGcj02(lon, lat, &lon_gcj02, &lat_gcj02);
    lon_gcj02 = lon;
    lat_gcj02 = lat;

    nlohmann::json coordinates;
    coordinates.push_back(lon_gcj02);
    coordinates.push_back(lat_gcj02);

    geometry["coordinates"] = coordinates;
    feature["geometry"] = geometry;
    feature["properties"]["ID"] = id;

    // Add the feature to the feature array
    features.push_back(feature);
    id += 1;
  }

  geojson["features"] = features;

  // Write the GeoJSON to the file
  all_pose_file << geojson.dump(4);
  all_pose_file.close();
}

void WritePosesToLineString(const std::string& out_path,
                            const std::vector<drivers::gnss::Ins>& poses) {
  std::ofstream line_string_file(out_path);

  // Write the header: id;geom
  line_string_file << "id;geom" << std::endl;
  line_string_file << "1;LINESTRING (";

  for (int i = 0; i < poses.size(); i++) {
    const auto pose = poses[i];
    const auto& position = pose.position();
    line_string_file << position.x() << ' ' << position.y();

    if (i < poses.size() - 1) {
      line_string_file << ',';
    } else {
      line_string_file << ')';
    }
  }

  line_string_file.close();
}

std::vector<drivers::gnss::Ins> RemoveLargeDeltaPoses(
    const std::vector<drivers::gnss::Ins>& poses) {
  double max_dist = 0.0;

  std::vector<drivers::gnss::Ins> valid_poses;
  for (int i = 0; i < poses.size() - 1; i++) {
    const auto pose = poses[i];
    const auto next_pose = poses[i + 1];
    const auto dist =
        (Vector2d(pose.position().x(), pose.position().y()) -
         Vector2d(next_pose.position().x(), next_pose.position().y()))
            .norm();

    if (dist > max_dist) {
      max_dist = dist;
    }

    if (i >= FLAGS_pose_start_index && i < FLAGS_pose_end_index) {
      valid_poses.push_back(pose);
    }
  }

  MLOG(INFO) << "max dist: " << max_dist << " m.";
  return valid_poses;
}  // namespace

void Process() {
  std::vector<drivers::gnss::Ins> poses;
  deeproute::proto::Projection projection;

  auto start_time = std::chrono::system_clock::now();
  if (!LoadPosesAndProjectionPoint(FLAGS_pose_path, &poses, &projection)) {
    MLOG(FATAL) << "failed to load poses and projection point.";
    return;
  }
  auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "total time for loading pose and projection pt: "
             << std::chrono::duration_cast<std::chrono::seconds>(end_time -
                                                                 start_time)
                    .count()
             << " s.";
  poses = RemoveLargeDeltaPoses(poses);

  ProjectionTransformation utm_proj(projection);
  const std::string all_pose_file_path =
      boost::filesystem::path(FLAGS_out_root_path)
          .append("poses_gcj02.geojson")
          .string();
  WritePosesToGeojson(all_pose_file_path, projection, poses);
  const std::string all_pose_file_csv_path =
      boost::filesystem::path(FLAGS_out_root_path)
          .append("poses_utm.csv")
          .string();
  WritePosesToLineString(all_pose_file_csv_path, poses);

  MLOG(INFO) << "total time for writing poses to file: "
             << std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " s.";
  end_time = std::chrono::system_clock::now();

  deeproute::sd_map::SDMap sd_map;
  LoadSdMap(FLAGS_sd_map_path, &sd_map);
  MLOG(INFO) << "total time for loading sd map: "
             << std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " s.";
  end_time = std::chrono::system_clock::now();

  CHECK(!FLAGS_sd_map_geojson_path.empty())
      << "FLAGS_sd_map_geojson_path empty: " << FLAGS_sd_map_geojson_path;
  std::ofstream outputFile(FLAGS_sd_map_geojson_path);
  const auto sd_map_geojson = ConvertSdQueriedLinksToGeojson(sd_map, utm_proj);
  // convert QueryLinksRep to Geojson and write it to a file
  if (outputFile.is_open()) {
    outputFile << sd_map_geojson.dump(
        4);  // Write JSON with indentation of 4 spaces
    outputFile.close();
    MLOG(INFO) << "Geojson data has been written to the file.";
  } else {
    MLOG(INFO) << "Unable to open the file for writing.";
  }
  MLOG(INFO) << "total time for converting and writing geojson file: "
             << std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " s.";
  end_time = std::chrono::system_clock::now();

  // convert geojson file to shp map file
  CHECK(!FLAGS_sd_map_shp_path.empty())
      << "FLAGS_sd_map_shp_path empty: " << FLAGS_sd_map_shp_path;
  if (!ConvertGeoJsonToShp(FLAGS_sd_map_geojson_path, FLAGS_sd_map_shp_path,
                           "DR")) {
    "Converting geojson file to shp file failed.";
  }
  MLOG(INFO) << "total time for converting and writing shp file: "
             << std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " s.";
  end_time = std::chrono::system_clock::now();

  // convert shp map file to prior map file.
  CHECK(!FLAGS_prior_map_path.empty())
      << "FLAGS_prior_map_path empty: " << FLAGS_prior_map_path;
  const std::string command = absl::Substitute(
      "/opt/deeproute/fmm/bin/ubodt_gen --network $0  --output $1 --delta "
      "$2 --source Start --target End --network_id ID",
      FLAGS_sd_map_shp_path, FLAGS_prior_map_path, FLAGS_prior_map_delta);
  MLOG(INFO) << "command: " << command;
  auto p1 = boost::process::child(
      std::vector<std::string>{"/bin/bash", "-c", command},
      boost::process::std_out > boost::process::null,
      boost::process::std_err > boost::process::null);
  p1.wait();
  MLOG(INFO) << "total time for converting and writing prior map: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " ms.";
  end_time = std::chrono::system_clock::now();

  deeproute::navinfo::SDRoutingResponse sd_routing_response =
      ConvertLinksToSdRoutingResponse(sd_map, projection);
  MLOG(INFO) << "total time for converting links to sd routing response: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " ms.";
  end_time = std::chrono::system_clock::now();

  std::vector<std::string> sparsely_visited_links;
  RunLockOnRoadOnPosesAndMap(
      poses, projection, sd_routing_response, FLAGS_prior_map_path,
      boost::filesystem::path(FLAGS_out_root_path).append("sparse").string(),
      &sparsely_visited_links);
  MLOG(INFO) << "total time for lock on map: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now() - end_time)
                    .count()
             << " ms.";
}

}  // namespace
}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  // os::Node nh{"localization"};
  // google::InitGoogleLogging(argv[0]);
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  if (!boost::filesystem::exists("/tmp/lor_log/")) {
    boost::filesystem::create_directory("/tmp/lor_log/");
  }

  if (FLAGS_input_dir_path.empty()) {
    CHECK(!FLAGS_pose_path.empty()) << "pose path is empty.";
    CHECK(!FLAGS_sd_map_path.empty()) << "sd map path is empty.";

    if (!FLAGS_out_root_path.empty()) {
      FLAGS_out_path = boost::filesystem::path(FLAGS_out_root_path)
                           .append("result_pose.txt")
                           .string();
    } else {
      CHECK(!FLAGS_out_path.empty()) << "out path is empty.";
    }

  } else {
    std::string temp_root_path = FLAGS_input_dir_path;
    FLAGS_pose_path =
        boost::filesystem::path(temp_root_path).append("hd_pose.txt").string();
    temp_root_path = FLAGS_input_dir_path;
    // FLAGS_sd_map_path =
    //     std::find_if(
    //         boost::filesystem::directory_iterator(temp_root_path),
    //         boost::filesystem::directory_iterator(),
    //         [](const boost::filesystem::directory_entry& entry) {
    //           return boost::filesystem::is_regular_file(entry.status()) &&
    //                  entry.path().filename().string().find("sdmap.txt") !=
    //                      std::string::npos;
    //         })
    //         ->path()
    //         .string();
    FLAGS_sd_map_path =
        boost::filesystem::path(temp_root_path).append("sdmap.txt").string();
    FLAGS_sd_map_geojson_path = boost::filesystem::path(FLAGS_input_dir_path)
                                    .append("sdmap.geojson")
                                    .string();
    FLAGS_sd_map_shp_path = boost::filesystem::path(FLAGS_input_dir_path)
                                .append("sdmap.shp")
                                .string();
    FLAGS_prior_map_path =
        boost::filesystem::path(temp_root_path).append("ubodt.txt").string();
    temp_root_path = FLAGS_input_dir_path;
    FLAGS_out_root_path =
        boost::filesystem::path(temp_root_path).append("result").string();
    temp_root_path = FLAGS_input_dir_path;
    FLAGS_out_path = boost::filesystem::path(temp_root_path)
                         .append("result")
                         .append("result_pose.txt")
                         .string();
    temp_root_path = FLAGS_input_dir_path;

    if (!boost::filesystem::exists(FLAGS_out_root_path)) {
      boost::filesystem::create_directories(FLAGS_out_root_path);
    }
    MLOG(INFO) << "You provided with a data input dir, so";
    MLOG(INFO) << "HD pose: " << FLAGS_pose_path;
    MLOG(INFO) << "SD map: " << FLAGS_sd_map_path;
    MLOG(INFO) << "FLAGS_prior_map_path: " << FLAGS_prior_map_path;
    MLOG(INFO) << "Result output: " << FLAGS_out_path;
    MLOG(INFO) << "Result output root: " << FLAGS_out_root_path;
  }

  // calculate time used for entire process.
  const auto start_time = std::chrono::system_clock::now();
  deeproute::localization::Process();
  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "total time used: "
             << std::chrono::duration_cast<std::chrono::milliseconds>(
                    end_time - start_time)
                    .count()
             << " ms.";

  return 0;
}
