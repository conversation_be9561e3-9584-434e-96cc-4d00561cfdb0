import os
import matplotlib
matplotlib.use('Agg') 
import matplotlib.pyplot as plt
import numpy as np


file = open("../build/log1.log").readlines()
tss = []
x_values = []
y_values = []
for line in file:
    line = line.split()
    if len(line) == 25:
        tss.append(int(line[6])/1e6)
        print(line[8], line[9])
        try:
            x_values.append(float(line[8]))
            y_values.append(float(line[9]))
        except:
            continue

plt.figure(figsize=(10, 6))
plt.plot(x_values, y_values)
plt.xlabel('X')
plt.ylabel('Y')
plt.title('Amap Evaluated UTM')
plt.grid()
plt.savefig('amap_evaluated_utm.png', dpi=300)


dts = []
for i in range(len(tss)-1):
    dt = tss[i+1] - tss[i]
    dts.append(dt)

print(max(dts))
print(min(dts))
print(np.mean(dts))


import re
file = open("../build/log2.log").readlines()
tss = []
for line in file:
    # line = line.split('.')
    line = re.split(",| ", line)
    print(line, len(line))
    if len(line) == 38:
        print(line[10])
        try:
            tss.append(int(line[10])/1e6)
        except:
            continue

dts = []
for i in range(len(tss)-1):
    dt = tss[i+1] - tss[i]
    dts.append(dt)

print(max(dts))
print(min(dts))
print(np.mean(dts))