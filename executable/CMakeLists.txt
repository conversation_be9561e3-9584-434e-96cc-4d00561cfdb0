
install(DIRECTORY ros_share/
DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)
install(DIRECTORY os_starter_config/
DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION})
install(DIRECTORY python
DESTINATION ${CATKIN_PACKAGE_SHARE_DESTINATION}
)


# if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
#         if(ENABLE_ROS)
#                 add_executable(ddmm_debug_on_bag
#                         ddmm_debug_on_bag.cpp)
#                 target_link_libraries(ddmm_debug_on_bag
#                         gflags
#                         lam_common_base
#                         ${PROTOBUF_LIBRARIES}
#                         Common::os_interface
#                         lane_localization_proto
#                         localization_mm
#                         bag_viewer
#                         common_conversions
#                 )
#                 install(TARGETS
#                 ddmm_debug_on_bag
#                 ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#                 LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
#                 RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})
#         endif()
# endif()

add_executable(ddmm_debug_performance_on_bag
        ddmm_debug_performance_on_bag.cpp)
target_link_libraries(ddmm_debug_performance_on_bag
        gflags
        lam_common_base
        ${PROTOBUF_LIBRARIES}
        Common::os_interface
        lane_localization_proto
        localization_mm
)

add_executable(run_lor_on_case
        run_lor_on_case.cpp)
target_link_libraries(run_lor_on_case
        gflags
        lam_common_base
        ${PROTOBUF_LIBRARIES}
        Common::os_interface
        lane_localization_proto
        localization_mm
        localization_localizer
        common_conversions
        lam_common_client
)

add_executable(run_lor_on_bags
        run_lor_on_bags.cpp)
target_link_libraries(run_lor_on_bags
        gflags
        lam_common_base
        ${PROTOBUF_LIBRARIES}
        Common::os_interface
        lane_localization_proto
        localization_mm
        localization_localizer
        common_conversions
        lam_common_client
)

add_executable(generate_autotune_file
        generate_autotune_file.cpp)
        target_link_libraries(generate_autotune_file
        gflags
        lam_common_base
        ${PROTOBUF_LIBRARIES}
        lane_localization_proto
        localization_mm
        ${engine_lib}
        cudart
)

install(TARGETS
        # ddmm_debug_on_bag
        ddmm_debug_performance_on_bag
        generate_autotune_file
        run_lor_on_case
        run_lor_on_bags
ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})

# if(CHURCH_BUILD)
# add_library(lock_on_road_component_impl lock_on_road_component_impl.cpp)
# target_link_libraries(lock_on_road_component_impl
#         PRIVATE gflags
#         PRIVATE lam_common_base
#         PRIVATE ${PROTOBUF_LIBRARIES}
#         PRIVATE absl::strings
#         PRIVATE lane_localization_proto
#         PRIVATE localization_localizer
#         PRIVATE lam_common_proto
#         # PRIVATE lam_common_client
# )

# install(TARGETS lock_on_road_component_impl
# ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
# LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
# RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})

# install(FILES lock_on_road_component_impl.h
# DESTINATION ${CATKIN_PACKAGE_INCLUDE_DESTINATION})
# endif(CHURCH_BUILD)
