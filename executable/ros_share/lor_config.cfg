topics{
    pose_topic: "/sensors/gnss/pose"
    gnss_topic: "/sensors/gnss/raw_gnss_position"
    odometry_topic: "/localization/pose"
    ras_map_topic: "/perception/ras_map"
    routing_response_topic: "/map/routing/internal/response"
    perception_topic: "/perception/objects"
    publish_lock_on_road_topic: "/localization/lock_on_road"
    publish_lane_index_estimation_topic: "/localization/lane_index"
    publish_lock_on_road_debug_topic: "/localization/lock_on_road_debug"
    debug_ras_map_topic: "/localization/debug_ras_map"
    # lidar_topic: "/sensors/lidar/combined_point_cloud"
}

lane_estimator_config{
    window_size: 3
    lateral_change_threshold: 1.5
    enable_lane_estimation: true
    process_with_topology: true
    use_odom_measurement: false
}

map_matching_config{
    window_size: 20
    obs_noise: 10
    transition_noise: 0.25
    heading_noise: 0.5 #0.75
    search_radius: 100
    fmm_log_level: "info" # one of debug, info, trace
    neigbhor_num: 12
    history_length: 2
    
    # for debug
    noise_injection {
        enable: false
        mean: 0
        std: 5 
    }

    log_config {
        # local log path
        # log_path: "/home/<USER>/Documents/match_replay/replay_log_dir"
        log_path: ""
        # true: enble write log to local
        enable_write_log: false
        log_type: 1
    }

    enable_amap: true

    enable_routing_mask_yaw_judgement: true
}

ddmm_config{
    enable_ddmm: true
    enable_ddmm_debug_output: true
    use_dla: true
    use_calib_file: false
}

enable_visualizer: true
pub_sd_map: false
pub_semantic_map: false

enable_ras_map_post_processing: true
