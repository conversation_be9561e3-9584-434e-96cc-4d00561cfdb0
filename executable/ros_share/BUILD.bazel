package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")
load("@deeproute_build_tools//rules/pkg:deeproute_release.bzl", "deeproute_release_package")

filegroup(
    name = "configs",
    srcs = [
        "ddmm.dla",
        "debug.cfg",
        "lor_config.cfg",
        "regions.cfg",
        "test.launch",
    ]
)

deeproute_release_package(
    name = "package",
    srcs = [ ":configs" ],
    mode = "0755",
    package_dir = "ros_share",
)
