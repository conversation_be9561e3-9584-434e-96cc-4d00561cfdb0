<launch>
	<env name="MLOG_logtostderr" value="1" />
	<env name="MLOG_alsologtostderr" value="1" />
	<arg name="bag_path" default="/home/<USER>/map_matching/data/bags/3.bag" />
    <arg name="config_path" default="/home/<USER>/lane-based-localization-benchmark/executable/ros_share/config.cfg" />
    <arg name="planned_links" default="/home/<USER>/map_matching/data/bags/3_links.txt" />

	<node pkg="lane_based_localization" type="run_lock_on_road_offline_single_trip" name="localization" ns="localization" args="--planned_links ${planned_links} --config_lock_on_road ${config_path} --bag_path ${bag_path}" output="screen">
	</node>
	<node type="rviz" name="rviz" pkg="rviz" args="-d $(find lane_based_localization)/debug.rviz" />
</launch>
