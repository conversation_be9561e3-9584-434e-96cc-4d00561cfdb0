topics{
    pose_topic: "/sensors/gnss/pose"
    gnss_topic: "/sensors/gnss/gnss"
    odometry_topic: "/localization/pose"
    ras_map_topic: "/perception/ras_map"
    routing_response_topic: "/map/routing/internal/response"
    perception_topic: "/perception/perception_obstacles"
    publish_lock_on_road_topic: "/localization/lock_on_road"
    publish_lane_index_estimation_topic: "/localization/lane_index"
    publish_lock_on_road_debug_topic: "/localization/lock_on_road_debug"
}


lane_estimator_config{
    window_size: 10
    lateral_change_threshold: 0.6
}

map_matching_config{
    window_size: 20
    obs_noise: 2
    transition_noise: 0.5
    heading_noise: 0.5 #0.75
    search_radius: 100
    fmm_log_level: "info" # one of debug, info, trace
    neigbhor_num: 12
    history_length: 700
    
    # for debug
    noise_injection{
        enable: false
        mean: 0
        std: 5 
    }

    log_config {
        log_path: "/home/<USER>/Documents/match_replay/replay_log_dir"
        enable_write_log: false
        log_type: 1
    }
    
}

enable_visualizer: true
pub_sd_map: true
pub_semantic_map: false

# for debug purposes, when pose matched to a particular edge, system will imediately return
# debug_edge: "109070521"
# debug_edge: "1050092"
# debug_edge: "620584550"
# debug_edge: "29848445"
# debug_edge: "1050881"
# debug_edge: "620582340"
# debug_edge: "500089609"
# debug_edge: "58322353"