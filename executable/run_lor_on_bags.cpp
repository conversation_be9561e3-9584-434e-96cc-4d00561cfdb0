#include <stdlib.h>

#include <fstream>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>
#include <network/type.hpp>

#include "common/configs/vehicle_config.pb.h"
#include "drivers/gnss/gnss_raw.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "joint/proto/config_region.pb.h"
#include "lam_common/projection.pb.h"
#include "map/sd_map.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "common/conversions/cvmat_conversions.h"
#include "common/log.h"
#include "data_adapter/ins_adapter.h"
#include "lam_common/proto_utils.h"
#include "lam_common/utm_projection_convert.h"
#include "localizer/localizer.h"
#include "mm_localizer/utils.h"
#include "os_interface/os_interface.h"

DEFINE_string(config_lock_on_road,
              "/opt/deeproute/map-engine/config/lock_on_road/"
              "config.cfg",
              "config_lock_on_road");
DEFINE_string(in, "", "");
DEFINE_string(out, "", "");
DEFINE_bool(online_query_sdmap, false, "");

const std::string kDefaultRegionInfoPath =
    "/opt/deeproute/map-engine/config/lock_on_road/"
    "regions.cfg";

namespace deeproute {
namespace localization {
namespace {}  // namespace

void Process(const std::string& bag_dir, const std::string& out_dir) {
  if (boost::filesystem::exists(out_dir)) {
    boost::filesystem::remove_all(out_dir);
  }
  boost::filesystem::create_directories(out_dir);

  const std::string camera_1_dir = out_dir + "/camera_1";
  if (!boost::filesystem::exists(camera_1_dir)) {
    boost::filesystem::create_directories(camera_1_dir);
  }

  setenv("DDMM_DEBUG", "1", true);
  setenv("DDMM_DEBUG_DIR", out_dir.c_str(), true);
  setenv("DDMM_ONLINE_QUERY_SDMAP",
         std::to_string(FLAGS_online_query_sdmap).c_str(), true);

  deeproute::localization::LockOnRoadConfig lock_on_road_config;
  if (!deeproute::LoadProtoFile(FLAGS_config_lock_on_road,
                                &lock_on_road_config)) {
    MLOG(FATAL) << "failed to load config: " << FLAGS_config_lock_on_road;
  }

  std::set<std::string> topics;
  // const std::string kGnssTopic = "/sensors/gnss/gnss";
  const std::string kRtkTopic = "/sensors/gnss/pose";
  const std::string kGnssPositionTopic = "/sensors/gnss/raw_gnss_position";
  const std::string kRoutingResponse = "/map/routing/internal/response";
  const std::string kRasMapNNTopic = "/perception/ras_map_nn";
  const std::string kRasMapTopic = "/perception/ras_map";
  const std::string kSdMapTopic = "/map/sd_map";
  const std::string kHorizonMapTopic = "/map/sd_horizon_map";
  const std::string kCameraTopic =
      "/sensors/camera/camera_1_raw_data/compressed_proto";

  // topics.emplace(kGnssTopic);
  topics.emplace(kRtkTopic);
  topics.emplace(kGnssPositionTopic);
  topics.emplace(kRoutingResponse);
  topics.emplace(kSdMapTopic);
  topics.emplace(kHorizonMapTopic);
  topics.emplace(kRasMapNNTopic);
  topics.emplace(kRasMapTopic);
  topics.emplace(kCameraTopic);
  std::vector<std::string> all_dir_files;
  boost::filesystem::path bag_dir_path(bag_dir);
  boost::filesystem::directory_iterator end_itr;
  for (boost::filesystem::directory_iterator itr(bag_dir_path); itr != end_itr;
       ++itr) {
    all_dir_files.push_back(itr->path().string());
  }
  std::sort(all_dir_files.begin(), all_dir_files.end());

  for (const auto file_path : all_dir_files) {
    if (boost::filesystem::path(file_path).extension() != ".bag") {
      continue;
    }
    const auto bag_path = file_path;
    MLOG(INFO) << "Bag path :" << bag_path;

    const auto bag_name = boost::filesystem::path(bag_path).stem().string();
    const std::string bag_out_dir = out_dir + "/" + bag_name;
    if (boost::filesystem::exists(bag_out_dir)) {
      boost::filesystem::remove_all(bag_out_dir);
    }
    boost::filesystem::create_directories(bag_out_dir);
    setenv("DDMM_DEBUG_DIR", bag_out_dir.c_str(), true);

    auto localizer =
        CreateLocalizer(lock_on_road_config, ::common::Transformation3());

    os::bag::Bag bag;
    bag.Open(bag_path, os::bag::BagMode::Read);
    os::bag::View view(bag, topics);

    MLOG(INFO) << "bag message size: " << view.Size();

    for (os::bag::MessageInstance const& m : *view.GetBagViewer()) {
      if (m.GetTopic() == kRtkTopic) {
        // "/sensors/gnss/pose";
        const auto ins_string = m.Instantiate<std_msgs::String>();
        CHECK(ins_string);
        drivers::gnss::SensorsIns sensor_ins;
        sensor_ins.ParseFromString(ins_string->data);
        auto sensor_ins_ptr = std::make_shared<drivers::gnss::SensorsIns>();
        *sensor_ins_ptr = sensor_ins;
        localizer->HandleRtkSensorInsMeasurementMsg(sensor_ins_ptr);
      } else if (m.GetTopic() == kGnssPositionTopic) {
        // '/sensors/gnss/raw_gnss_position',
        const auto gnss_string = m.Instantiate<std_msgs::String>();
        CHECK(gnss_string);
        deeproute::drivers::gnss::GnssPosition gnss_raw;
        gnss_raw.ParseFromString(gnss_string->data);
        auto gnss_raw_ptr =
            std::make_shared<deeproute::drivers::gnss::GnssPosition>();
        *gnss_raw_ptr = gnss_raw;
        localizer->HandleGnssDataMeasurementMsg(gnss_raw_ptr);
      } else if (m.GetTopic() == kRoutingResponse) {
        const auto routing_res = m.Instantiate<std_msgs::String>();
        CHECK(routing_res);
        deeproute::navinfo::SDRoutingResponse rr;
        rr.ParseFromString(routing_res->data);

        auto rr_ptr = std::make_shared<deeproute::navinfo::SDRoutingResponse>();
        *rr_ptr = rr;
        localizer->HandleGlobalRoutingMsg(rr_ptr);
      } else if (m.GetTopic() == kSdMapTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::sd_map::SDMapOnboard sdmap;
        sdmap.ParseFromString(data_str->data);
        auto sdmap_ptr = std::make_shared<deeproute::sd_map::SDMapOnboard>();
        *sdmap_ptr = sdmap;
        localizer->HandleSdMapOnboard(sdmap_ptr);
      } else if (m.GetTopic() == kRasMapNNTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::perception::NnFrame nn_frame;
        nn_frame.ParseFromString(data_str->data);
        auto nn_frame_ptr = std::make_shared<deeproute::perception::NnFrame>();
        *nn_frame_ptr = nn_frame;
        localizer->HandleRasMapNN(nn_frame_ptr);
      } else if (m.GetTopic() == kRasMapTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::perception::RASMap rasmap;
        rasmap.ParseFromString(data_str->data);
        auto nn_frame_ptr = std::make_shared<deeproute::perception::RASMap>();
        *nn_frame_ptr = rasmap;
        localizer->HandleRasMapMeasurementMsg(nn_frame_ptr);
      } else if (m.GetTopic() == kCameraTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::drivers::CompressedImage compressed_image;
        compressed_image.ParseFromString(data_str->data);

        cv::Mat cv_image;
        const bool ret = deeproute::common::ProtoCompressedToCvMat(
            compressed_image, cv_image);

        if (!ret) {
          MLOG(WARN) << "DDMM RUN ON BAGS: converting images failed.";
          continue;
        }

        const std::string image_name =
            camera_1_dir + "/" + std::to_string(m.GetTime().ToSec()) + ".png";
        cv::imwrite(image_name, cv_image);
      }
    }
  }
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  os::Node nh{"localization"};

  gflags::ParseCommandLineFlags(&argc, &argv, true);

  deeproute::localization::Process(FLAGS_in, FLAGS_out);

  return 0;
}
