#include <fstream>
#include <random>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>
#include <opencv2/opencv.hpp>

#include "lock_on_road/lock_on_road.pb.h"
#include "lock_on_road/lock_on_road_debug.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "common/log.h"
#include "lam_common/proto_utils.h"
#include "mm_localizer/ddmm_model.h"
#include "os_interface/os_interface.h"

DEFINE_string(bag_path, "", "");
DEFINE_string(out, "", "");

namespace deeproute {
namespace localization {
namespace {}  // namespace

void Process() {
  DdmmConfig config;
  config.set_enable_ddmm(true);
  config.set_enable_ddmm_debug_output(true);
#ifdef DR_NVIDIA
  auto ddmm = CreateDDMMModel(config);
#endif

  std::set<std::string> topics;
  const std::string kLockOnMapDebugTopic = "/localization/lock_on_map_debug";
  const std::string kLockOnRoadTopic = "/localization/lock_on_road";
  topics.emplace(kLockOnMapDebugTopic);
  topics.emplace(kLockOnRoadTopic);
  os::bag::Bag bag;
  bag.Open(FLAGS_bag_path, os::bag::BagMode::Read);
  os::bag::View view(bag, topics);
  unsigned message_idx = 0;
  const auto size = view.Size();
  MLOG(INFO) << "bag message size: " << view.Size();

  LockOnRoadResult lor_result;
  for (int i = 0; i < 1000; i++) {
    for (os::bag::MessageInstance const& m : *view.GetBagViewer()) {
      if (m.GetTopic() == kLockOnMapDebugTopic) {
        if (!lor_result.has_sd_link_id()) {
          continue;
        }

        MLOG(INFO) << "Processing message_idx: " << message_idx << "/" << size;
        const auto string = m.Instantiate<std_msgs::String>();
        CHECK(string);
        LockOnRoadDdmmDebugInfo debug_info;
        debug_info.ParseFromString(string->data);

        cv::Mat debug_image;
#ifdef DR_NVIDIA
        ddmm->DebugMatch(debug_info, lor_result, debug_image);
#endif

        const auto image_path =
            boost::filesystem::path(FLAGS_out)
                .append(std::to_string(debug_info.time()) + ".png")
                .string();
        cv::imwrite(image_path, debug_image);
      } else if (m.GetTopic() == kLockOnRoadTopic) {
        const auto string = m.Instantiate<std_msgs::String>();
        CHECK(string);
        lor_result.ParseFromString(string->data);
      }
      message_idx += 1;
    }
  }
}

void Process2() {
  DdmmConfig config;
  config.set_enable_ddmm(true);
  config.set_enable_ddmm_debug_output(true);
#ifdef DR_NVIDIA
  auto ddmm = CreateDDMMModel(config);
#endif
  std::random_device rd;
  std::mt19937 gen(rd());
  std::uniform_real_distribution<float> dis(0.0, 1.0);
  std::vector<float> random_vec;
  random_vec.reserve(230400);  // 预留空间以提高性能
  for (int i = 0; i < 230400; ++i) {
    float value = dis(gen);
    random_vec.push_back(value);
  }

  for (int i = 0; i < 10000; i++) {
#ifdef DR_NVIDIA
    ddmm->TestPerformance(random_vec);
#endif
  }
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  // deeproute::common::InitLogging("LOCALIZATION");
  os::Node nh{"localization"};

  gflags::ParseCommandLineFlags(&argc, &argv, true);

  // CHECK(!FLAGS_bag_path.empty());

  // if(!boost::filesystem::exists(FLAGS_out)){
  //   boost::filesystem::create_directory(FLAGS_out);
  // }

  deeproute::localization::Process2();

  return 0;
}
