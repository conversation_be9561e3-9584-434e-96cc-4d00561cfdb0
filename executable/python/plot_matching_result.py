import matplotlib.pyplot as plt
import shapefile as shp  # Requires the pyshp package
from shapely.wkt import loads
from shapely.geometry import Point, LineString


def plot(map_path, output_path, pt_match_file):

    file = open(pt_match_file).readlines()
    n = len(file)
    print("Total ", n, "points")

    xs = []
    ys = []
    mxs = []
    mys = []

    # line_idx = 0
    for line in file:
        
        line = line.split()
        xs.append(float(line[0]))
        ys.append(float(line[1]))
        mxs.append(float(line[2]))
        mys.append(float(line[3]))

    shapely_pts = []
    for i in range(len(xs)):
        shapely_pts.append(Point(xs[i], ys[i]))
    traj_line = LineString(shapely_pts)

    dpi = 96
    plt.figure(figsize=(1680/dpi, 1680/dpi), dpi=dpi)
    sf = shp.Reader(map_path)
    for shape in sf.shapeRecords():

        edge_linestring = LineString(shape.shape.points)
        if(edge_linestring.distance(traj_line) > 100):
            continue

        x = [i[0] for i in shape.shape.points[:]]
        y = [i[1] for i in shape.shape.points[:]]

        # edge_s = [x[0],  y[0]]
        # edge_e = [x[-1], y[-1]]

        # plot linestring start
        plt.plot(x[0],  y[0], 'ro')
        # plot linestring end
        plt.plot(x[-1], y[-1], 'ro')
        # plot linestring
        plt.plot(x, y, color='g')

    plt.scatter(xs, ys, color='r', alpha=0.5, s=2)
    plt.scatter(mxs, mys, color='b', alpha=0.5, s=2)

    colors = ["m", 'c', 'y']
    for i in range(len(mxs)):
        if i % 10 != 0:
            continue

        plt.plot([xs[i], mxs[i]], [ys[i], mys[i]], alpha=0.5, color=colors[0])
        front_color = colors.pop(0)
        colors.append(front_color)

    plt.savefig(output_path)

    import os
    os.remove(pt_match_file)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "map_path, output, point_file")
        sys.exit()

    bw = plot(sys.argv[1], sys.argv[2], sys.argv[3])
