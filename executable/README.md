# 如何离线基于bag生成期望的routing response?
## A. 使用本项目中的python脚本提取多个bags的combined poses
用法:

```
python3.8 extract_combined_poses_from_a_folder_of_bags INPUT_BAG_FOLDER OUTPUT_FILES_FOLDER
```

这个过陈会生成很多文件, 这里我们使用gnss_poses_lonlat.geojson这个文件, 这个文件的结果为GCJ02坐标系, 适用与阿里云DATAV可视化平台.

## B. 在阿里云DATAV WEB端上传gnss_poses_lonlat.geojson
![](./images/1.png)

上传后,点击图中按钮zoom in到轨迹区域.
![](./images/2.png)

结果如图所示:
![](./images/3.png)

## C. 根据底图以及轨迹, 选择最多13个轨迹点.
按1选择线特征, 沿着轨迹点几个点
![](./images/4.png)

1. 选择线特征选项卡
2. 选择对应线特征
3. 保存生成的geojson文件.
![](./images/5.png)

## D. 安装deeproute-lock-on-road-dev 以及map engine

1. 按照文档(https://rqk9rsooi4.feishu.cn/docx/doxcnL645G49pvPutZAgh2yGlzE)中的描述, 安装, 启动 map engine
2. 使用下面的命令生成routing response:
```
/opt/deeproute/lock_on_road/lib/lock_on_road/create_routing_request_and_save_routing_response_node --waypoints '/home/<USER>/Downloads/selected_geo_atlas_wps.geojson'  --out '/home/<USER>/Downloads/rr.geojson'
```
3. 将生成的rr.geojson上传到阿里云DATAV WEB端, 之后可见规划好的轨迹
![](./images/6.png)