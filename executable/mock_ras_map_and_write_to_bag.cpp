#include <boost/filesystem.hpp>
#include <boost/filesystem/path.hpp>
#include <pcl/common/common.h>
#include <pcl/common/transforms.h>
#include <std_msgs/String.h>

#include "external/common/proto_gen/drivers/gnss/ins.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/ground_truth.pb.h"

#include "common/file_util.h"
#include "common/geometry.h"
#include "common/log.h"
#include "common/road_map/hd_map_server_loading_utils.h"
#include "common/road_map/hd_map_spatial_lookup_server.h"
#include "common/road_map/hdmap_info.h"
#include "common/road_map/types.h"
#include "common/semantic_lmdb/semantic_map_conversion.h"
#include "common/time.h"
#include "localizer/semantic_map_loader.h"
#include "os_interface/os_interface.h"

DEFINE_string(bag, "", "");
DEFINE_string(out_bag, "", "");
DEFINE_string(semantic_map,
              "/opt/deeproute/onboard/maps/semantic-map/semantic-map.bin", "");
DEFINE_string(output, "", "");

namespace deeproute {
namespace localization {
namespace {

int GetLeftLaneNum(const deeproute::common::HDMapLaneInfoServer& map_server,
                   const deeproute::hdmap::Lane& lane,
                   std::vector<int32_t>* left_lane_ids) {
  if (lane.has_left_neighbor_forward_lane_id() &&
      lane.left_neighbor_forward_lane_id() != 0) {
    left_lane_ids->push_back(lane.left_neighbor_forward_lane_id());
    const deeproute::hdmap::Lane* left_lane =
        map_server.GetLaneById(lane.left_neighbor_forward_lane_id());
    CHECK(left_lane != nullptr) << "lane.DebugString(): " << lane.DebugString();
    return 1 + GetLeftLaneNum(map_server, *left_lane, left_lane_ids);
  }

  return 0;
}

int GetRightLaneNum(const deeproute::common::HDMapLaneInfoServer& map_server,
                    const deeproute::hdmap::Lane lane,
                    std::vector<int32_t>* right_lane_ids) {
  if (lane.has_right_neighbor_forward_lane_id() &&
      lane.right_neighbor_forward_lane_id() != 0) {
    right_lane_ids->push_back(lane.right_neighbor_forward_lane_id());
    const deeproute::hdmap::Lane* right_lane =
        map_server.GetLaneById(lane.right_neighbor_forward_lane_id());
    CHECK(right_lane != nullptr)
        << "lane id: " << lane.right_neighbor_forward_lane_id();
    return 1 + GetRightLaneNum(map_server, *right_lane, right_lane_ids);
  }

  return 0;
}

bool GetLaneNumSameDirection(
    const deeproute::common::HDMapLaneInfoServer& map_server,
    const deeproute::hdmap::Lane& lane, std::vector<int32_t>* left_lane_ids,
    std::vector<int32_t>* right_lane_ids, int* left_lane_num,
    int* right_lane_num) {
  *left_lane_num = GetLeftLaneNum(map_server, lane, left_lane_ids);
  *right_lane_num = GetRightLaneNum(map_server, lane, right_lane_ids);

  return true;
}

}  // namespace

void GetLaneIndexesFromBagPose(const std::string& bag_path,
                               const std::string& semantic_map,
                               const std::string& output_path) {
  CHECK(boost::filesystem::exists(bag_path)) << bag_path << " doesn't exist!";
  if (!boost::filesystem::exists(output_path)) {
    boost::filesystem::create_directory(output_path);
  }

  // save semantic map to txt format for inspection
  deeproute::hdmap::Map map;
  ::common::GetProtoFromFile(FLAGS_semantic_map, &map);
  const std::string map_output =
      boost::filesystem::path(FLAGS_output).append("semantic_map.cfg").string();
  deeproute::hdmap::Map lane_map;
  for (const auto& lane : map.lane()) {
    *lane_map.add_lane() = lane;
  }
  if (!boost::filesystem::exists(map_output)) {
    ::common::SetProtoToASCIIFile(lane_map, map_output);
  }

  // construct semantic lane map server
  const std::string lmdb_path =
      boost::filesystem::path(FLAGS_output).append("semantic_lmdb").string();
  deeproute::common::GenerateSemanticLmdbFromProto(FLAGS_semantic_map,
                                                   lmdb_path);
  auto map_server = deeproute::common::LoadSemanticMapServerFromWholeRegion<
      deeproute::common::HDMapLaneInfoServer>(lmdb_path);
  map_server->Initialize();

  // process bag
  const std::string kRasMap = "/perception/ras_map";
  const std::string kGnssPose = "/sensors/gnss/pose";
  const std::string kLocalizationPose = "/localization/pose";
  std::set<std::string> topics = {kLocalizationPose, kGnssPose};

  os::bag::Bag bag;
  bag.Open(bag_path, os::bag::BagMode::Read);
  os::bag::View view(bag, topics);

  os::bag::Bag out_bag;
  out_bag.Open(FLAGS_out_bag, os::bag::BagMode::Write);

  int valid_poses = 0;
  int invalid_poses = 0;
  for (os::bag::MessageInstance const& m : *view.GetBagViewer()) {
    if (m.GetTopic() == kLocalizationPose) {
      const auto ins_string = m.Instantiate<std_msgs::String>();
      CHECK(ins_string);
      deeproute::drivers::gnss::Ins ins;
      ins.ParseFromString(ins_string->data);

      deeproute::common::Id lane_id;
      ::common::Point2d p(ins.position().x(), ins.position().y());
      map_server->GetNearestLane(p, &lane_id);
      const auto lane = map_server->GetLaneById(lane_id);
      CHECK(lane != nullptr) << "Lane ptr null!";

      int left_lane_num, right_lane_num;
      std::vector<int32_t> left_lane_ids, right_lane_ids;
      GetLaneNumSameDirection(*map_server, *lane, &left_lane_ids,
                              &right_lane_ids, &left_lane_num, &right_lane_num);

      const deeproute::common::LaneInfo* lane_info =
          map_server->GetLaneInfoById(lane_id);

      if (lane_info) {
        if (!lane_info->IsOnLaneFast(p)) {
          invalid_poses += 1;
          continue;
        }
        valid_poses += 1;
      } else {
        invalid_poses += 1;
        continue;
      }

      deeproute::perception::RASMap ras_map;
      // ras_map.set_lane_index(left_lane_num);
      // ras_map.set_time_us(ins.measurement_time());
      // ras_map.set_lane_num(left_lane_num + right_lane_num + 1);
      for (int i = 0; i < left_lane_num; i++) {
        auto lane = ras_map.add_lanes();
        lane->set_id(i);
      }
      CHECK(ras_map.lanes_size() == left_lane_num);

      std_msgs::String ras_string;
      ras_string.data = ras_map.SerializeAsString();
      out_bag.Write(kRasMap, ::common::Time::ToOsTime(ins.measurement_time()),
                    ras_string);
    } else if (m.GetTopic() == kGnssPose) {
      const auto gnss_string = m.Instantiate<std_msgs::String>();
      CHECK(gnss_string);
      deeproute::drivers::gnss::SensorsIns gnss;
      gnss.ParseFromString(gnss_string->data);
      std_msgs::String string;
      string.data = gnss.SerializeAsString();
      out_bag.Write(kGnssPose,
                    ::common::Time::ToOsTime(gnss.measurement_time()), string);
    }
  }
  MLOG(INFO) << "Bag data processing finished!";
  MLOG(INFO) << "Valid poses: " << valid_poses;
  MLOG(INFO) << "Invalid poses: " << invalid_poses;
  bag.Close();
  out_bag.Close();
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);
  deeproute::localization::GetLaneIndexesFromBagPose(
      FLAGS_bag, FLAGS_semantic_map, FLAGS_output);

  return 0;
}
