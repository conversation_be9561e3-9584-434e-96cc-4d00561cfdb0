#include <dirent.h>
#include <sys/types.h>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>
#include <std_msgs/String.h>

#include "drivers/sensor_image.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "dpbag/bag_viewer/view.h"
#include "mm_localizer/ddmm_model.h"

DEFINE_string(bag_dir, "", "");
DEFINE_string(out, "", "");

namespace deeproute {
namespace localization {
namespace {}  // namespace

void Process() {
  DdmmConfig config;
  config.set_enable_ddmm(true);
  config.set_enable_ddmm_debug_output(true);
#ifdef DR_NVIDIA
  auto ddmm = CreateDDMMModel(config);
#endif

  std::set<std::string> topics;
  const std::string kLockOnMapDebugTopic = "/localization/lock_on_map_debug";
  const std::string kLockOnMapDebugImageTopic =
      "/localization/lock_on_map_debug_image";
  const std::string kLockOnRoadTopic = "/localization/lock_on_road";
  const std::string kLockOnMapTopic = "/localization/lock_on_map";
  const std::string kInsTopic = "/sensors/gnss/pose";

  topics.emplace(kLockOnMapDebugTopic);
  topics.emplace(kLockOnMapDebugImageTopic);
  topics.emplace(kLockOnRoadTopic);
  topics.emplace(kLockOnMapTopic);
  topics.emplace(kInsTopic);
  std::vector<std::string> files;

  MLOG(INFO) << "dir = " << FLAGS_bag_dir.c_str();

  // 打开特定文件夹
  DIR* dir = opendir(FLAGS_bag_dir.c_str());

  // 遍历特定文件夹下的所有文件
  struct dirent* entry;
  while ((entry = readdir(dir)) != NULL) {
    // 获取文件名
    std::string filename = entry->d_name;
    MLOG(INFO) << "filename = " << filename;

    // 如果文件名不是 "." 或 "..", 则将其添加到 vector 中
    if (filename != "." && filename != "..") {
      std::string path = FLAGS_bag_dir + "/" + filename;
      files.push_back(path);
      MLOG(INFO) << "filename = " << path;
    }
  }
  closedir(dir);

  deeproute::dpbag::bag::View view;

  for (const auto& bag_file : files) {
    MLOG(INFO) << "bag_file = " << bag_file;
    deeproute::dpbag::bag::DPBag bag(bag_file);
    view.AddQuery(bag, topics);
  }

  // os::bag::Bag bag;
  // bag.Open(FLAGS_bag_path, os::bag::BagMode::Read);
  // os::bag::View view(bag, topics);

  MLOG(INFO) << "debug 0";
  unsigned message_idx = 0;
  const auto size = view.Size();
  MLOG(INFO) << "bag message size: " << view.Size();
  std::unordered_map<uint64_t, LockOnRoadResult> onboard_lom_res;
  std::vector<uint64_t> onboard_time_list;
  std::unordered_map<uint64_t, LockOnRoadResult> offboard_lom_res;
  std::vector<uint64_t> offboard_time_list;
  int unfind_cnt = 0;
  int mismatch_cnt = 0;
  int lom_cnt = 0;
  int ddmm_hmm_mismatch_cnt = 0;

  MLOG(INFO) << "debug 1";

  // for (os::bag::MessageInstance const& m : *view.GetBagViewer()) {
  for (auto const& m : view) {
    LockOnRoadResult onboard_lor_result;
    LockOnRoadResult onboard_lom_result;

    if (m.GetTopic() == kLockOnMapDebugTopic) {
      // if (!onboard_lor_result.has_sd_link_id()) {
      //   continue;
      // }

      MLOG(INFO) << "Processing message_idx: " << message_idx << "/" << size;
      const auto string = m.Instantiate<std_msgs::String>();
      CHECK(string);
      LockOnRoadDdmmDebugInfo debug_info;
      debug_info.ParseFromString(string->data);

      cv::Mat debug_image;
      LockOnRoadResult lor_result;
#ifdef DR_NVIDIA
      ddmm->DebugMatch(debug_info, lor_result, debug_image);
#endif
      offboard_lom_res.insert(std::make_pair(lor_result.time_us(), lor_result));
      offboard_time_list.push_back(lor_result.time_us());
      MLOG(INFO) << "debug time = " << lor_result.time_us();
      const auto image_path =
          boost::filesystem::path(FLAGS_out)
              .append(std::to_string(debug_info.time()) + ".png")
              .string();
      cv::imwrite(image_path, debug_image);
    } else if (m.GetTopic() == kLockOnRoadTopic) {
      const auto string = m.Instantiate<std_msgs::String>();
      CHECK(string);
      onboard_lor_result.ParseFromString(string->data);
    } else if (m.GetTopic() == kLockOnMapTopic) {
      const auto string = m.Instantiate<std_msgs::String>();
      CHECK(string);
      onboard_lom_result.ParseFromString(string->data);
      onboard_lom_res.insert(
          std::make_pair(onboard_lom_result.time_us(), onboard_lom_result));
      onboard_time_list.push_back(onboard_lom_result.time_us());

      lom_cnt++;
      MLOG(INFO) << "online time = " << onboard_lom_result.time_us()
                 << ",link id = " << onboard_lom_result.sd_link_id()
                 << ",status = " << onboard_lom_result.status();
      if (onboard_lom_result.shadow_mode_result().lock_on_road_link_id() !=
          onboard_lom_result.shadow_mode_result().ddmm_link_id()) {
        MLOG(INFO)
            << "mismatch, lor id = "
            << onboard_lom_result.shadow_mode_result().lock_on_road_link_id()
            << ",lom id = "
            << onboard_lom_result.shadow_mode_result().ddmm_link_id();
        ddmm_hmm_mismatch_cnt++;
      }
    } else if (m.GetTopic() == kInsTopic) {
      ::deeproute::drivers::gnss::Ins ins;
      const auto string = m.Instantiate<std_msgs::String>();
      CHECK(string);
      ins.ParseFromString(string->data);
      // MLOG(INFO) << "ins " << ins.measurement_time() << ",type = " <<
      // ins.type()
      //            << ",linear_velocity_flu = " <<
      //            ins.linear_velocity_flu().x()
      //            << "," << ins.linear_velocity_flu().y() << ","
      //            << ins.linear_velocity_flu().z()
      //            << ",enu = " << ins.linear_velocity_enu().x() << ","
      //            << ins.linear_velocity_enu().y() << ","
      //            << ins.linear_velocity_enu().z()
      //            << "pos = " << ins.position_llh().lon() << ","
      //            << ins.position_llh().lat() << ","
      //            << ins.position_llh().height();

    } else if (m.GetTopic() == kLockOnMapDebugImageTopic) {
      ::deeproute::drivers::CompressedImage debug_image;
      const auto string = m.Instantiate<std_msgs::String>();
      CHECK(string);
      debug_image.ParseFromString(string->data);
      auto format = debug_image.format();
      auto data = debug_image.data();
      std::vector<char> data_array(data.begin(), data.end());
      if (data_array.size() > 0) {
        cv::Mat img_decode = cv::imdecode(data_array, cv::IMREAD_UNCHANGED);
        const auto debug_image_path =
            boost::filesystem::path(FLAGS_out)
                .append(std::to_string(debug_image.header().timestamp_sec()) +
                        "_online.png")
                .string();
        cv::imwrite(debug_image_path, img_decode);
      }
    }
    message_idx += 1;
  }

  std::unordered_map<uint64_t, uint64_t> off_on_pair_list;
  size_t th = 0;
  for (size_t i = 0; i < offboard_time_list.size() - 1; ++i) {
    for (size_t j = th; j < onboard_time_list.size(); ++j) {
      if (i < offboard_time_list.size() - 1) {
        if (onboard_time_list[j] >= offboard_time_list[i] &&
            onboard_time_list[j] < offboard_time_list[i + 1]) {
          off_on_pair_list.insert(
              std::make_pair(offboard_time_list[i], onboard_time_list[j]));
          th = j;
          break;
        }
      } else if (i == offboard_time_list.size() - 1) {
        if (onboard_time_list[j] >= offboard_time_list[i]) {
          off_on_pair_list.insert(
              std::make_pair(offboard_time_list[i], onboard_time_list[j]));
          th = j;
          break;
        }
      }
    }
  }

  MLOG(INFO) << "off_on_pair_list size = " << off_on_pair_list.size();

  for (const auto& off_on_pair : off_on_pair_list) {
    uint64_t off_time = off_on_pair.first;
    uint64_t on_time = off_on_pair.second;
    std::string off_link_id =
        offboard_lom_res.find(off_time)->second.sd_link_id();
    std::string on_link_id = onboard_lom_res.find(on_time)->second.sd_link_id();
    if (off_link_id != on_link_id) {
      MLOG(INFO) << "link id mismatch , online id = " << on_link_id
                 << ", off id = " << off_link_id << ",time = " << off_time;
      mismatch_cnt++;
    }
  }

  // // compare
  // for (const auto& off_lom : offboard_lom_res) {
  //   if (onboard_lom_res.find(off_lom.first) != onboard_lom_res.end()) {
  //     std::string off_link_id = off_lom.second.sd_link_id();
  //     std::string on_link_id =
  //     onboard_lom_res.at(off_lom.first).sd_link_id(); if (off_link_id !=
  //     on_link_id) {
  //       MLOG(INFO) << "link id mismatch , online id = " << on_link_id
  //                  << ", off id = " << off_link_id
  //                  << ",time = " << off_lom.first;
  //       mismatch_cnt++;
  //     }

  //   } else {
  //     unfind_cnt++;
  //   }
  // }
  MLOG(INFO) << "onboard_lom_res size = " << onboard_lom_res.size()
             << ",offline lom cnt = " << offboard_lom_res.size();
  MLOG(INFO) << "mismatch_cnt = " << mismatch_cnt
             << ", unfind cnt = " << unfind_cnt;

  MLOG(INFO) << "lom cnt = " << lom_cnt
             << ",hmm_ddmm_mismatch_cnt = " << ddmm_hmm_mismatch_cnt;
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  os::Node nh{"localization"};

  gflags::ParseCommandLineFlags(&argc, &argv, true);

  if (!boost::filesystem::exists(FLAGS_out)) {
    boost::filesystem::create_directory(FLAGS_out);
  }

  deeproute::localization::Process();

  return 0;
}
