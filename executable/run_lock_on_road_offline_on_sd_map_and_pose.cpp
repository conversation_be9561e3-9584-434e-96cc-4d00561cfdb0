#include <fstream>
#include <iostream>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>
#include <network/type.hpp>

#include "common/configs/vehicle_config.pb.h"
#include "drivers/gnss/gnss_raw.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "joint/proto/config_region.pb.h"
#include "lam_common/map_data/pose.pb.h"
#include "lam_common/projection.pb.h"
#include "lam_common/sd_map/sd_map_service.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "benchmark/benchmark.h"
#include "common/json.hpp"
#include "common/log.h"
#include "common/time_based_interpolation.h"
#include "data_adapter/ins_adapter.h"
#include "data_adapter/projection_transformation.h"
#include "joint/ll_utils.h"
#include "lam_common/proto_utils.h"
#include "lam_common/utm_projection_convert.h"
#include "localizer/localizer.h"
#include "mm_localizer/utils.h"
#include "os_interface/os_interface.h"

DEFINE_string(config_lock_on_road,
              "/opt/deeproute/map-engine/config/lock_on_road/"
              "config.cfg",
              "config_lock_on_road");
DEFINE_string(input_dir_path, "", "");
DEFINE_string(sd_map_path, "", "");
DEFINE_string(pose_path, "", "");
DEFINE_string(out_path, "", "");
DEFINE_string(out_root_path, "/opt/deeproute/tmp/", "");

namespace deeproute {
namespace localization {
namespace {

bool LoadPosesAndProjectionPoint(const std::string pose_path,
                                 std::vector<drivers::gnss::Ins>* poses,
                                 deeproute::proto::Projection* projection) {
  deeproute::proto::Trajectory traj;
  if (!deeproute::LoadProtoFile(pose_path, &traj)) {
    MLOG(FATAL) << "failed to pose at: " << pose_path;
    return false;
  }

  if (traj.poses_size() == 0) {
    MLOG(FATAL) << "trajectory is empty.";
    return false;
  }

  *projection = traj.meta_data().projection();

  drivers::gnss::Ins prev_ins;
  prev_ins.mutable_position()->set_x(0.0);
  prev_ins.mutable_position()->set_y(0.0);
  for (int i = 0; i < traj.poses_size(); ++i) {
    drivers::gnss::Ins ins;

    ins.set_measurement_time(traj.poses(i).time_us());

    ins.mutable_position()->set_x(traj.poses(i).position().x());
    ins.mutable_position()->set_y(traj.poses(i).position().y());
    ins.mutable_position()->set_z(traj.poses(i).position().z());

    Vector2d cur_position(traj.poses(i).position().x(),
                          traj.poses(i).position().y());
    Vector2d prev_position(prev_ins.position().x(), prev_ins.position().y());
    if (prev_position.norm() == 0.0) {
      ins.mutable_linear_velocity_flu()->set_x(0.0);
      ins.mutable_linear_velocity_flu()->set_y(0.0);
      ins.mutable_linear_velocity_flu()->set_z(0.0);
    } else {
      double dt =
          (traj.poses(i).time_us() - prev_ins.measurement_time()) / 1e-6;
      Vector2d velocity_flu = (cur_position - prev_position) / dt;
      ins.mutable_linear_velocity_flu()->set_x(velocity_flu.x());
      ins.mutable_linear_velocity_flu()->set_y(velocity_flu.y());
      ins.mutable_linear_velocity_flu()->set_z(0.0);
    }
    prev_ins = ins;

    ins.mutable_euler_angles()->set_x(traj.poses(i).roll_pitch_yaw().x());
    ins.mutable_euler_angles()->set_y(traj.poses(i).roll_pitch_yaw().y());
    ins.mutable_euler_angles()->set_z(traj.poses(i).roll_pitch_yaw().z());

    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().x() *
                                           traj.poses(i).position_std().x());
    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().y() *
                                           traj.poses(i).position_std().y());
    ins.mutable_position_covariance()->Add(traj.poses(i).position_std().z() *
                                           traj.poses(i).position_std().z());

    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().x() *
        traj.poses(i).euler_angle_std().x());
    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().y() *
        traj.poses(i).euler_angle_std().y());
    ins.mutable_euler_angles_covariance()->Add(
        traj.poses(i).euler_angle_std().z() *
        traj.poses(i).euler_angle_std().z());

    poses->push_back(ins);
  }

  MLOG(INFO) << "Loaded " << poses->size() << " poses.";

  return true;
}

bool LoadSdMap(const std::string sd_map_path,
               deeproute::sd_map::QueryLinksRep* sd_map) {
  if (!deeproute::LoadProtoFile(sd_map_path, sd_map)) {
    MLOG(FATAL) << "failed to load sd map at " << sd_map_path << ".";
    return false;
  }

  MLOG(INFO) << "Loaded sd map with " << sd_map->links_size() << " links.";
  return true;
}

google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH> Gcj02ToUtm(
    const google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH>&
        shape_points,
    const UtmProjectionConvert& utm_projector) {
  google::protobuf::RepeatedPtrField<::deeproute::common::PointLLH> utm_segment;
  for (const auto& ll : shape_points) {
    double wgs_lon;
    double wgs_lat;
    Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
    double x;
    double y;
    utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);

    auto shape_point = utm_segment.Add();
    shape_point->set_lon(x);
    shape_point->set_lat(y);
  }
  return utm_segment;
}

deeproute::navinfo::SDRoutingResponse ConvertLinksToSdRoutingResponse(
    const deeproute::sd_map::QueryLinksRep& links,
    const deeproute::proto::Projection& projection) {
  UtmProjectionConvert utm_projector(projection);
  deeproute::navinfo::SDRoutingResponse sd_routing_response;
  deeproute::navinfo::Result result;
  deeproute::navinfo::Route route;

  for (const auto& link : links.links()) {
    deeproute::navinfo::Segment segm;
    segm.set_ni_id(std::to_string(link.link_id()));

    segm.mutable_shape_points()->CopyFrom(link.points());
    segm.set_f_lane_num(link.forward_lane_num());
    segm.set_pr(link.priority());
    segm.set_tunnel(link.tunnel());
    segm.set_usage(link.usage());
    *route.add_segm() = segm;
  }
  result.mutable_route()->CopyFrom(route);
  *sd_routing_response.add_result() = result;
  sd_routing_response.set_request_id("1");

  return sd_routing_response;
}

Vector2d UtmToGcj02(const ProjectionTransformation& proj,
                    const Vector2d& utm_pose) {
  double lat, lon;
  proj.UtmToLatLon(utm_pose[0], utm_pose[1], &lat, &lon);

  double gcj_lon, gcj_lat;
  Wgs84ToGcj02(lon, lat, &gcj_lon, &gcj_lat);
  return Vector2d(gcj_lat, gcj_lon);
}

void RunLockOnRoadOnPosesAndMap(
    const std::vector<drivers::gnss::Ins>& poses,
    const deeproute::proto::Projection& projection,
    const deeproute::navinfo::SDRoutingResponse& sd_map) {
  deeproute::localization::LockOnRoadConfig config_lock_on_road;
  if (!deeproute::LoadProtoFile(FLAGS_config_lock_on_road,
                                &config_lock_on_road)) {
    MLOG(FATAL) << "failed to load config lock on road.";
  }

  const int kPoseNum = poses.size();
  config_lock_on_road.mutable_map_matching_config()->set_history_length(
      kPoseNum);
  MLOG(INFO) << "pose num: " << kPoseNum;

  auto localizer = CreateLocalizer(config_lock_on_road, projection);

  ProjectionTransformation utm_proj(projection);

  auto sd_map_ptr = std::make_shared<deeproute::navinfo::SDRoutingResponse>();
  *sd_map_ptr = sd_map;
  localizer->HandleGlobalRoutingMsg(sd_map_ptr);

  if (!FLAGS_out_root_path.empty() &&
      !boost::filesystem::exists(FLAGS_out_root_path)) {
    boost::filesystem::create_directory(FLAGS_out_root_path);
  }
  const std::string out_pose_path = boost::filesystem::path(FLAGS_out_root_path)
                                        .append("pose_pts.txt")
                                        .string();
  const std::string out_matched_path =
      boost::filesystem::path(FLAGS_out_root_path)
          .append("matched_pts.txt")
          .string();
  const std::string pose_to_matched_path =
      boost::filesystem::path(FLAGS_out_root_path)
          .append("pose_to_matched.txt")
          .string();
  const std::string utm_links = boost::filesystem::path(FLAGS_out_root_path)
                                    .append("utm_links.txt")
                                    .string();
  // create file to write poses
  std::ofstream pose_file;
  pose_file.open(out_pose_path);
  std::ofstream matched_file;
  matched_file.open(out_matched_path);
  std::ofstream pose_to_matched;
  pose_to_matched.open(pose_to_matched_path);
  std::ofstream utm_links_file;
  utm_links_file.open(utm_links);

  const auto custom_graph = localizer->GetRoutingResponseGraph();
  for (const auto utm_link : custom_graph) {
    utm_links_file << utm_link.id << " ";

    for (int i = 0; i < utm_link.geom.get_num_points(); i++) {
      const double x = utm_link.geom.get_x(i);
      const double y = utm_link.geom.get_y(i);
      utm_links_file << x << " " << y << " ";
    }

    utm_links_file << "\n";
  }
  utm_links_file.close();

  deeproute::proto::Trajectory result_trajectory;
  for (size_t idx = 0; idx < poses.size(); idx++) {
    const auto pose = poses[idx];
    auto ins_ptr = std::make_shared<drivers::gnss::Ins>();
    *ins_ptr = pose;
    localizer->HandleRtkInsMeasurementMsg(ins_ptr);

    const Vector2d adc_latlon = UtmToGcj02(
        utm_proj, Vector2d(ins_ptr->position().x(), ins_ptr->position().y()));
    pose_file << std::to_string(adc_latlon[0]) + " " +
                     std::to_string(adc_latlon[1]) + " " +
                     std::to_string(ins_ptr->position().z()) + "\n";

    if (!localizer->SystemReady()) {
      continue;
    }

    const auto& debug_infos = localizer->GetLatestLockOnLaneDebugInfo();
    const auto& lock_on_road_result = localizer->GetLatestLockOnRoadResult();

    MLOG(INFO) << lock_on_road_result.ShortDebugString();

    MLOG(INFO) << "Debug info size: " << debug_infos.pose_debug_info_size();

    if (lock_on_road_result.status() !=
        deeproute::localization::LockOnRoadResult_Status_NORMAL) {
      MLOG(INFO) << "lock on road failed on current pose at: "
                 << ins_ptr->measurement_time();
      continue;
    } else {
      const auto debug_info_size = debug_infos.pose_debug_info_size();
      for (size_t idx = 0; idx < debug_info_size; idx++) {
        const auto lor_debug_info = debug_infos.pose_debug_info()[idx];

        const auto world_to_matched_pt = ::common::Transformation3(
            lor_debug_info.matched_position().x(),
            lor_debug_info.matched_position().y(),
            lor_debug_info.position().z(), lor_debug_info.euler_angles().x(),
            lor_debug_info.euler_angles().y(),
            lor_debug_info.euler_angles().z());

        pose_to_matched
            << std::to_string(lor_debug_info.position().x()) + " " +
                   std::to_string(lor_debug_info.position().y()) + " " +
                   std::to_string(lor_debug_info.matched_position().x()) + " " +
                   std::to_string(lor_debug_info.matched_position().y()) + "\n";

        const auto world_to_matched_pt_position =
            world_to_matched_pt.GetTranslation();
        const auto world_to_matched_pt_euler_angles =
            world_to_matched_pt.GetRollPitchYaw();

        deeproute::proto::Pose result_pose;
        result_pose.set_time_us(lor_debug_info.time());
        // result_pose.set_lock_on_road_status(lor_debug_info.status());
        result_pose.set_lock_on_road_status(
            deeproute::localization::LockOnRoadResult_Status_NORMAL);
        result_pose.set_sd_link_id(lor_debug_info.debug_sd_link_id());

        result_pose.mutable_position()->set_x(world_to_matched_pt_position.x());
        result_pose.mutable_position()->set_y(world_to_matched_pt_position.y());
        result_pose.mutable_position()->set_z(world_to_matched_pt_position.z());
        result_pose.mutable_roll_pitch_yaw()->set_x(
            world_to_matched_pt_euler_angles.x());
        result_pose.mutable_roll_pitch_yaw()->set_y(
            world_to_matched_pt_euler_angles.y());
        result_pose.mutable_roll_pitch_yaw()->set_z(
            world_to_matched_pt_euler_angles.z());

        // std related.
        result_pose.mutable_position_std()->set_x(
            std::sqrt(ins_ptr->position_covariance()[0]));
        result_pose.mutable_position_std()->set_y(
            std::sqrt(ins_ptr->position_covariance()[1]));
        result_pose.mutable_position_std()->set_z(
            std::sqrt(ins_ptr->position_covariance()[2]));
        result_pose.mutable_euler_angle_std()->set_x(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));
        result_pose.mutable_euler_angle_std()->set_y(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));
        result_pose.mutable_euler_angle_std()->set_z(
            std::sqrt(ins_ptr->euler_angles_covariance()[0]));

        const Vector2d matched_latlon = UtmToGcj02(
            utm_proj, Vector2d(lor_debug_info.matched_position().x(),
                               lor_debug_info.matched_position().y()));

        matched_file << std::to_string(matched_latlon[0]) + " " +
                            std::to_string(matched_latlon[1]) + " " +
                            std::to_string(world_to_matched_pt_position.z()) +
                            "\n";

        *result_trajectory.add_poses() = result_pose;
      }
    }
  }

  // save result to output path
  if (!WriteProtoFile(FLAGS_out_path, result_trajectory)) {
    MLOG(FATAL) << "writting resul to " << FLAGS_out_path << " failed.";
  } else {
    MLOG(INFO) << "writting resul to " << FLAGS_out_path << " success.";
  }

  const std::string rr_path = boost::filesystem::path(FLAGS_out_root_path)
                                  .append("routing_response.txt")
                                  .string();
  WriteProtoFile(rr_path, *sd_map_ptr);

  pose_file.close();
  matched_file.close();
  pose_to_matched.close();
}

void Process() {
  std::vector<drivers::gnss::Ins> poses;
  deeproute::proto::Projection projection;
  if (!LoadPosesAndProjectionPoint(FLAGS_pose_path, &poses, &projection)) {
    MLOG(FATAL) << "failed to load poses and projection point.";
    return;
  }

  deeproute::sd_map::QueryLinksRep sd_map;
  LoadSdMap(FLAGS_sd_map_path, &sd_map);
  deeproute::navinfo::SDRoutingResponse sd_routing_response =
      ConvertLinksToSdRoutingResponse(sd_map, projection);

  RunLockOnRoadOnPosesAndMap(poses, projection, sd_routing_response);
}

}  // namespace
}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  // os::Node nh{"localization"};
  // google::InitGoogleLogging(argv[0]);
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  if (FLAGS_input_dir_path.empty()) {
    CHECK(!FLAGS_out_path.empty()) << "out path is empty.";
    CHECK(!FLAGS_pose_path.empty()) << "pose path is empty.";
    CHECK(!FLAGS_sd_map_path.empty()) << "sd map path is empty.";
  } else {
    std::string temp_root_path = FLAGS_input_dir_path;
    FLAGS_pose_path =
        boost::filesystem::path(temp_root_path).append("hd_pose.txt").string();
    temp_root_path = FLAGS_input_dir_path;
    FLAGS_sd_map_path =
        boost::filesystem::path(temp_root_path).append("sdmap.txt").string();
    temp_root_path = FLAGS_input_dir_path;
    FLAGS_out_root_path =
        boost::filesystem::path(temp_root_path).append("result").string();
    temp_root_path = FLAGS_input_dir_path;
    FLAGS_out_path = boost::filesystem::path(temp_root_path)
                         .append("result")
                         .append("result_pose.txt")
                         .string();
    if (!boost::filesystem::exists(FLAGS_out_root_path)) {
      boost::filesystem::create_directories(FLAGS_out_root_path);
      MLOG(INFO) << "You provided with a data input dir, so";
      MLOG(INFO) << "HD pose: " << FLAGS_pose_path;
      MLOG(INFO) << "SD map: " << FLAGS_sd_map_path;
      MLOG(INFO) << "Result output: " << FLAGS_out_path;
      MLOG(INFO) << "Result output root: " << FLAGS_out_root_path;
    }
  }

  // calculate time used for entire process.
  const auto start_time = std::chrono::system_clock::now();
  deeproute::localization::Process();
  const auto end_time = std::chrono::system_clock::now();
  MLOG(INFO) << "total time used: "
             << std::chrono::duration_cast<std::chrono::seconds>(end_time -
                                                                 start_time)
                    .count()
             << " s.";

  return 0;
}
