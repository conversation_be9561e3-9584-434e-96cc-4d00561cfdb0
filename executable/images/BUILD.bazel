package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")
load("@deeproute_build_tools//rules/pkg:deeproute_release.bzl", "deeproute_release_package")

filegroup(
    name = "configs",
    srcs = [
        "1.png",
        "2.png",
        "3.png",
        "4.png",
        "5.png",
        "6.png",
    ]
)

deeproute_release_package(
    name = "package",
    srcs = [ ":configs" ],
    mode = "0755",
    package_dir = "images",
)
