#include <stdlib.h>

#include <fstream>
#include <unordered_map>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>
#include <glog/logging.h>
#include <network/type.hpp>

#include "common/configs/vehicle_config.pb.h"
#include "drivers/gnss/gnss_raw.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "joint/proto/config_region.pb.h"
#include "lam_common/projection.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "map/sd_map.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "proto/lock_on_road_config.pb.h"

#include "common/conversions/cvmat_conversions.h"
#include "common/json.hpp"
#include "common/log.h"
#include "common/types.h"
#include "data_adapter/ins_adapter.h"
#include "joint/ll_utils.h"
#include "lam_common/proto_adapter/blc_adapter.h"
#include "lam_common/proto_utils.h"
#include "lam_common/utm_projection_convert.h"
#include "localizer/localizer.h"
#include "mm_localizer/utils.h"
#include "os_interface/os_interface.h"

using namespace nlohmann;

DEFINE_string(config_lock_on_road,
              "/opt/deeproute/map-engine/config/lock_on_road/"
              "lor_config.cfg",
              "config_lock_on_road");
DEFINE_string(in, "/tmp/11824275_light", "");
DEFINE_string(out, "", "");
DEFINE_bool(online_query_sdmap, false, "");
DEFINE_bool(save_img, false, "");

const std::string kDefaultRegionInfoPath =
    "/opt/deeproute/map-engine/config/lock_on_road/"
    "regions.cfg";

namespace deeproute {
namespace localization {
namespace {

void ConvertAmapRouteToGeoJSON(
    const std::unordered_map<int64, AmapStatus>& time_to_amap_status,
    const std::string& output_file) {
  if (!time_to_amap_status.empty()) {
    AmapStatus sample_amap;
    // Selected the 1st valid amap status
    for (const auto& [amap_time, amap_status] : time_to_amap_status) {
      if (amap_status.valid) {
        sample_amap = amap_status;
        break;
      }
    }

    auto amap_link_id_to_amap_step_gcj02 =
        sample_amap.amap_link_id_to_amap_step_gcj02;

    json feature_collection = {{"type", "FeatureCollection"},
                               {"features", json::array()}};

    for (const auto& [amap_step_id, amap_step_gcj02] :
         amap_link_id_to_amap_step_gcj02) {
      // MLOG(INFO) << "ConvertAmapRouteToGeoJSON: step id: " << amap_step_id;
      for (const auto& amap_link : amap_step_gcj02.links()) {
        // MLOG(INFO) << "ConvertAmapRouteToGeoJSON: link id: " <<
        // amap_link.id();

        std::vector<std::vector<double>> coordinates;
        for (auto i = 0; i < amap_link.polyline_llh_size(); i++) {
          auto ll = amap_link.polyline_llh(i);
          coordinates.push_back({ll.lon(), ll.lat()});
        }

        json feature = {
            {"type", "Feature"},
            {"geometry",
             {{"type", "LineString"}, {"coordinates", coordinates}}},
            {"properties",
             {{"amap_step_id", amap_step_gcj02.id()},
              {"amap_link_id", amap_link.id()}}}};

        feature_collection["features"].push_back(feature);
      }
    }

    // Save the GeoJSON to a file
    std::ofstream output_stream(output_file);
    output_stream << feature_collection.dump(2) << std::endl;
    output_stream.close();
  }
}

}  // namespace

void Process(const std::string& bag_dir, const std::string& out_dir) {
  MLOG(INFO) << "bag_dir: " << bag_dir;
  MLOG(INFO) << "out_dir: " << out_dir;

  // if (boost::filesystem::exists(out_dir)) {
  //   boost::filesystem::remove_all(out_dir);
  // }
  boost::filesystem::create_directories(out_dir);

  const std::string camera_1_dir = out_dir + "/camera_1";
  if (!boost::filesystem::exists(camera_1_dir)) {
    boost::filesystem::create_directories(camera_1_dir);
  }

  // if path not found, create one
  const std::string routing_mask_dir = out_dir + "/routing_mask_yaw_debug";
  if (!boost::filesystem::exists(routing_mask_dir)) {
    boost::filesystem::create_directories(routing_mask_dir);
  }

  setenv("DDMM_DEBUG", "1", true);
  setenv("DDMM_DEBUG_DIR", out_dir.c_str(), true);
  setenv("DDMM_ONLINE_QUERY_SDMAP",
         std::to_string(FLAGS_online_query_sdmap).c_str(), true);

  deeproute::localization::LockOnRoadConfig lock_on_road_config;
  if (!deeproute::LoadProtoFile(FLAGS_config_lock_on_road,
                                &lock_on_road_config)) {
    MLOG(FATAL) << "failed to load config: " << FLAGS_config_lock_on_road;
  }
  lock_on_road_config.mutable_ddmm_config()->set_enable_ddmm(false);

  auto localizer =
      CreateLocalizer(lock_on_road_config, ::common::Transformation3());

  std::set<std::string> topics;
  // const std::string kGnssTopic = "/sensors/gnss/gnss";
  const std::string kRtkTopic = "/sensors/gnss/pose";
  const std::string kOdomTopic = "/localization/pose";
  const std::string kGnssPositionTopic = "/sensors/gnss/raw_gnss_position";
  const std::string kRoutingResponse = "/map/routing/internal/response";
  const std::string kRasMapNNTopic = "/perception/ras_map_nn";
  const std::string kRasMapTopic = "/perception/ras_map";
  const std::string kSdMapTopic = "/map/sd_map";
  const std::string kHorizonMapTopic = "/map/sd_horizon_map";
  const std::string kCameraTopic =
      "/sensors/camera/camera_1_raw_data/compressed_proto";
  const std::string kAmapStatusTopic = "/gwm/blc/amap_navigation";

  // topics.emplace(kGnssTopic);
  topics.emplace(kRtkTopic);
  topics.emplace(kOdomTopic);
  topics.emplace(kGnssPositionTopic);
  topics.emplace(kRoutingResponse);
  topics.emplace(kSdMapTopic);
  topics.emplace(kHorizonMapTopic);
  topics.emplace(kRasMapNNTopic);
  topics.emplace(kRasMapTopic);
  topics.emplace(kAmapStatusTopic);
  if (FLAGS_save_img) {
    topics.emplace(kCameraTopic);
  }

  std::vector<std::string> all_dir_files;
  boost::filesystem::path bag_dir_path(bag_dir);
  boost::filesystem::directory_iterator end_itr;
  for (boost::filesystem::directory_iterator itr(bag_dir_path); itr != end_itr;
       ++itr) {
    all_dir_files.push_back(itr->path().string());
  }
  std::sort(all_dir_files.begin(), all_dir_files.end());

  std::unordered_map<int64, drivers::gnss::SensorsIns> time_to_gnss_pose;
  std::unordered_map<int64, Vector3d> time_to_lor_input_pose;
  std::vector<LockOnRoadResult> lor_results;
  std::unordered_map<int64, AmapStatus> time_to_amap_status;

  for (const auto file_path : all_dir_files) {
    if (boost::filesystem::path(file_path).extension() != ".bag") {
      continue;
    }
    const auto bag_path = file_path;
    MLOG(INFO) << "Bag path :" << bag_path;

    os::bag::Bag bag;
    bag.Open(bag_path, os::bag::BagMode::Read);
    os::bag::View view(bag, topics);

    MLOG(INFO) << "bag message size: " << view.Size();

    for (os::bag::MessageInstance const& m : *view.GetBagViewer()) {
      if (m.GetTopic() == kRtkTopic) {
        // "/sensors/gnss/pose";
        const auto ins_string = m.Instantiate<std_msgs::String>();
        CHECK(ins_string);
        drivers::gnss::SensorsIns sensor_ins;
        sensor_ins.ParseFromString(ins_string->data);
        auto sensor_ins_ptr = std::make_shared<drivers::gnss::SensorsIns>();
        *sensor_ins_ptr = sensor_ins;
        localizer->HandleRtkSensorInsMeasurementMsg(sensor_ins_ptr);

        // get LOR result
        const auto lor_result = localizer->GetLatestLockOnRoadResult();

        CHECK(lor_result.navi_status_internal_size() == 1);
        CHECK(lor_result.status() ==
              lor_result.navi_status_internal(0).status());
        CHECK(lor_result.navi_status_internal(0).source() ==
              LockOnRoadResult_NaviStatus_Source_LOCK_ON_ROAD);

        // MLOG(INFO) << "lor_result: "<<lor_result.ShortDebugString();

        lor_results.push_back(lor_result);

        time_to_gnss_pose[sensor_ins.measurement_time()] = sensor_ins;

        // get AMAP result
        const auto amap_status = localizer->GetLatestAmapStatus();
        time_to_amap_status[amap_status.time_us] = amap_status;

        // get LOR input pose
        time_to_lor_input_pose[sensor_ins.measurement_time()] =
            localizer->GetLorInputPoseBlhGcj02();

      } else if (m.GetTopic() == kGnssPositionTopic) {
        // '/sensors/gnss/raw_gnss_position',
        const auto gnss_string = m.Instantiate<std_msgs::String>();
        CHECK(gnss_string);
        deeproute::drivers::gnss::GnssPosition gnss_raw;
        gnss_raw.ParseFromString(gnss_string->data);
        auto gnss_raw_ptr =
            std::make_shared<deeproute::drivers::gnss::GnssPosition>();
        *gnss_raw_ptr = gnss_raw;
        localizer->HandleGnssDataMeasurementMsg(gnss_raw_ptr);
      } else if (m.GetTopic() == kOdomTopic) {
        const auto odom_string = m.Instantiate<std_msgs::String>();
        drivers::gnss::Ins odom;
        odom.ParseFromString(odom_string->data);
        auto odom_ptr = std::make_shared<drivers::gnss::Ins>();
        *odom_ptr = odom;
        localizer->HandleOdometryMeasurementMsg(odom_ptr);
      } else if (m.GetTopic() == kRoutingResponse) {
        const auto routing_res = m.Instantiate<std_msgs::String>();
        CHECK(routing_res);
        deeproute::navinfo::SDRoutingResponse rr;
        rr.ParseFromString(routing_res->data);

        auto rr_ptr = std::make_shared<deeproute::navinfo::SDRoutingResponse>();
        *rr_ptr = rr;
        localizer->HandleGlobalRoutingMsg(rr_ptr);
      } else if (m.GetTopic() == kSdMapTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::sd_map::SDMapOnboard sdmap;
        sdmap.ParseFromString(data_str->data);
        auto sdmap_ptr = std::make_shared<deeproute::sd_map::SDMapOnboard>();
        *sdmap_ptr = sdmap;
        localizer->HandleSdMapOnboard(sdmap_ptr);
      } else if (m.GetTopic() == kRasMapNNTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::perception::NnFrame nn_frame;
        nn_frame.ParseFromString(data_str->data);
        auto nn_frame_ptr = std::make_shared<deeproute::perception::NnFrame>();
        *nn_frame_ptr = nn_frame;
        localizer->HandleRasMapNN(nn_frame_ptr);
      } else if (m.GetTopic() == kRasMapTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::perception::RASMap rasmap;
        rasmap.ParseFromString(data_str->data);
        auto nn_frame_ptr = std::make_shared<deeproute::perception::RASMap>();
        *nn_frame_ptr = rasmap;
        localizer->HandleRasMapMeasurementMsg(nn_frame_ptr);
      } else if (m.GetTopic() == kAmapStatusTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        map_onboard_adapter::RealTimeAmap amap_data;
        amap_data.ParseFromString(data_str->data);
        auto amap_data_ptr =
            std::make_shared<map_onboard_adapter::RealTimeAmap>();
        *amap_data_ptr = amap_data;

        localizer->HandleAmapMsg(amap_data_ptr);

      } else if (m.GetTopic() == kCameraTopic) {
        const auto data_str = m.Instantiate<std_msgs::String>();
        deeproute::drivers::CompressedImage compressed_image;
        compressed_image.ParseFromString(data_str->data);

        cv::Mat cv_image;
        const bool ret = deeproute::common::ProtoCompressedToCvMat(
            compressed_image, cv_image);
        MLOG(INFO) << "DDMM RUN ON CASE: converting images. " << ret;

        if (!ret) {
          MLOG(INFO) << "DDMM RUN ON CASE: converting images failed.";
          continue;
        }

        const std::string image_name =
            camera_1_dir + "/" + std::to_string(m.GetTime().ToSec()) + ".png";
        cv::imwrite(image_name, cv_image);
      }
    }
  }

  // return;

  MLOG(INFO) << "lor_results size: " << lor_results.size();

  // convert matched lor results to geojson
  const auto out_path = out_dir + "/matching_result.json";
  std::ofstream matching_file(out_path);

  nlohmann::json geojson;
  geojson["type"] = "FeatureCollection";
  nlohmann::json features;

  // also write lor to a txt file
  const auto out_path_txt = out_dir + "/matching_result.txt";
  std::ofstream matching_file_txt(out_path_txt);
  int lor_input_idx = 0;
  for (const LockOnRoadResult& lor : lor_results) {
    lor_input_idx += 1;
    matching_file_txt << lor.DebugString() << std::endl;
    matching_file_txt << "-----------------------------------" << std::endl;

    const auto sensors_ins = time_to_gnss_pose[lor.time_us()];
    common::PointLLH llh;
    if (sensors_ins.has_imu_frame_position_llh()) {
      llh.set_lon(sensors_ins.imu_frame_position_llh().lon());
      llh.set_lat(sensors_ins.imu_frame_position_llh().lat());
      llh.set_height(sensors_ins.imu_frame_position_llh().height());
    } else if (sensors_ins.has_vehicle_frame_position_llh()) {
      llh.set_lon(sensors_ins.vehicle_frame_position_llh().lon());
      llh.set_lat(sensors_ins.vehicle_frame_position_llh().lat());
      llh.set_height(sensors_ins.vehicle_frame_position_llh().height());
    }

    Vector2d pose_latlon_gcj02;
    using RefSystem = drivers::gnss::SensorsIns::ReferenceCoordinateSystem;
    if (sensors_ins.reference_coodinate_system() ==
        RefSystem::SensorsIns_ReferenceCoordinateSystem_GCJ02) {
      pose_latlon_gcj02.x() = llh.lat();
      pose_latlon_gcj02.y() = llh.lon();
    } else {
      double gcj_lat, gcj_lon;
      Wgs84ToGcj02(llh.lon(), llh.lat(), &gcj_lon, &gcj_lat);
      pose_latlon_gcj02.x() = gcj_lat;
      pose_latlon_gcj02.y() = gcj_lon;
    }

    // const Vector2d pose_latlon =
    // time_to_lor_input_pose[lor.time_us()].head(2); const Vector2d pose_latlon
    // = pose_latlon_gcj02;
    const Vector2d matched_latlon = Vector2d(lor.matched_position_gcj02().y(),
                                             lor.matched_position_gcj02().x());

    // nlohmann::json feature;
    // feature["type"] = "Feature";
    // nlohmann::json geometry;
    // geometry["type"] = "LineString";
    // nlohmann::json coordinates;
    // coordinates.push_back({pose_latlon.y(), pose_latlon.x()});
    // coordinates.push_back({matched_latlon.y(), matched_latlon.x()});

    // geometry["coordinates"] = coordinates;
    // feature["geometry"] = geometry;
    // feature["properties"]["time"] = lor.time_us();
    // feature["properties"]["status"] = lor.status();
    // feature["properties"]["type"] = "LOR";
    // feature["properties"]["idx"] = idx;
    nlohmann::json feature;
    feature["type"] = "Feature";
    nlohmann::json geometry;
    geometry["type"] = "Point";
    nlohmann::json coordinates;
    // coordinates.push_back(pose_latlon.y());
    // coordinates.push_back(pose_latlon.x());

    coordinates.push_back(matched_latlon.y());
    coordinates.push_back(matched_latlon.x());

    geometry["coordinates"] = coordinates;
    feature["geometry"] = geometry;
    feature["properties"]["time"] = lor.time_us();
    feature["properties"]["status"] = lor.status();
    feature["properties"]["type"] = "LOR";
    feature["properties"]["idx"] = lor_input_idx;

    if (lor.has_route_seg_idx()) {
      feature["properties"]["route_seg_idx"] = lor.route_seg_idx();
    } else {
      feature["properties"]["route_seg_idx"] = -1;
    }

    features.push_back(feature);

    // write amap status to features
    // const auto amap_status = time_to_amap_status[lor.time_us()];

    // find closest amap status to the pose time
    double min_time_diff = std::numeric_limits<double>::max();
    AmapStatus closest_amap_data;
    for (const auto& [amap_time, amap_status] : time_to_amap_status) {
      double time_diff = std::abs(amap_time - lor.time_us());
      if (time_diff < min_time_diff) {
        min_time_diff = time_diff;
        closest_amap_data = amap_status;
      }
    }
    if (min_time_diff > 1e6) {
      // MLOG(INFO) << "Invalid AMAP AND GNSS min_time_diff :" << min_time_diff;
      continue;
    }
    // else {
    // MLOG(INFO) << "Valid AMAP AND GNSS min_time_diff :" << min_time_diff;
    // }
    auto amap_status = closest_amap_data;
    if (!amap_status.valid) {
      continue;
    }

    nlohmann::json amap_feature;
    amap_feature["type"] = "Feature";
    nlohmann::json amap_geometry;
    amap_geometry["type"] = "LineString";
    nlohmann::json amap_coordinates;
    amap_coordinates.push_back({amap_status.lon_gcj02, amap_status.lat_gcj02});
    amap_coordinates.push_back(
        {amap_status.matched_lon_gcj02, amap_status.matched_lat_gcj02});
    amap_geometry["coordinates"] = amap_coordinates;
    amap_feature["geometry"] = amap_geometry;
    amap_feature["properties"]["type"] = "AMAP";

    features.push_back(amap_feature);
  }
  matching_file_txt.close();

  geojson["features"] = features;

  // convert QueryLinksRep to Geojson and write it to a file
  if (matching_file.is_open()) {
    matching_file << geojson.dump(
        4);  // Write JSON with indentation of 4 spaces
    matching_file.close();
    MLOG(INFO) << "Geojson data has been written to the file.";
  } else {
    MLOG(INFO) << "Unable to open the file for writing.";
  }

  // save amap route
  // convert amap route to geojson and save it to a file
  const auto amap_out_path = out_dir + "/amap_route.geojson";
  ConvertAmapRouteToGeoJSON(time_to_amap_status, amap_out_path);

  // print LOR status set
  std::set<int> lor_status_set;
  std::unordered_map<int, int> lor_status_to_num;
  for (const auto& lor : lor_results) {
    // MLOG(INFO) << "LOR status: " << lor.status();
    lor_status_set.insert(lor.status());

    if (lor_status_to_num.find(lor.status()) != lor_status_to_num.end()) {
      lor_status_to_num[lor.status()] += 1;
    } else {
      lor_status_to_num[lor.status()] = 0;
    }
  }
  MLOG(INFO) << "---------------lor STATUS summary-------------";
  for (const auto& status : lor_status_set) {
    MLOG(INFO) << "LOR status: " << status
               << ", appear num: " << lor_status_to_num[status];
  }
  MLOG(INFO) << "-----------------------------------------------";

  // convert images to a video, image path: routing_mask_dir

  const std::string video_path = routing_mask_dir + "/camera_1.mp4";

  // Check if the input directory exists
  if (!boost::filesystem::exists(routing_mask_dir)) {
    MLOG(ERROR) << "Input directory does not exist: " << routing_mask_dir;
    throw std::runtime_error("Input directory does not exist: " +
                             routing_mask_dir);
  }

  // Check if there are any PNG files in the input directory
  bool png_files_exist = false;
  for (const auto& entry :
       boost::filesystem::directory_iterator(routing_mask_dir)) {
    if (entry.path().extension() == ".png") {
      png_files_exist = true;
      break;
    }
  }

  if (!png_files_exist) {
    MLOG(ERROR) << "No PNG files found in the input directory: "
                << routing_mask_dir;
    throw std::runtime_error("No PNG files found in the input directory: " +
                             routing_mask_dir);
  }

  // Construct the ffmpeg command
  // const std::string cmd = "ffmpeg -r 10 -i " + routing_mask_dir +
  //                         "/%d.png -vcodec mpeg4 -y " + video_path;
  const std::string cmd =
      "ffmpeg -r 10 -pattern_type glob -i \"" + routing_mask_dir +
      "/*.png\" -vcodec libx264 -pix_fmt yuv420p -y " + video_path;
  MLOG(INFO) << "Executing command: " << cmd;

  // Execute the command
  int ret = system(cmd.c_str());

  // Check the result
  if (ret != 0) {
    MLOG(ERROR) << "Command failed with return code: " << ret;
    throw std::runtime_error("ffmpeg command failed with return code: " +
                             std::to_string(ret));
  } else {
    MLOG(INFO) << "Command executed successfully. Video saved to: "
               << video_path;
  }
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  os::Init(argc, argv, "SdMapLocalizationOnlineNode");
  os::Node nh{"localization"};

  gflags::ParseCommandLineFlags(&argc, &argv, true);

  deeproute::localization::Process(FLAGS_in, FLAGS_out);

  return 0;
}
