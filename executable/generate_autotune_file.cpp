#include <fstream>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>

#include "proto/lock_on_road_config.pb.h"

#include "common/log.h"
#include "mm_localizer/ddmm_model.h"

DEFINE_string(sample_data_path, "", "");
DEFINE_string(model_path, "/opt/deeproute/ddmm-model/weights/ddmm.bin", "");
DEFINE_string(tune_file_path, ".", "");

namespace deeproute {
namespace localization {
namespace {

std::vector<float> LoadDataFromBinaryFile(const std::string& file_path) {
  std::vector<float> float_vector;
  std::ifstream input_file(file_path, std::ios::binary);

  if (input_file.is_open()) {
    float value;
    while (input_file.read(reinterpret_cast<char*>(&value), sizeof(float))) {
      float_vector.push_back(value);
    }

    input_file.close();
  } else {
    MLOG(INFO) << "Failed to open the file for reading.";
  }

  return float_vector;
}

}  // namespace

void Process() {
  const char* kAutoTuneReadPath = "TUNE_FILE_PATH_WRITE";
  const char* kAutoTuneReadValue = FLAGS_tune_file_path.c_str();
  const int ret = setenv(kAutoTuneReadPath, kAutoTuneReadValue, 1);
  if (!ret) {
    MLOG(WARN) << "Setting " << kAutoTuneReadPath << " to "
               << kAutoTuneReadValue << " succeeded!";
  } else {
    MLOG(WARN) << "Setting " << kAutoTuneReadPath << " to "
               << kAutoTuneReadValue << " failed!";
  }
  const char* result = getenv(kAutoTuneReadPath);
  if (result != nullptr) {
    MLOG(WARN) << "Value of " << kAutoTuneReadPath << ": " << result
               << std::endl;
  } else {
    MLOG(WARN) << "Environment variable " << kAutoTuneReadPath << " is not set."
               << std::endl;
  }

  DdmmConfig config;
  config.set_enable_ddmm(true);
  config.set_enable_ddmm_debug_output(true);
#ifdef DR_NVIDIA
  auto ddmm = CreateDDMMModel(config);
#endif

  std::vector<float> test_data = LoadDataFromBinaryFile(FLAGS_sample_data_path);

  if (!boost::filesystem::exists(FLAGS_model_path)) {
    MLOG(FATAL) << "Model not found at: " << FLAGS_model_path;
  }

#ifdef DR_NVIDIA
  // ---------------------- build engine starts --------------------------------
  DRInfer::IEngine* engine = DRInfer::CreateInferenceEngine(nullptr);
  engine->SetModelFrameworkType(DRInfer::MODEL_FRAMEWORK_PYTORCH);
  engine->SetRunTimeDataType(DRInfer::MODEL_HALF);  // MODEL_FLOAT, MODEL_HALF
  engine->AllowInputFloatConversion(true);
  engine->LoadFromOfflineGraph(FLAGS_model_path);
  DRInfer::Shape input_shape(
      {BATCH_SIZE, INPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  DRInfer::Shape reg_out_shape({REG_DIM1, REG_DIM2});
  DRInfer::Shape seg_out_shape(
      {BATCH_SIZE, SEG_OUTPUT_CHANNEL, IMAGE_SIZE, IMAGE_SIZE});
  std::string input_name = "input_data_0";
  std::string reg_out_name = "output_data_0";
  std::string seg_out_name = "output_data_1";

  engine->RegisterMaxInputShape(input_name.c_str(), &input_shape);
  engine->RegisterInferInputShape(input_name.c_str(), &input_shape);
  engine->RegisterOutputDataLayer(reg_out_name.c_str(), 0);
  engine->RegisterOutputDataLayer(seg_out_name.c_str(), 0);

  engine->AddOptimizations(DRInfer::OPTIMIZATION_MEMORY_REUSE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_MERGE_SLICE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_CONV_BN_MERGE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_CONV_RELU_MERGE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_FC_RELU_MERGE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_TENSORFORMAT_TRANSFORM);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_AUTOTUNE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_FC_BN_MERGE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_DUMMY_FUSE);
  engine->AddOptimizations(DRInfer::OPTIMIZATION_MULTIPLE_STREAM_SINGLE_BATCH);

  engine->BuildEngine();
  // ---------------------- build engine ends
  // -----------------------------------

  engine->RegisterInferInputData(input_name.c_str(), test_data.data(), false);
  engine->Inference(1, nullptr);
#endif
}

}  // namespace localization
}  // namespace deeproute

int main(int argc, char** argv) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  deeproute::localization::Process();

  return 0;
}
