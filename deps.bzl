load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository", "new_git_repository")

def lane_based_localization_benchmark_deps():
    ## Register toolchains
    native.register_toolchains(
        ":all",
    )

    native.local_repository(
        name = "lam_common",
        path = "../lam_common",
    )

    native.local_repository(
        name = "common",
        path = "../common",
    )

    native.local_repository(
        name = "dataocean-common-proto",
        path = "../common/dpbag/proto/dataocean-common-proto",
    )

    native.local_repository(
        name = "platform",
        path = "../platform",
    )

    native.local_repository(
        name = "driver",
        path = "../driver",
    )
