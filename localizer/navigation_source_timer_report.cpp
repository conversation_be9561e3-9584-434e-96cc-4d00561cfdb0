#include "localizer/navigation_source_timer_report.h"

#include <cstdlib>

#include <google/protobuf/util/json_util.h>

#include "drivers/gnss/ins.pb.h"
#include "lock_on_road/lock_on_road_debug.pb.h"

#include "base/time/time.h"
#include "common/event_log_handle.h"
#include "common/log.h"
#include "lam_common/proto_adapter/blc_adapter.h"

namespace deeproute {
namespace localization {

constexpr int kAmapDivergeCounterThreshold = 5;
constexpr int kAmapConvergedCounterThreshold = 5;
constexpr int kDrDivergeCounterThreshold = 5;
constexpr int kDrConvergedCounterThreshold = 5;

void NavigationSourceTimerReport::AddAmapMeasurements(
    const map_onboard_adapter::RealTimeAmap& amap_data) {
  if (amap_data.navi_type() == dr::gwm::navigation::STATUS_NONE) {
    amap_invalid_counter_ += 1;
    amap_valid_counter_ = 0;
  } else {
    amap_valid_counter_ += 1;
  }

  if (amap_invalid_counter_ >= kAmapDivergeCounterThreshold) {
    amap_navi_source_first_diverged_ = true;
    amap_navi_source_then_converged_ = false;
    amap_ready_time_ = 0;
  }

  if (amap_valid_counter_ >= kAmapConvergedCounterThreshold) {
    amap_navi_source_then_converged_ = true;
    amap_ready_time_ = deeproute::base::Time::Now().ToMicrosecond();
  }
}

// add gnss position raw
void NavigationSourceTimerReport::AddDrNaviMeasurements(
    const drivers::gnss::SensorsIns& gnss_position) {
  if (gnss_position.measurement_time() == gnss_pose_.measurement_time()) {
    // timestamp not change, skip handle
    return;
  }
  gnss_pose_ = gnss_position;

  if (gnss_position.type() == drivers::gnss::SensorsIns::INVALID ||
      gnss_position.type() == drivers::gnss::SensorsIns::CONVERGING ||
      gnss_position.type() == drivers::gnss::SensorsIns::UNCONVERGE_DR) {
    dr_invalid_counter_ += 1;
    dr_valid_counter_ = 0;
  } else {
    dr_valid_counter_ += 1;
  }

  if (dr_invalid_counter_ >= kDrDivergeCounterThreshold) {
    dr_navi_source_first_diverged_ = true;
    dr_navi_source_then_converged_ = false;
    dr_ready_time_ = 0;
  }

  if (dr_valid_counter_ >= kDrConvergedCounterThreshold) {
    dr_navi_source_then_converged_ = true;
    dr_ready_time_ = gnss_pose_.measurement_time();
  }
}

void NavigationSourceTimerReport::Report() {
  if (reported_) {
    MLOG_EVERY(INFO, 10000)
        << "NavigationSourceTimerReport Timer has reported once, neglect.";
    return;
  }

  if (amap_navi_source_first_diverged_ && amap_navi_source_then_converged_ &&
      dr_navi_source_first_diverged_ && dr_navi_source_then_converged_ &&
      amap_ready_time_ != 0 && dr_ready_time_ != 0) {
    NavigationSourceReadyTimeComparison time_compare;
    // DR navigation source
    NavigationSourceReadyTime dr;
    dr.set_source_name("dr");
    dr.set_ready_time(dr_ready_time_);

    // AMAP navigation source
    NavigationSourceReadyTime amap;
    amap.set_source_name("amap");
    amap.set_ready_time(amap_ready_time_);

    time_compare.add_ready_times()->CopyFrom(dr);
    time_compare.add_ready_times()->CopyFrom(amap);

    std::string json_string;
    google::protobuf::util::MessageToJsonString(time_compare, &json_string);
    MLOG(INFO) << "NavigationSourceTimerReport time_compare: " << json_string;
    MLOG(INFO) << "NavigationSourceTimerReport time diff: "
               << abs(dr_ready_time_ - amap_ready_time_) / 1e6 << " s.";
    common::ReportEvent(
        dr::common::LOCK_ON_ROAD,
        dr::common::LOCK_ON_ROAD_RUNTIME_NAVIGATION_SOURCE_READY_TIME,
        json_string);

    reported_ = true;
  } else {
    // clang-format off
    MLOG_EVERY(INFO, 100) << "NavigationSourceTimerReport, "
               << "  amap_valid_counter_  : " << amap_valid_counter_   << "/" << kAmapConvergedCounterThreshold
               << ", amap_invalid_counter_: " << amap_invalid_counter_ << "/" << kAmapDivergeCounterThreshold
               << ", dr_valid_counter_    : " << dr_valid_counter_     << "/" << kDrConvergedCounterThreshold
               << ", dr_invalid_counter_  : " << dr_invalid_counter_   << "/" << kDrDivergeCounterThreshold;
    // clang-format on
  }
}

}  // namespace localization

}  // namespace deeproute
