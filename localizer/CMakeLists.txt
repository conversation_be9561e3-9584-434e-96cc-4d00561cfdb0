add_library(localization_localizer
        localizer.cpp
        semantic_map_loader.cpp
        scenario_handler.cpp
        link_properties_pub_event.cpp
        mm_confidence_estimator.cpp
        navigation_source_timer_report.cpp
        gnss_span_abnormal_report.cpp
)
target_link_libraries(localization_localizer
        ${FMM_LIBRARY}
        ${Boost_LIBRARIES}
        common_math
        localization_mm
        localization_hmm
        common_transform
        ${GTEST_BOTH_LIBRARIES}
        lane_localization_proto
        common_geographiclib
        lam_common_base
        common_semantic_lmdb
        common_road_map
        localization_geometry
        common_time
        common_event_log
        snappy
)

install(TARGETS
        localization_localizer
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})