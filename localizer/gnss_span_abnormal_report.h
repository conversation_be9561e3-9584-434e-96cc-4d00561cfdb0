#pragma once

#include <boost/circular_buffer.hpp>

#include "drivers/gnss/ins.pb.h"

#include "transform/transformation.h"

namespace deeproute {
namespace localization {

class GnssSpanAbnormalReport {
 public:
  GnssSpanAbnormalReport() { odom_queue_.set_capacity(500); };
  ~GnssSpanAbnormalReport() = default;

  // add gnss position raw
  void AddDrNaviMeasurements(const drivers::gnss::SensorsIns& gnss_position);

  void AddOdomMeasurements(const ::common::Transformation3& odom);

 private:
  bool reported_ = false;

  boost::circular_buffer<::common::Transformation3> odom_queue_;
};

}  // namespace localization

}  // namespace deeproute