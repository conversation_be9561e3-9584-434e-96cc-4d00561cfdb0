#include "localizer/gnss_span_abnormal_report.h"

#include <cstdlib>

#include <google/protobuf/util/json_util.h>

#include "drivers/gnss/ins.pb.h"

#include "common/event_log_handle.h"
#include "common/log.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

// add gnss position raw
void GnssSpanAbnormalReport::AddDrNaviMeasurements(
    const drivers::gnss::SensorsIns& gnss_position) {
  if (reported_) {
    return;
  }

  if (gnss_position.position_velocity_type() != 0) {
    odom_queue_.clear();
  }

  double span_error_odom_movement = 0.0;
  if (odom_queue_.size() >= 2) {
    span_error_odom_movement = abs((odom_queue_.front().GetTranslation() -
                                    odom_queue_.back().GetTranslation())
                                       .norm());
  }

  if (span_error_odom_movement >= 100) {
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_OUTPUT_GNSS_STATUS_ABNORMAL);
    reported_ = true;
    MLOG(INFO) << "GnssSpanAbnormalReport: error distance: "
               << span_error_odom_movement << ", reported: " << reported_
               << " gnss_position.position_velocity_type(): "
               << gnss_position.position_velocity_type();
  }
  // else {
  //   MLOG(INFO) << "GnssSpanAbnormalReport: error distance: "
  //              << span_error_odom_movement << ", reported: " << reported_
  //              << " gnss_position.position_velocity_type(): "
  //              << gnss_position.position_velocity_type();
  // }
}

void GnssSpanAbnormalReport::AddOdomMeasurements(
    const ::common::Transformation3& odom) {
  if (reported_) {
    return;
  }

  if (odom_queue_.empty()) {
    odom_queue_.push_back(odom);
    return;
  }

  // if delta pose > 1m, add to queue
  ::common::Transformation3 prev_pose = odom_queue_.back();
  const double delta_dist =
      abs((odom.GetTranslation() - prev_pose.GetTranslation()).norm());
  if (delta_dist < 1) {
    return;
  }

  odom_queue_.push_back(odom);
}

}  // namespace localization

}  // namespace deeproute
