#pragma once

#include <boost/circular_buffer.hpp>
#include "drivers/gnss/ins.pb.h"
#include "common/types.h"

namespace deeproute {
namespace localization {

class ScenarioHandler {
 public:
  ScenarioHandler() {
    constexpr int kHistoryInsConvergenceWindowSize = 50;  // 5sec
    fixed_window_ins_convergences_.set_capacity(
        kHistoryInsConvergenceWindowSize);
  };
  ~ScenarioHandler() = default;

  void Add(const deeproute::drivers::gnss::SensorsIns_Type gnss_type, const bool sd_link_type_is_tunnel,
           const Vector3d& utm_position, const double xy_std);

  Vector3d GetLastPositionInTunnel() const;

  // if sd link type is tunnel and gnss type is <= 5, then vehicle should be
  // inside tunnel
  bool IsInTunnel() const;

  double DistanceToLastTunnelPose(const Vector3d& cur_pos);

  bool IsNearLastTunnel(const Vector3d& cur_pos, const double thres);

  bool GlobalPoseAccurate();

  void Reset() {
    latest_utm_position_in_tunnel_ = Vector3d(-1e6, -1e6, -1e6);
    sd_link_type_is_tunnel_ = false;
    gnss_type_ = 6;
    xy_std_ = 0.01;
  };

 private:
  int gnss_type_ = 6;
  bool sd_link_type_is_tunnel_ = false;
  Vector3d latest_utm_position_in_tunnel_ = {-1e6, -1e6, -1e6};
  double xy_std_ = 0.01;

  boost::circular_buffer<bool> fixed_window_ins_convergences_;
};

}  // namespace localization
}  // namespace deeproute