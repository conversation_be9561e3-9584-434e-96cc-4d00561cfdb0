#include "localizer/mm_confidence_estimator.h"

#include "drivers/gnss/gnss_raw.pb.h"

#include "common/log.h"

namespace deeproute {
namespace localization {

constexpr double kLateralDistanceThreshold = 20.0;
constexpr double kLongitudinalDistanceThreshold = 20.0;
constexpr double kOdomAccuErrorRateLongidutinal = 0.006;

void MMConfidenceEstimator::AddLor(
    const deeproute::localization::LockOnRoadResult& lor) {
  lock_on_road_result_ = lor;

  if (lor.status() == LockOnRoadResult::NO_ROUTING) {
    in_tunnel_ = false;
    tunnel_start_link_idx_ = -1;
    tunnel_end_link_idx_ = -1;
    return;
  }

  if (!lock_on_road_result_.has_link_properties() ||
      !lock_on_road_result_.link_properties().tunnel()) {
    in_tunnel_ = false;
    tunnel_start_link_idx_ = -1;
    tunnel_end_link_idx_ = -1;
    return;
  }

  if (!in_tunnel_ && lock_on_road_result_.link_properties().tunnel()) {
    in_tunnel_ = true;
    tunnel_start_link_idx_ = lock_on_road_result_.sd_link_index();
    return;
  }

  if (in_tunnel_ && lock_on_road_result_.link_properties().tunnel()) {
    tunnel_end_link_idx_ = lock_on_road_result_.sd_link_index();
    return;
  }
}

// getter
LockOnRoadResult MMConfidenceEstimator::GetLockOnRoadResult() {
  if (lock_on_road_result_.status() == LockOnRoadResult::NO_ROUTING ||
      min_distance_to_routing_response_ == -1) {
    return lock_on_road_result_;
  }

  //-------- lateral position confidence------------
  lock_on_road_result_.mutable_sd_localization_status()
      ->mutable_lateral_position_confidence()
      ->set_confidence_value(min_distance_to_routing_response_);
  if (min_distance_to_routing_response_ <= kLateralDistanceThreshold) {
    lock_on_road_result_.mutable_sd_localization_status()
        ->mutable_lateral_position_confidence()
        ->set_confidence_value(
            LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_HIGH);
  } else {
    lock_on_road_result_.mutable_sd_localization_status()
        ->mutable_lateral_position_confidence()
        ->set_confidence_value(
            LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_LOW);
  }

  // --------longitudinal position confidence------------
  const double travelled_distance_in_tunnel = GetTravelledDistanceInTunnel();
  const double accu_longitudinal_error =
      travelled_distance_in_tunnel * kOdomAccuErrorRateLongidutinal;

  const double pose_std = std::sqrt(gnss_pose_.position_covariance()[0] +
                                    gnss_pose_.position_covariance()[1]);
  const bool pose_converged = pose_std < 3;

  if (accu_longitudinal_error <= kLongitudinalDistanceThreshold &&
      pose_converged) {
    lock_on_road_result_.mutable_sd_localization_status()
        ->mutable_longitudinal_position_confidence()
        ->set_confidence_value(
            LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_HIGH);
  } else {
    lock_on_road_result_.mutable_sd_localization_status()
        ->mutable_longitudinal_position_confidence()
        ->set_confidence_value(
            LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_LOW);
  }

  // summary for one combined status
  if (lock_on_road_result_.sd_localization_status()
              .lateral_position_confidence()
              .confidence_value() ==
          LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_HIGH &&
      lock_on_road_result_.sd_localization_status()
              .longitudinal_position_confidence()
              .confidence_value() ==
          LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_HIGH) {
    lock_on_road_result_.mutable_sd_localization_status()->set_confidence(
        LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_HIGH);
  } else {
    lock_on_road_result_.mutable_sd_localization_status()->set_confidence(
        LockOnRoadResult_SdLocalizationStatus_ConfidenceStatus_LOW);
  }

  // MLOG(INFO) << "MMConfidenceEstimator: travelled_distance_in_tunnel: "
  //            << travelled_distance_in_tunnel
  //            << ", accu_longitudinal_error: " << accu_longitudinal_error
  //            << ", pose_converged: " << pose_converged
  //            << ", pose_std: " << pose_std
  //            <<", min_distance_to_routing_response_: "<<min_distance_to_routing_response_;
  // MLOG(INFO)
  //     << "MMConfidenceEstimator: "
  //     << lock_on_road_result_.sd_localization_status().ShortDebugString();

  return lock_on_road_result_;
}

double MMConfidenceEstimator::GetTravelledDistanceInTunnel() {
  if (custom_graph_.empty()) {
    return 0.0;
  }

  if (tunnel_start_link_idx_ == -1 || tunnel_end_link_idx_ == -1) {
    return 0.0;
  }

  if (tunnel_start_link_idx_ >= tunnel_end_link_idx_) {
    MLOG(WARN) << "MM confidence estimator: tunnel start link index is larger "
                  "than tunnel end link index: "
               << tunnel_start_link_idx_ << " " << tunnel_end_link_idx_;
    return 0.0;
  }

  if (tunnel_end_link_idx_ >= static_cast<int>(custom_graph_.size())) {
    MLOG(WARN)
        << "MM confidence estimator: tunnel end link index is larger than "
           "custom graph links size: "
        << tunnel_end_link_idx_ << " " << custom_graph_.size();
    return 0.0;
  }

  route_distance_in_tunnel_ = 0.0;
  for (int i = tunnel_start_link_idx_; i < tunnel_end_link_idx_; ++i) {
    route_distance_in_tunnel_ += custom_graph_[i].length;
  }

  MLOG_EVERY(INFO, 100) << "MM confidence estimator: tunnel start link index: "
             << tunnel_start_link_idx_
             << " tunnel end link index: " << tunnel_end_link_idx_
             << " route distance in tunnel: " << route_distance_in_tunnel_;

  return route_distance_in_tunnel_;
}

}  // namespace localization
}  // namespace deeproute
