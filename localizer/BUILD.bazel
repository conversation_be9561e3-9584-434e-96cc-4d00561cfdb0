package(default_visibility = ["//visibility:public"])
load("@rules_cc//cc:defs.bzl", "cc_library", "cc_binary")

cc_library(
  name = "localizer",
  hdrs = ["localizer.h"],
  srcs = ["localizer.cpp"],
  deps = [
        "@common//proto/compliance:compliance_proto_cc",
        "@lam_common//lam_common/proto_adapter:blc_adapter",
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//proto/perception:perception_nn_proto_cc",
        "@common//proto/common:geometry_proto_cc",
        "@common//proto/perception:deeproute_perception_ras_map_proto_cc",
        "@common//proto/routing:navinfo_routing_proto_cc",
        "@common//proto/drivers:sensor_image_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_debug_proto_cc",
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@common//proto/map:amap_drive_route_planning_proto_cc",
        "@common//proto/map:sd_horizon_provider_proto_cc",
        "@common//proto/drapi:gwm_navigation_proto_cc",
        "@common//proto/drapi:operation_status_proto_cc",
        ":link_properties_pub_event",
        ":scenario_handler",
        ":semantic_map_loader",
        ":navigation_source_timer_report",
        ":mm_confidence_estimator",
        ":gnss_span_abnormal_report",
        "//data_adapter:ins_adapter",
        "//data_adapter:projection_transformation",
        "//geometry:geometry",
        "//geometry:ras_map_process",
        "//joint:ll_utils",
        "//lane_estimator:lane_estimator",
        "//mm_localizer:ddmm_model",
        "//mm_localizer:ddmm_shadow_mode",
        "//mm_localizer:log_utils",
        "//mm_localizer:map_matching",
        "//mm_localizer:utils",
        "@common//common/conversions:cvmat_conversions",
        "@common//common/road_map:types",
        "@common//common:event_log_handle",
        "@common//common:geometry",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:point",
        "@common//common:time",
        "@common//common:time_based_interpolation",
        "@common//common:time_types",
        "@common//common:types",
        "@common//transform:transformation",
        "@lam_common//lam_common/client:region_server_client",
        "@lam_common//lam_common/client:sd_map_reader",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
        "@lam_common//lam_common:proto_utils",
        "@lam_common//lam_common:singleton_projection",
        "@lam_common//lam_common:utm_projection_convert",
        "@fmm//:fmm",
        "@snappy//:snappy",
        "@grpc//:grpc++",
  ],
)

cc_library(
  name = "link_properties_pub_event",
  hdrs = ["link_properties_pub_event.h"],
  srcs = ["link_properties_pub_event.cpp"],
  deps = [
        "@common//proto/compliance:compliance_proto_cc",
        "@common//proto/common:module_event_proto_cc",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "//proto:lock_on_road_config_cc_proto",
        "@fmm//:fmm",
        "@common//transform:transformation",
        "@common//common:event_log_handle",
        "@common//common:log",
        "@common//common:point",
        "@common//common:time",
        "@lam_common//lam_common/proto/lam_common:projection_cc_proto",
  ],
)

cc_library(
  name = "scenario_handler",
  hdrs = ["scenario_handler.h"],
  srcs = ["scenario_handler.cpp"],
  deps = [
        "@common//common:types",
        "@common//proto/drivers/gnss:ins_proto_cc",
  ],
)

cc_library(
  name = "semantic_map_loader",
  hdrs = ["semantic_map_loader.h"],
  srcs = ["semantic_map_loader.cpp"],
  deps = [
        "@common//proto/common:geometry_proto_cc",
        "@common//proto/semantic_map:map_proto_cc",
        "@common//common/road_map:hd_map_server_loading_utils",
        "@common//common/road_map:hd_map_spatial_lookup_server",
        "@common//common/road_map:hdmap_info",
        "@common//common/road_map:types",
        "@common//common/semantic_lmdb:semantic_map_conversion",
        "@common//common:file_util",
        "@common//common:geometry",
        "@lam_common//lam_common:proto_utils",
  ],
)

cc_library(
  name = "mm_confidence_estimator",
  hdrs = ["mm_confidence_estimator.h"],
  srcs = ["mm_confidence_estimator.cpp"],
  deps = [
        "@common//proto/drivers/gnss:gnss_raw_proto_cc",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:time_types",
        "@common//common:types",
        "@common//proto/lock_on_road:lock_on_road_proto_cc",
        "@fmm//:fmm",
  ],
)


cc_library(
  name = "navigation_source_timer_report",
  hdrs = ["navigation_source_timer_report.h"],
  srcs = ["navigation_source_timer_report.cpp"],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@lam_common//lam_common/proto_adapter:blc_adapter",
        "@common//proto/lock_on_road:lock_on_road_debug_proto_cc",
        "@common//common:event_log_handle",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:time",
        "@common//common:time_types",
        "@common//common:types",
        "@fmm//:fmm",
  ],
)

cc_library(
  name = "gnss_span_abnormal_report",
  hdrs = ["gnss_span_abnormal_report.h"],
  srcs = ["gnss_span_abnormal_report.cpp"],
  deps = [
        "@common//proto/drivers/gnss:ins_proto_cc",
        "@common//common:event_log_handle",
        "@common//common:log",
        "@common//common:macros",
        "@common//common:time",
        "@common//common:time_types",
        "@common//common:types",
        "@common//transform:transformation",
        "@fmm//:fmm",
  ],
)
