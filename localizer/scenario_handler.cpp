#include "scenario_handler.h"


namespace deeproute {
namespace localization {

void ScenarioHandler::Add(const deeproute::drivers::gnss::SensorsIns_Type gnss_type,
                          const bool sd_link_type_is_tunnel,
                          const Vector3d& utm_position, const double xy_std) {
  gnss_type_ = gnss_type;
  sd_link_type_is_tunnel_ = sd_link_type_is_tunnel;
  xy_std_ = xy_std;

  // check gnss_raw.proto for gnss_type
  bool gnss_good = (gnss_type_ == deeproute::drivers::gnss::SensorsIns_Type_GOOD ? true : false);
  fixed_window_ins_convergences_.push_back(gnss_good);

  if (IsInTunnel()) {
    latest_utm_position_in_tunnel_ = utm_position;
  }
}

Vector3d ScenarioHandler::GetLastPositionInTunnel() const {
  return latest_utm_position_in_tunnel_;
}

// POSITION_TYPE_NONE = 0;
// POSITION_TYPE_FIXED = 1;
// POSITION_TYPE_FIXEDHEIGHT = 2;
// POSITION_TYPE_FLOATCONV = 4;
// POSITION_TYPE_WIDELANE = 5;
// POSITION_TYPE_NARROWLANE = 6;
// POSITION_TYPE_DOPPLER_VELOCITY = 8;
// POSITION_TYPE_SINGLE = 16;
bool ScenarioHandler::IsInTunnel() const {
  return sd_link_type_is_tunnel_;
}

double ScenarioHandler::DistanceToLastTunnelPose(const Vector3d& cur_pos) {
  return (cur_pos - GetLastPositionInTunnel()).norm();
}

bool ScenarioHandler::IsNearLastTunnel(const Vector3d& cur_pos,
                                       const double thres) {
  return (DistanceToLastTunnelPose(cur_pos) < thres);
}

bool ScenarioHandler::GlobalPoseAccurate() {
  const bool ins_converged =
      std::all_of(fixed_window_ins_convergences_.begin(),
                  fixed_window_ins_convergences_.end(),
                  [](const bool& converged) { return converged; });
  return ins_converged;
}

}  // namespace localization
}  // namespace deeproute