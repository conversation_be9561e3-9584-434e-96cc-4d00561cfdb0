#include "localizer/semantic_map_loader.h"

#include "common/geometry.pb.h"
#include "semantic_map/map.pb.h"

#include "common/road_map/hd_map_server_loading_utils.h"
#include "common/semantic_lmdb/semantic_map_conversion.h"
#include "lam_common/proto_utils.h"

namespace deeproute {
namespace localization {

std::shared_ptr<deeproute::common::HDMapLaneInfoServer>
CreateHdMapServerFromBinary(const std::string& semantic_binary_path,
                            const std::string& semantic_lmdb_path) {
  deeproute::common::GenerateSemanticLmdbFromProto(semantic_binary_path,
                                                   semantic_lmdb_path);
  std::shared_ptr<deeproute::common::HDMapLaneInfoServer> map_server =
      deeproute::common::LoadSemanticMapServerFromWholeRegion<
          deeproute::common::HDMapLaneInfoServer>(semantic_lmdb_path);
  map_server->Initialize();

  return map_server;
}

std::shared_ptr<deeproute::common::HDMapLaneInfoServer>
CreateHdMapServerFromLmdb(const std::string& semantic_lmdb_path) {
  std::shared_ptr<deeproute::common::HDMapLaneInfoServer> map_server =
      deeproute::common::LoadSemanticMapServerFromWholeRegion<
          deeproute::common::HDMapLaneInfoServer>(semantic_lmdb_path);
  map_server->Initialize();

  return map_server;
}

void LoadSemanticBoundaryMap(
    const std::string& semantic_binary_path,
    std::unordered_map<int, deeproute::common::Polyline>*
        boundary_id_to_boundaries) {
  deeproute::hdmap::Map map;
  if (!deeproute::LoadProtoFile(semantic_binary_path, &map)) {
    MLOG(FATAL) << "failed to map: " << semantic_binary_path;
  }

  for (const auto& boundary : map.lane_boundaries()) {
    (*boundary_id_to_boundaries)[boundary.id()] = boundary.boundary();
  }
}

void GetLaneBoundariesGivenIds(
    const std::unordered_map<int, deeproute::common::Polyline>&
        boundary_id_to_boundaries,
    const std::vector<int>& nearby_lane_boundary_ids,
    std::vector<deeproute::common::Polyline>* polylines) {
  for (const auto& id : nearby_lane_boundary_ids) {
    if (boundary_id_to_boundaries.find(id) == boundary_id_to_boundaries.end()) {
      MLOG(INFO) << "LANE BOUNDARY ID: " << id
                 << " NOT FOUND IN SEMANTIC MAP! THIS COULD BE "
                    "SEMANTIC MAP VERSION MISMATCH!";
      continue;
    }
    // MLOG(INFO) << "Found lane id: " << id;
    // MLOG(INFO) << "Polygon: " <<
    // boundary_id_to_boundaries.at(id).DebugString();
    polylines->push_back(boundary_id_to_boundaries.at(id));
  }
  // MLOG(INFO) << "Loaded number of polylines: " << polylines->size();
}

}  // namespace localization
}  // namespace deeproute
