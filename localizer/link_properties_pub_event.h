#pragma once

#include <memory>

#include <boost/signals2.hpp>
#include <network/type.hpp>

#include "compliance/compliance.pb.h"
#include "lock_on_road/lock_on_road.pb.h"

#include "common/log.h"

namespace deeproute {
namespace localization {

class LinkPropertiesPubEvent {
 public:
  virtual ~LinkPropertiesPubEvent() = default;
  LinkPropertiesPubEvent() {};

  void Update(const LockOnRoadResult& lor_result,
              const LockOnRoadResult& lom_result);

  inline void UpdateComplianceInfo(
      const std::shared_ptr<const compliance::Compliance>& compliance) {
    compliance_ = compliance;
  }

  inline void UpdataSdMapInfo(const std::string& map_version) {
    if (map_version.empty()) {
      MLOG(ERROR) << "SdOnboardMap map_version is empty";
      map_version_ = "NOT_GIVEN";
    } else {
      map_version_ = map_version;
    }
  }

 private:
  void Publish();

  std::string map_version_ = "NULL";
  std::shared_ptr<const compliance::Compliance> compliance_;

  LockOnRoadResult prev_lor_result_;
  LockOnRoadResult prev_lom_result_;
  LockOnRoadResult prev_published_result_;
  LockOnRoadResult result_to_be_published_;
};
}  // namespace localization
}  // namespace deeproute
