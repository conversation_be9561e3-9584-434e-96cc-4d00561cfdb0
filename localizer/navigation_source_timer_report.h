#pragma once

#include "drivers/gnss/ins.pb.h"

#include "lam_common/proto_adapter/blc_adapter.h"

namespace deeproute {
namespace localization {

class NavigationSourceTimerReport {
 public:
  NavigationSourceTimerReport() = default;
  ~NavigationSourceTimerReport() = default;

  void AddAmapMeasurements(const map_onboard_adapter::RealTimeAmap& amap);

  // add gnss position raw
  void AddDrNaviMeasurements(const drivers::gnss::SensorsIns& gnss_position);

  void Report();

 private:
  bool reported_ = false;

  int amap_valid_counter_ = 0;
  int amap_invalid_counter_ = 0;
  bool amap_navi_source_first_diverged_ = false;
  bool amap_navi_source_then_converged_ = false;
  int64_t amap_ready_time_ = 0;


  drivers::gnss::SensorsIns gnss_pose_;
  int dr_invalid_counter_ = 0;
  int dr_valid_counter_ = 0;
  bool dr_navi_source_first_diverged_ = false;
  bool dr_navi_source_then_converged_ = false;
  int64_t dr_ready_time_ = 0;

};

}  // namespace localization

}  // namespace deeproute
