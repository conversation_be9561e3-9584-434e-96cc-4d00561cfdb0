import numpy as np
import matplotlib.pyplot as plt


file = open(
    "/home/<USER>/lane-based-localization-benchmark/build/test.log").readlines()

import re

lane_sums = []
lane_index = []
for line in file:
    # line = line.split(", ")
    line = re.split('; |, | ', line)
    
    print(line)
    if len(line) != 13:
        continue

    print(line[7], line[10])

    lane_sums.append(float(line[7]))
    lane_index.append(float(line[10]))

plt.plot(lane_sums, label='lane_sums')
plt.plot(lane_index, label='lane_index')
plt.legend(loc="upper left")
plt.show()