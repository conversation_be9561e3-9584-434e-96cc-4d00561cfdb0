#pragma once

#include <boost/filesystem.hpp>
#include <boost/filesystem/path.hpp>

#include "common/road_map/hd_map_lane_info_server.h"

namespace deeproute {
namespace localization {

std::shared_ptr<deeproute::common::HDMapLaneInfoServer>
CreateHdMapServerFromBinary(const std::string& semantic_binary_path,
                            const std::string& semantic_lmdb_path);

std::shared_ptr<deeproute::common::HDMapLaneInfoServer>
CreateHdMapServerFromLmdb(const std::string& semantic_lmdb_path);

void LoadSemanticBoundaryMap(
    const std::string& semantic_binary_path,
    std::unordered_map<int, deeproute::common::Polyline>*
        boundary_id_to_boundaries);

void GetLaneBoundariesGivenIds(
    const std::unordered_map<int, deeproute::common::Polyline>&
        boundary_id_to_boundaries,
    const std::vector<int>& nearby_lane_boundary_ids,
    std::vector<deeproute::common::Polyline>* polylines);

}  // namespace localization
}  // namespace deeproute
