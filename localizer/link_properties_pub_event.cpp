#include "localizer/link_properties_pub_event.h"

#include <google/protobuf/util/json_util.h>

#include "common/module_event.pb.h"

#include "common/event_log_handle.h"
#include "common/log.h"

namespace deeproute {

namespace localization {

void LinkPropertiesPubEvent::Update(const LockOnRoadResult& lor_result,
                                    const LockOnRoadResult& lom_result) {
  prev_lor_result_ = lor_result;
  prev_lom_result_ = lom_result;

  if (lor_result.status() ==
          LockOnRoadResult::Status::LockOnRoadResult_Status_NORMAL ||
      lor_result.status() ==
          LockOnRoadResult::Status::LockOnRoadResult_Status_PASSOVER) {
    result_to_be_published_ = lor_result;
  } else if (lom_result.status() ==
                 LockOnRoadResult::Status::LockOnRoadResult_Status_NORMAL ||
             lom_result.status() ==
                 LockOnRoadResult::Status::LockOnRoadResult_Status_PASSOVER) {
    result_to_be_published_ = lom_result;
  } else {
    MLOG(INFO) << "LOR/LOM PUB DEBUG no valid result to pub.";
  }

  if (result_to_be_published_.sd_link_id() !=
          prev_published_result_.sd_link_id() &&
      map_version_ != "NULL" && result_to_be_published_.sd_link_id() != "0") {
    result_to_be_published_.mutable_link_properties()->set_sdmap_version(
        map_version_);
    // result_to_be_published_.mutable_link_properties()->set_sdmap_source_type(
    //     LockOnRoadResult_LinkProperties::TENCENT_PLUS);
    result_to_be_published_.mutable_link_properties()->set_sdmap_source_type(
        LockOnRoadResult_LinkProperties::TENCENT);

    Publish();
  } else {
    MLOG(INFO) << "LOR/LOM PUB DEBUG id : "
               << result_to_be_published_.sd_link_id() << " "
               << prev_published_result_.sd_link_id()
               << ", map_version_:" << map_version_;
  }
  prev_published_result_ = result_to_be_published_;
}

void LinkPropertiesPubEvent::Publish() {
  if (compliance_ != nullptr && compliance_->has_is_sensitive() &&
      compliance_->is_sensitive()) {
    MLOG_EVERY(INFO, 1000)
        << "LOR/LOM PUB DEBUG sensitive area, do not publish link properties";
    return;
  }

  std::string json_string;
  google::protobuf::util::MessageToJsonString(result_to_be_published_,
                                              &json_string);
  common::ReportEvent(dr::common::LOCK_ON_ROAD,
                      dr::common::LOCK_ON_ROAD_RUNTIME_LINK_PROPERTIES,
                      json_string);
  MLOG(INFO) << "LOR/LOM PUB DEBUG result_to_be_published_: " << json_string;
}

}  // namespace localization
}  // namespace deeproute
