#include "localizer/localizer.h"

#include <math.h>
#include <snappy.h>
#include <sys/utsname.h>

#include <algorithm>
#include <cstdint>
#include <cstdlib>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include <Eigen/Dense>
#include <boost/circular_buffer.hpp>
#include <boost/circular_buffer/base.hpp>
#include <boost/filesystem/operations.hpp>
#include <boost/geometry/algorithms/distance.hpp>
#include <mm/fmm/fmm_algorithm.hpp>
#include <mm/mm_type.hpp>
#include <mm/transition_graph.hpp>
#include <network/network.hpp>
#include <network/type.hpp>
#include <opencv2/core/hal/interface.h>
#include <opencv2/opencv.hpp>
#include <pcl/common/transforms.h>

#include "common/geometry.pb.h"
#include "lam_common/projection.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "map/amap_drive_route_planning.pb.h"
#include "map/sd_horizon_provider.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "perception/perception_nn.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "algorithm/geom_algorithm.hpp"
#include "common/event_log_handle.h"
#include "common/log.h"
#include "common/time.h"
#include "common/time_based_interpolation.h"
#include "common/time_types.h"
#include "common/types.h"
#include "data_adapter/ins_adapter.h"
#include "data_adapter/projection_transformation.h"
#include "data_adapter/utm_projector.h"
#include "geometry/geometry.h"
#include "geometry/ras_map_process.h"
#include "joint/ll_utils.h"
#include "lam_common/client/sd_map_reader.h"
#include "lam_common/singleton_projection.h"
#include "lam_common/utm_projection_convert.h"
#include "lane_estimator/lane_estimator.h"
#include "localizer/gnss_span_abnormal_report.h"
#include "localizer/link_properties_pub_event.h"
#include "localizer/navigation_source_timer_report.h"
#include "localizer/scenario_handler.h"
#include "mm_localizer/ddmm_model.h"
#include "mm_localizer/ddmm_shadow_mode.h"
#include "mm_localizer/log_utils.h"
#include "mm_localizer/map_matching.h"
#include "mm_localizer/utils.h"

namespace deeproute {
namespace localization {

namespace {

template <typename T>
std::vector<T> ConvertToVector(const boost::circular_buffer<T>& elements) {
  std::vector<T> vecs;
  for (const auto& ele : elements) {
    vecs.push_back(ele);
  }
  return vecs;
}

void ConvertPoseToTrajectoryAndHeadings(
    const boost::circular_buffer<TimeToPosePair>& poses, Vector2dVector* traj,
    std::vector<double>* headings, std::vector<double>* timestamps) {
  CHECK(poses.size());
  for (const auto& pose : poses) {
    const Vector3d t = pose.second.GetTranslation();
    traj->emplace_back(t.x(), t.y());
    const Vector3d rpy = pose.second.GetRollPitchYaw();
    const double yaw = rpy[2];
    const double shifted_yaw = yaw > 0 ? yaw : yaw + 2 * M_PI;
    headings->push_back(shifted_yaw);
    timestamps->push_back(static_cast<double>(pose.first / 1e6));
  }
}

void ConvertRoutingResponseToLinkIdLinkInfoDict(
    const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
        routing_response,
    std::unordered_map<std::string, deeproute::navinfo::Segment>*
        link_id_to_link_info) {
  link_id_to_link_info->clear();
  const deeproute::navinfo::Result& result = routing_response->result(0);
  const deeproute::navinfo::Route& route = result.route();
  for (const auto& segment : route.segm()) {
    (*link_id_to_link_info)[segment.ni_id()] = segment;
  }
}

}  // namespace

class Localizer : public LocalizerBase {
 public:
  EIGEN_MAKE_ALIGNED_OPERATOR_NEW

  explicit Localizer(const LockOnRoadConfig& config,
                     const ::common::Transformation3& vehicle_to_imu_transform);
  virtual ~Localizer();

  void HandleRtkInsMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::Ins>& rtk) override;

  void HandleRtkSensorInsMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::SensorsIns>& sensor_ins)
      override;

  void HandleGnssDataMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::GnssPosition>& gnss) override;

  void HandleOdometryMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::Ins>& odom) override;

  void HandleRasMapMeasurementMsg(
      const std::shared_ptr<const deeproute::perception::RASMap>& ras_map)
      override;

  void HandleGlobalRoutingMsg(
      const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
          routing_response) override;

  void HandleRasMapNN(
      const std::shared_ptr<const perception::NnFrame>& rasmap_nn) override;

  void HandleSdMapOnboard(const std::shared_ptr<const sd_map::SDMapOnboard>&
                              sdmap_onboard) override;

  void HandleAmapMsg(
      const std::shared_ptr<const map_onboard_adapter::RealTimeAmap>& amap_data)
      override;

  void HandleCompliance(
      const std::shared_ptr<const compliance::Compliance>& compliance) override;

  void HandleBlcStatus(
      const std::shared_ptr<const dr::operationstatus::OperationStatus>
          operation_status) override;

  bool SetProjectionPoint(const double lon_deg, const double lat_deg,
                          const double scale_factor) override;

  LockOnRoadResult GetLatestLockOnRoadResult() override;
  LaneIndexEstimationResult GetLatestLaneIndexEstimationResult() override;
  LockOnRoadDebugInfo GetLatestLockOnLaneDebugInfo() override;
  perception::RASMap GetDebugRasMap() override;
  LockOnRoadResult GetLatestDdmmResult() override;
  const LockOnRoadDdmmDebugInfo& GetLatestDdmmDebugInfo() override;
  AmapStatus GetLatestAmapStatus() override;
  Vector3d GetLorInputPoseBlhGcj02() override;

  // void PublishLockOnRoadResult(::common::TimeMicro& current_time);
  void RunDataDrivenMapMatching(const uint64_t time);

 protected:
  void LaneIndexEstimation(
      const std::shared_ptr<const perception::RASMap>& ras_map,
      LaneIndexEstimationResult& latest_lane_index_result);

  void NotifyLockOnRoadUpdate(const MapMatchingResult& result,
                              const std::string& request_id,
                              const uint64_t tick_time);

 protected:
  LockOnRoadConfig config_;
  proto::Projection projection_;
  std::unique_ptr<UtmProjectionConvert> utm_projector_ = nullptr;

  AmapStatus amap_status_;
  Vector3d lor_input_pose_;

  boost::circular_buffer<AmapStatus> amap_status_queue_;
  ::common::TimeBasedInterpolation<Vector3d> amap_utm_interpolator_;
  ::common::TimeBasedInterpolation<Vector3d> odom_pos_interpolator_;
  std::unordered_map<int, deeproute::map::AmapStep>
      amap_step_id_to_amap_step_utm_;
  std::unordered_map<int, deeproute::map::AmapStep>
      amap_step_id_to_amap_step_gcj02_;

  boost::circular_buffer<TimeToPosePair> gnss_time_and_poses_queue_;
  boost::circular_buffer<TimeToPosePair> odom_time_and_poses_queue_;
  // todo(canwang): not actually used. consider removing it.
  boost::circular_buffer<TimeToVector3DPair> odom_time_and_flu_vel_queue_;
  boost::circular_buffer<double> velocities_;
  boost::circular_buffer<PointCloudXYZIRT::ConstPtr> point_cloud_queue_;

  boost::circular_buffer<int> obs_lane_index_history_;
  boost::circular_buffer<int> obs_lane_sum_history_;

  bool gnss_divergence_ = true;
  bool gnss_solution_good_ = false;
  ::common::TimeMicro first_interger_time_;

  std::unique_ptr<MapMatchingModel> mm_localizer_ = nullptr;
#ifdef DR_NVIDIA
  std::unique_ptr<DDMMModel> ddmm_localizer_ = nullptr;
#endif
  std::unique_ptr<DdmmShadowMode> ddmm_shadow_mode_ = nullptr;
  std::unique_ptr<LaneEstimatorBase> lane_estimator_ = nullptr;

  // std::unique_ptr<DDRoutingModel> dd_routing_ = nullptr;
  bool blc_not_self_driving_mode_ = false;

  ::common::Transformation3 utm_to_odom_;

  std::shared_ptr<deeproute::common::HDMapLaneInfoServer> lane_map_server_ =
      nullptr;

  std::unordered_map<int, deeproute::common::Polyline>
      lane_id_to_lane_boundaries_;

  double obs_noise_;
  double heading_noise_;
  double transition_noise_;
  double search_radius_;
  bool enable_rtk_quality_control_;
  int neigbhor_num_;
  bool enable_ras_map_post_processing_;
  bool enable_lane_estimation_;
  bool use_amap_;

  int trip_id_ = 0;
  std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>
      prev_routing_response_ = std::make_shared<navinfo::SDRoutingResponse>();

  std::shared_ptr<const deeproute::perception::PerceptionObstacles>
      perception_obstacles_ = nullptr;

  drivers::gnss::Ins prev_ins_;
  ::common::Transformation3 rtk_pose_, prev_rtk_pose_;
  ::common::Transformation3 odom_pose_;

  float pose_horizontal_position_std_;
  std::atomic<bool> setting_routing_response_finished_;
  std::unordered_map<int, int> link_idx_to_route_seg_idx_;
  std::vector<std::string> virtual_links_ids_;
  std::vector<int> virtual_link_indices_;

  bool initialized_ = false;

  std::string sdmap_version_ = "";
  std::string routing_response_request_id_ = "-1";
  FMM::NETWORK::CustomGraph routing_response_graph_;
  std::unordered_map<long long, FMM::NETWORK::Edge> link_id_to_edge_;
  std::unordered_map<std::string, deeproute::navinfo::Segment>
      link_id_to_link_info_;

  PublishLockOnRoadSignal publish_lock_on_road_signal_;
  PublishLaneIndexSignal publish_lane_index_signal_;
  PublishLockOnRoadDebugInfoSignal publish_lock_on_road_debug_info_signal_;

  int64_t ras_map_time_ = -1;
  int32_t gnss_type_ = -1;
  int32_t obs_lane_index_ = -1;
  int32_t obs_lane_sum_ = -1;
  int32_t sd_lane_sum_ = -1;
  int32_t sd_link_is_tunnel_ = 0;
  int32_t ras_ego_lane_id_ = -1;
  ClosestCurbStatus prev_closest_curb_ = ClosestCurbStatus::NO_CURB;
  bool lock_on_road_succeeded_ = false;
  double min_distance_to_routing_response_ = -1;
  deeproute::common::Polyline ego_lane_;

  int lidar_step_ = 0;

  LockOnRoadResult latest_lock_on_lane_result_;
  LockOnRoadResult latest_ddmm_lock_on_lane_result_;
  LockOnRoadDdmmDebugInfo latest_ddmm_debug_info_;
  LaneIndexEstimationResult latest_lane_index_result_;
  LaneIndexEstimationResult latest_lane_index_raw_;

  RoutingSceneType routing_scene_type_;

  LockOnRoadDebugInfo latest_lock_on_lane_debug_;
  perception::RASMap debug_ras_map_;
  cv::Mat latest_ddmm_result_debug_image_;

  ScenarioHandler scenario_handler_;
  LinkPropertiesPubEvent link_properties_event_pub_;

  FMM::NETWORK::CustomGraph current_routing_response_graph_;

  ::common::Transformation3 vehicle_to_imu_transform_;
  InsAdapter ins_adapter_;
  bool acquire_projection_finished_ = false;

  // DDMM related variables
  std::mutex local_sd_map_mutex_;
  deeproute::sd_map::SdLinks local_sd_map_;
  std::unordered_map<uint64_t, LockOnRoadResult::LinkProperties>
      ni_id_to_link_properties_;

  Vector2d prev_query_point_;
  ::common::TimeMicro ddmm_handle_time_ = 0;
  bool enable_ddmm_ = false;
  int prev_onboard_sdmap_id_ = -1;
  std::string ddmm_debug_out_dir_;
  bool online_query_sdmap_ = false;
  std::unique_ptr<SdMapServiceReaderClient> sd_map_client_ = nullptr;

  // DDMMRotuing related variables
  std::shared_ptr<const perception::NnFrame> rasmap_nn_ =
      std::make_shared<perception::NnFrame>();

  // MMConfidenceEstimator mm_confidence_estimator_;
  NavigationSourceTimerReport navigation_source_timer_report_;
  GnssSpanAbnormalReport gnss_span_abnormal_report_;
};

Localizer::Localizer(const LockOnRoadConfig& config,
                     const ::common::Transformation3& vehicle_to_imu_transform)
    : config_(config),
      amap_utm_interpolator_(10 * deeproute::kSecond, 12 * deeproute::kSecond),
      odom_pos_interpolator_(5 * deeproute::kSecond, 2 * deeproute::kSecond),
      obs_noise_(config.map_matching_config().obs_noise()),
      heading_noise_(config.map_matching_config().heading_noise()),
      transition_noise_(config.map_matching_config().transition_noise()),
      search_radius_(config.map_matching_config().search_radius()),
      enable_rtk_quality_control_(
          config_.map_matching_config().enable_rtk_quality_control()),
      neigbhor_num_(config.map_matching_config().neigbhor_num()),
      enable_ras_map_post_processing_(config.enable_ras_map_post_processing()),
      enable_lane_estimation_(
          config.lane_estimator_config().enable_lane_estimation()),
      use_amap_(config.map_matching_config().enable_amap()),
      prev_query_point_(0.0, 0.0) {
  gnss_time_and_poses_queue_.set_capacity(
      config.map_matching_config().history_length());
  odom_time_and_poses_queue_.set_capacity(
      config.map_matching_config().history_length());
  odom_time_and_flu_vel_queue_.set_capacity(600);  // 12s
  velocities_.set_capacity(config.map_matching_config().history_length());

  // force it to be true
  use_amap_ = true;

  vehicle_to_imu_transform_ = vehicle_to_imu_transform;

  obs_lane_index_history_.set_capacity(100);
  obs_lane_sum_history_.set_capacity(100);
  point_cloud_queue_.set_capacity(10);
  amap_status_queue_.set_capacity(10);

  mm_localizer_ = CreateStMapMatchingModel(config.map_matching_config());
  lane_estimator_ = CreateLaneEstimator(config.lane_estimator_config());

  struct utsname systemInfo;
  std::string machine;
  if (uname(&systemInfo) != -1) {
    machine = systemInfo.machine;
    if (machine == "x86_64") {
      MLOG(INFO) << "System architecture: x86_64";
    } else if (machine == "aarch64") {
      MLOG(INFO) << "System architecture: aarch64";
    } else {
      MLOG(FATAL) << "Unknown system architecture";
    }
  } else {
    MLOG(FATAL) << "Failed to retrieve system information";
  }

  char* ddmm_debug_dir = std::getenv("DDMM_DEBUG_DIR");
  if (ddmm_debug_dir == nullptr) {
    MLOG(WARN) << "DDMM DEBUG ENV DDMM_DEBUG_DIR NOT SET";
    ddmm_debug_out_dir_ = "";
  } else {
    ddmm_debug_out_dir_ = std::string(ddmm_debug_dir);
    MLOG(WARN) << "DDMM DEBUG ENV DDMM_DEBUG_DIR SET TO "
               << ddmm_debug_out_dir_;
  }

  enable_ddmm_ = config_.ddmm_config().enable_ddmm();
#ifdef DR_NVIDIA
  if (enable_ddmm_) {
    ddmm_localizer_ = CreateDDMMModel(config.ddmm_config());
    ddmm_shadow_mode_ = CreateDdmmShadowMode();
  }
#else
  enable_ddmm_ = false;
#endif

  setting_routing_response_finished_ = false;

  prev_ins_.set_measurement_time(0);

  LogUtil::GetInstance()->Init(config.map_matching_config().log_config());
}

Localizer::~Localizer() { LogUtil::ReleaseInstance(); }

void Localizer::HandleGlobalRoutingMsg(
    const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
        routing_response) {
  if (!acquire_projection_finished_) {
    MLOG_EVERY(INFO, 100)
        << "When handling routing response, projection acquiring not "
           "finished yet.";
    return;
  }

  setting_routing_response_finished_ = false;

  const auto new_request_id = routing_response->request_id();
  const auto prev_request_id = prev_routing_response_->has_request_id()
                                   ? prev_routing_response_->request_id()
                                   : "-1";

  const auto new_response_id = routing_response->has_response_id()
                                   ? routing_response->response_id()
                                   : -1;
  const auto prev_response_id = prev_routing_response_->has_response_id()
                                    ? prev_routing_response_->response_id()
                                    : -1;

  if (!routing_response->result_size()) {
    MLOG(WARN) << "Received routing response with no result, ignore it.";
    return;
  }

  if (new_request_id != prev_request_id ||
      new_response_id != prev_response_id) {
    MLOG(INFO) << "Processing new routing response with projection pt: "
               << projection_.ShortDebugString();

    absl::StatusOr<FMM::NETWORK::CustomGraph> custom_graph;
    try {
      custom_graph =
          ConvertRoutingResponseToCustomGraph(*routing_response, projection_);
    } catch (const std::exception& e) {
      MLOG(WARN) << "Error converting routing response to custom graph: "
                 << e.what();
      return;
    }

    if (!custom_graph.ok()) {
      MLOG(ERROR) << "ignore current routing response. "
                  << custom_graph.status();
      return;
    }
    MLOG(INFO) << " Current and prev request id: " << new_request_id << ", "
               << prev_request_id
               << " Current and prev sub route id: " << new_response_id << ", "
               << prev_response_id << " Converted routing response size: "
               << custom_graph.value().size();

    if (custom_graph.value().empty()) {
      MLOG(WARN) << "Routing response recerved, but converted custom graph is "
                    "empty, this couldn'be be right!";
      return;
    }

    current_routing_response_graph_ = std::move(*custom_graph);
    mm_localizer_->SetGlobalPlannedRoutes(current_routing_response_graph_);
    mm_localizer_->SetRoute(routing_response, current_routing_response_graph_);
    scenario_handler_.Reset();
    MLOG(INFO) << "Reset scenario handler.";

    routing_response_graph_ = mm_localizer_->GetSubGraph();
    link_id_to_edge_.clear();
    for (const auto& edge : routing_response_graph_) {
      link_id_to_edge_[edge.id] = edge;
    }

    MLOG(INFO) << "New routing response received with link size: "
               << routing_response_graph_.size();

    prev_routing_response_ = routing_response;

    routing_response_request_id_ = routing_response->request_id();

    ConvertRoutingResponseToLinkIdLinkInfoDict(routing_response,
                                               &link_id_to_link_info_);

    // construct link id to route seg num mapping
    int seg_idx = 0;
    std::unordered_map<int, int> link_idx_to_seg_idx;
    std::vector<std::string> virtual_links;
    std::vector<int> virtual_link_indices;
    for (int i = 0; i < routing_response->result(0).route().segm_size(); i++) {
      const auto& seg = routing_response->result(0).route().segm(i);
      if (seg.has_virtual_link() && seg.virtual_link()) {
        seg_idx += 1;
        virtual_links.push_back(seg.ni_id());
        virtual_link_indices.push_back(i);
      }
      link_idx_to_seg_idx[i] = seg_idx;
    }
    link_idx_to_route_seg_idx_ = link_idx_to_seg_idx;
    virtual_links_ids_ = virtual_links;
    virtual_link_indices_ = virtual_link_indices;

    // extract AMAP info from routing response
    const auto& amap_response =
        routing_response->request().request_info().amap_response();
    const auto& route = amap_response.route();
    int amap_path_index = 0;
    for (int i = 0; i < amap_response.route().paths_size(); i++) {
      MLOG(INFO) << "amap_response.route().paths(i).path_id_64(): "
                 << amap_response.route().paths(i).path_id_64()
                 << ", routing_response->route_id():"
                 << routing_response->route_id();
      if (amap_response.route().paths(i).path_id_64() ==
          routing_response->route_id()) {
        amap_path_index = i;
        break;
      }
    }
    // MLOG(INFO) << "amap_path_index: " << amap_path_index;

    if (amap_response.route().paths_size() <= amap_path_index) {
      MLOG(INFO) << "AMAP path index out of range, ignore it: "
                 << amap_path_index
                 << ", path size:" << amap_response.route().paths_size();
      return;
    }

    UtmProjectionConvert utm_projector(projection_);

    const auto& path = route.paths(amap_path_index);
    std::unordered_map<int, deeproute::map::AmapStep>
        amap_link_id_to_amap_step_gcj02;
    std::unordered_map<int, deeproute::map::AmapStep>
        amap_link_id_to_amap_step_utm;

    int step_idx = 0;
    for (const auto& step : path.steps()) {
      const int step_id = step.has_id() ? step.id() : step_idx;
      step_idx += 1;

      deeproute::map::AmapStep step_utm = step;
      for (auto i = 0; i < step.polyline_llh_size(); i++) {
        auto ll = step_utm.polyline_llh(i);
        double wgs_lon;
        double wgs_lat;
        Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
        double x;
        double y;
        utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);

        (*step_utm.mutable_polyline_llh())[i].set_lon(y);
        (*step_utm.mutable_polyline_llh())[i].set_lat(x);

        // for (auto j = 0; j < step.links_size(); j++) {
        // MLOG(INFO) << "PROCESSING AMAP ROUTE: link.id: "
        //            << step.links(j).id();

        // auto link = step_utm.mutable_links(j);
        // for (auto k = 0; k < link->polyline_llh_size(); k++) {
        //   auto ll = link->polyline_llh(k);
        //   double wgs_lon;
        //   double wgs_lat;
        //   Gcj02ToWgs84(ll.lon(), ll.lat(), &wgs_lon, &wgs_lat);
        //   double x;
        //   double y;
        //   utm_projector.LatLonToUtm(wgs_lat, wgs_lon, &x, &y);

        //   (*link->mutable_polyline_llh())[k].set_lon(x);
        //   (*link->mutable_polyline_llh())[k].set_lat(y);
        // }
        // }
      }

      amap_link_id_to_amap_step_gcj02[step_id] = step;
      amap_link_id_to_amap_step_utm[step_id] = step_utm;
    }
    amap_step_id_to_amap_step_gcj02_ = amap_link_id_to_amap_step_gcj02;
    amap_step_id_to_amap_step_utm_ = amap_link_id_to_amap_step_utm;
  }

  setting_routing_response_finished_ = true;
}

void Localizer::HandleRasMapNN(
    const std::shared_ptr<const perception::NnFrame>& rasmap_nn) {
  rasmap_nn_ = rasmap_nn;

  mm_localizer_->AddRasmapNN(*rasmap_nn);
}

void Localizer::HandleSdMapOnboard(
    const std::shared_ptr<const sd_map::SDMapOnboard>& sdmap_onboard) {
  if (sdmap_onboard == nullptr) {
    MLOG(WARN)
        << "DDMM ONBOARD SDMAP DEBUG: Sdmap onboard == nullptr, neglect.";
    return;
  }

  link_properties_event_pub_.UpdataSdMapInfo(
      sdmap_onboard->sd_map().header().map_version());

  sdmap_version_ = sdmap_onboard->sd_map().header().map_version();

  std::lock_guard<std::mutex> lock(local_sd_map_mutex_);

  if (sdmap_onboard->id() == prev_onboard_sdmap_id_) {
    MLOG(INFO) << "DDMM ONBOARD SDMAP DEBUG: same sdmap id, neglect.";
    return;
  } else if (sdmap_onboard->sd_map().links_size() == 0) {
    MLOG(INFO) << "DDMM ONBOARD SDMAP DEBUG: sdmap links size == 0, neglect. "
                  "sdmap id: "
               << sdmap_onboard->id();
    return;
  } else {
    MLOG(INFO) << "DDMM ONBOARD SDMAP DEBUG: new sdmap.";
    prev_onboard_sdmap_id_ = sdmap_onboard->id();
  }

  if (!projection_.has_latitude_deg() || !projection_.has_longitude_deg() ||
      projection_.latitude_deg() == 0 || projection_.longitude_deg() == 0) {
    MLOG(WARN) << "Projection point not set, stop processing sdmap, "
               << projection_.ShortDebugString();
    return;
  }

  const auto result = ExtractLocalSdMapFromQueriedResult(
      sdmap_onboard->sd_map(), projection_, &local_sd_map_,
      &ni_id_to_link_properties_);
  if (!result) {
    MLOG(WARN) << "DDMM ONBOARD SDMAP DEBUG: extract local sdmap failed.";
  }

  if (ddmm_shadow_mode_ != nullptr) {
    ddmm_shadow_mode_->AddLocalSdMap(local_sd_map_);
  }

  MLOG(INFO) << "DDMM ONBOARD SDMAP DEBUG: add local sdmap finished.";
}

void Localizer::HandleAmapMsg(
    const std::shared_ptr<const map_onboard_adapter::RealTimeAmap>& amap_data) {
  navigation_source_timer_report_.AddAmapMeasurements(*amap_data);

  // if (!acquire_projection_finished_) {
  //   MLOG(INFO) << "Projection point not set, stop processing amap, "
  //              << projection_.ShortDebugString();
  // }
  // if (amap_step_id_to_amap_step_utm_.empty()) {
  //   MLOG(INFO) << "AMAP LINK ID TO AMAP LINK EMPTY, IGNORE AMAP MSG.";
  //   return;
  // }
  // if (utm_projector_ == nullptr) {
  //   MLOG(INFO) << "Utm projector empty, ignore amap msg.";
  //   return;
  // }
  // if (latest_lock_on_lane_result_.time_us() == 0) {
  //   MLOG(INFO) << "Latest lock on lane result time is 0, ignore amap msg.";
  //   return;
  // }

  if (current_routing_response_graph_.empty()) {
    MLOG(INFO) << "Current routing response graph is empty, ignore amap msg.";
    return;
  }

  AmapStatus amap_status;
  // amap_status.time_us = latest_lock_on_lane_result_.time_us();
  // amap_status.curr_link_id = amap_data->curr_link_id();
  // amap_status.curr_step_id = amap_data->curr_step_id();
  // amap_status.amap_status = amap_data->navi_type();
  // amap_status.distance_to_next_link = amap_data->distance_to_next_link();
  amap_status.distance_to_next_step = amap_data->distance_to_next_step();
  // amap_status.lat_gcj02 = amap_data->curr_position().latitude();
  // amap_status.lon_gcj02 = amap_data->curr_position().longitude();
  // amap_status.is_parallel_road = amap_data->has_parallel_road();

  if (amap_status_queue_.empty()) {
    amap_status_queue_.push_back(amap_status);
  } else {
    const auto& last_amap = amap_status_queue_.back();
    if (amap_status.distance_to_next_step != last_amap.distance_to_next_step) {
      amap_status_queue_.push_back(amap_status);
    } else {
      return;
    }
  }

  // amap_status_ = amap_status;

  mm_localizer_->AddParallelRoadStatus(amap_data->has_parallel_road());
  MLOG(INFO) << "amap data amap_data->distance_to_next_step(): "
             << amap_data->distance_to_next_step();
  MLOG(INFO) << "amap_data->has_parallel_road(): "
             << amap_data->has_parallel_road();

  // amap_status_queue_.push_back(amap_status_);

  // const int amap_history_size = amap_status_queue_.size();
  // if (amap_history_size < 2) {
  //   return;
  // }

  // const auto last_amap = amap_status_queue_[amap_history_size - 1];
  // const auto second_last_amap = amap_status_queue_[amap_history_size - 2];
  // constexpr double kAmapValidTimeDiff = 5e6;
  // constexpr double kLorAmapValidTimeDiff = 3e6;
  // // 如果：
  // //  1. 相邻两个amap信息时间戳差值大于5s
  // //  2. 相邻两个amap时间戳没有递增
  // if (last_amap.time_us - second_last_amap.time_us > kAmapValidTimeDiff ||
  //     last_amap.time_us - second_last_amap.time_us < 0) {
  //   MLOG(INFO) << "AMAP time invalid, clear history amap info.";
  //   amap_status_queue_.clear();
  // } else {
  // }
}

void Localizer::HandleCompliance(
    const std::shared_ptr<const compliance::Compliance>& compliance) {
  link_properties_event_pub_.UpdateComplianceInfo(compliance);
}

void Localizer::HandleBlcStatus(
    const std::shared_ptr<const dr::operationstatus::OperationStatus>
        operation_status) {
  const auto acc_status = operation_status->acc_status().status();
  const bool self_driving_enabled =
      (dr::operationstatus::ACCStatus::ACC_NORMAL == acc_status ||
       dr::operationstatus::ACCStatus::ACC_OVERRIDE == acc_status ||
       dr::operationstatus::ACCStatus::ACC_STAND_ACTIVE == acc_status ||
       dr::operationstatus::ACCStatus::ACC_STAND_WAIT == acc_status ||
       dr::operationstatus::ACCStatus::ACC_BOM == acc_status);
  blc_not_self_driving_mode_ = !self_driving_enabled;

  MLOG(INFO) << "HANDLE BLC STATUS: blc not self driving mode: "
             << blc_not_self_driving_mode_;
}

namespace {
// create null fmm result when no routing response empty
MapMatchingResult CreateNullMatchingResult() {
  MapMatchingResult result;
  result.match_status = FMM::MM::MATCH_STATUS_NULL;
  return result;
}
}  // namespace

bool Localizer::SetProjectionPoint(const double lon_deg, const double lat_deg,
                                   const double scale_factor) {
  if (acquire_projection_finished_) {
    MLOG(ERROR) << "projection point: " << projection_.ShortDebugString()
                << " has been set, ignore this message.";
    return false;
  }

  proto::Projection projection;
  projection.set_longitude_deg(lon_deg);
  projection.set_latitude_deg(lat_deg);
  projection.set_scale_factor(scale_factor);
  projection_ = projection;
  acquire_projection_finished_ = true;

  MLOG(INFO) << "projection_: " << projection_.ShortDebugString();
  ins_adapter_ = InsAdapter(projection_, vehicle_to_imu_transform_);

  utm_projector_.reset(new UtmProjectionConvert(projection_));

  return true;
}

void Localizer::HandleRtkSensorInsMeasurementMsg(
    const std::shared_ptr<const drivers::gnss::SensorsIns>& sensors_ins) {
  if (std::abs(sensors_ins->measurement_time() - ddmm_handle_time_) <
      ::common::microPerMillSec * 100) {
    return;
  } else {
    ddmm_handle_time_ = sensors_ins->measurement_time();
  }

  // mm_confidence_estimator_.AddPose(*sensors_ins);
  navigation_source_timer_report_.AddDrNaviMeasurements(*sensors_ins);
  navigation_source_timer_report_.Report();
  gnss_span_abnormal_report_.AddDrNaviMeasurements(*sensors_ins);

  if (sensors_ins->has_type()) {
    latest_lock_on_lane_result_.set_gnss_type(sensors_ins->type());
  }
  if (sensors_ins->has_measurement_time()) {
    latest_lock_on_lane_result_.set_time_us(sensors_ins->measurement_time());
  }
  // if input gnss invalid, stop acquiring projection point
  if (sensors_ins->type() == drivers::gnss::SensorsIns::INVALID) {
    MLOG(WARN) << "Invalid sensors ins type. Ignore this message. proto: "
               << sensors_ins->ShortDebugString();
    latest_lock_on_lane_result_.clear_matched_position_wgs84();
    latest_lock_on_lane_result_.clear_matched_position_gcj02();
    latest_lock_on_lane_result_.clear_matched_heading();
    latest_lock_on_lane_result_.set_time_us(sensors_ins->measurement_time());
    latest_lock_on_lane_result_.set_request_id(routing_response_request_id_);
    latest_lock_on_lane_result_.set_status(LockOnRoadResult::GNSS_POSE_INVALID);
    auto lock_on_road_ptr = std::make_shared<LockOnRoadResult>();
    *lock_on_road_ptr = latest_lock_on_lane_result_;
    publish_lock_on_road_signal_(lock_on_road_ptr);
    return;
  }

  // if input gnss is valid, start acquiring projection point
  if (!acquire_projection_finished_) {
    common::PointLLH llh;
    if (sensors_ins->has_imu_frame_position_llh()) {
      MLOG(INFO) << "set projection pt using imu frame position llh."
                 << sensors_ins->imu_frame_position_llh().ShortDebugString();
      llh.set_lon(sensors_ins->imu_frame_position_llh().lon());
      llh.set_lat(sensors_ins->imu_frame_position_llh().lat());
      llh.set_height(sensors_ins->imu_frame_position_llh().height());
    } else if (sensors_ins->has_vehicle_frame_position_llh()) {
      MLOG(INFO)
          << "set projection pt using vehicle frame position llh."
          << sensors_ins->vehicle_frame_position_llh().ShortDebugString();
      llh.set_lon(sensors_ins->vehicle_frame_position_llh().lon());
      llh.set_lat(sensors_ins->vehicle_frame_position_llh().lat());
      llh.set_height(sensors_ins->vehicle_frame_position_llh().height());
    }
    const auto projection_pt = SingletonProjection::GetProjection(llh);
    MLOG(INFO) << "Acquired projection pt: "
               << projection_pt.ShortDebugString();

    SetProjectionPoint(projection_pt.longitude_deg(),
                       projection_pt.latitude_deg(),
                       projection_pt.scale_factor());
  }

  drivers::gnss::SensorsIns sensors_ins_copy = *sensors_ins;

  // -------------------------------------------------------------------------------------------------------
  if (sensors_ins_copy.has_imu_frame_position_llh()) {
    lor_input_pose_ =
        Vector3d(sensors_ins_copy.imu_frame_position_llh().lat(),
                 sensors_ins_copy.imu_frame_position_llh().lon(), 0);
  } else {
    lor_input_pose_ =
        Vector3d(sensors_ins_copy.vehicle_frame_position_llh().lat(),
                 sensors_ins_copy.vehicle_frame_position_llh().lon(), 0);
  }
  if (sensors_ins_copy.reference_coodinate_system() ==
      RefSystem::SensorsIns_ReferenceCoordinateSystem_WGS84) {
    double lon_gcj02, lat_gcj02;
    Wgs84ToGcj02(lor_input_pose_.y(), lor_input_pose_.x(), &lon_gcj02,
                 &lat_gcj02);
    lor_input_pose_[0] = lat_gcj02;
    lor_input_pose_[1] = lon_gcj02;
  }
  // --------------------------------------------------------------------------------------------------------

  const auto ins = ins_adapter_.GetIns(sensors_ins_copy);
  HandleRtkInsMeasurementMsg(ins);
}

void Localizer::HandleRtkInsMeasurementMsg(
    const std::shared_ptr<const drivers::gnss::Ins>& rtk) {
  ::common::Timer timer;
  if (!InsDataValid(*rtk)) {
    MLOG(INFO) << "Global pose invalid numerically.";
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_GLOBAL_POSE_INVALID);
    return;
  }

  double x = rtk->position().x();
  double y = rtk->position().y();
  double z = 0;
  double roll = rtk->euler_angles().x();
  double pitch = rtk->euler_angles().y();
  double yaw = rtk->euler_angles().z();

  const ::common::Transformation3 pose(x, y, z, roll, pitch, yaw);
  pose_horizontal_position_std_ =
      std::sqrt(rtk->position_covariance()[0] + rtk->position_covariance()[1]);

  // if timestamp not increasing, neglect.
  const double time_diff =
      (rtk->measurement_time() - prev_ins_.measurement_time()) / 1e6;
  if ((rtk_pose_.GetTranslation().norm() > 1e-3) && time_diff <= 0) {
    MLOG(WARN) << "Ins time not increasing, prev time: "
               << prev_ins_.measurement_time()
               << ", cur time: " << rtk->measurement_time();
    prev_ins_ = *rtk;
    return;
  }
  prev_ins_ = *rtk;

  // if large position change detected, neglect.
  const double delta_pos =
      (pose.GetTranslation() - rtk_pose_.GetTranslation()).norm();
  if ((rtk_pose_.GetTranslation().norm() > 1e-3) &&
      (delta_pos / time_diff) > 1e3 /*m/s*/) {
    MLOG(WARN) << "Large position change detected, Delta_pos: " << delta_pos
               << " .Current pose: x: " << x << ", y: " << y;
    rtk_pose_ = pose;
    return;
  }
  rtk_pose_ = pose;

  double horizontal_vel = 1e-6;
  if (time_diff > 1e-3) {
    horizontal_vel = delta_pos / time_diff;
  }

  double search_radius;
  if (latest_lock_on_lane_result_.gnss_type() ==
      deeproute::drivers::gnss::SensorsIns_Type_GOOD) {
    search_radius = 100;
  } else if (latest_lock_on_lane_result_.gnss_type() ==
                 deeproute::drivers::gnss::SensorsIns_Type_CONVERGING ||
             latest_lock_on_lane_result_.gnss_type() ==
                 deeproute::drivers::gnss::SensorsIns_Type_UNCONVERGE_DR) {
    search_radius = 300;
  } else {
    search_radius = 400;
  }

  // if measurements not enough, return.
  gnss_time_and_poses_queue_.push_back(
      std::make_pair(rtk->measurement_time(), rtk_pose_));

  velocities_.push_back(horizontal_vel);

  if (!gnss_time_and_poses_queue_.full() || !velocities_.full()) {
    MLOG(INFO) << "Gnss measurements not enougth!, Current size: "
               << gnss_time_and_poses_queue_.size();
    return;
  }

  Vector2dVector gnss_positions;
  std::vector<double> headings, timestamps, velocities;
  ConvertPoseToTrajectoryAndHeadings(gnss_time_and_poses_queue_,
                                     &gnss_positions, &headings, &timestamps);

  // set lock on road debug info no matter what, needed by @Hao Feng
  ///////////////////////////////////////////////////////
  LockOnRoadDebugInfo traj_debug_info;
  for (const auto& [timestamp, gnss_pose] : gnss_time_and_poses_queue_) {
    LockOnRoadSinglePoseDebugInfo debug_info;
    debug_info.set_time(timestamp);
    const auto& translation = gnss_pose.GetTranslation();
    debug_info.mutable_position()->set_x(translation.x());
    debug_info.mutable_position()->set_y(translation.y());
    debug_info.mutable_position()->set_z(translation.z());
    const auto& roll_pitch_yaw = gnss_pose.GetRollPitchYaw();
    debug_info.mutable_euler_angles()->set_x(roll_pitch_yaw.x());
    debug_info.mutable_euler_angles()->set_y(roll_pitch_yaw.y());
    debug_info.mutable_euler_angles()->set_z(roll_pitch_yaw.z());
    debug_info.set_gnss_type(gnss_type_);
    debug_info.set_obs_lane_index(obs_lane_index_);
    debug_info.set_obs_lane_sum(obs_lane_sum_);

    *(traj_debug_info.add_pose_debug_info()) = debug_info;
  }
  latest_lock_on_lane_debug_ = traj_debug_info;

  // NOTE(shr) this part
  if (!initialized_) {
    initialized_ = true;
  }

  velocities = ConvertToVector(velocities_);

  // if no routing response, return.
  constexpr int kRoutingResponseProcessInterval = 1000;
  if (current_routing_response_graph_.empty()) {
    MLOG_EVERY(WARN, kRoutingResponseProcessInterval)
        << "No routing response received so far!";
    NotifyLockOnRoadUpdate(CreateNullMatchingResult(),
                           routing_response_request_id_,
                           rtk->measurement_time());

    RunDataDrivenMapMatching(rtk->measurement_time());
    latest_ddmm_lock_on_lane_result_.set_time_us(rtk->measurement_time());
    return;
  } else {
    MLOG_EVERY(WARN, kRoutingResponseProcessInterval)
        << "Has valid routing response!";
  }
  const uint64_t rtk_time = rtk->measurement_time();
  RunDataDrivenMapMatching(rtk_time);

  // run LOR
  min_distance_to_routing_response_ =
      MinDistanceToCustomGraph(Vector2d(x, y), current_routing_response_graph_);
  MLOG_EVERY(INFO, 10) << "LOR, search_radius: " << search_radius
                       << ", min_dist_to_routing_response: "
                       << min_distance_to_routing_response_;

  // clang-format off
  MapMatchingResult result;
  PointCandidates ref_candidates;
  if (!mm_localizer_->Match(rtk,
                            gnss_positions,
                            projection_,
                            headings,
                            timestamps,
                            velocities,
                            /*radius*/ search_radius,
                            /*nerghbor num*/ neigbhor_num_,
                            /*gps_error*/ obs_noise_,
                            /*heading error*/ heading_noise_,
                            /*translation diff error*/ transition_noise_,
                            gnss_type_,
                            &result,
                            &ref_candidates
                            )) {
    MLOG_EVERY(WARN, 100) << "Matching failed for current trajectory!";

    mm_localizer_ = CreateStMapMatchingModel(config_.map_matching_config());
    mm_localizer_->SetGlobalPlannedRoutes(current_routing_response_graph_);

    lock_on_road_succeeded_ = false;
    NotifyLockOnRoadUpdate(result, routing_response_request_id_, rtk->measurement_time());
    return;
  }
  lock_on_road_succeeded_ = true;
  // clang-format on

  // MATCH_STATUS_NULL = 0,
  // /// 1-match normal
  // MATCH_STATUS_NORMAL,
  // /// 2-match yaw
  // MATCH_STATUS_YAW,
  // MATCH_STATUS_PASSOVER,
  scenario_handler_.Add(latest_lock_on_lane_result_.gnss_type(),
                        sd_link_is_tunnel_, pose.GetTranslation(),
                        pose_horizontal_position_std_);
  if (scenario_handler_.IsInTunnel()) {
    MLOG_EVERY(INFO, 100)
        << "Vehicle In tunnel . Disable YAW status. gnss type: "
        << latest_lock_on_lane_result_.gnss_type()
        << ", sd_link_is_tunnel: " << sd_link_is_tunnel_
        << ", pose_horizontal_position_std_:" << pose_horizontal_position_std_
        << ", raw result.match_status: " << result.match_status;
    result.match_status = FMM::MM::MATCH_STATUS_NORMAL;
  } else if (scenario_handler_.DistanceToLastTunnelPose(pose.GetTranslation()) <
                 500 &&
             !scenario_handler_.GlobalPoseAccurate()) {
    MLOG_EVERY(INFO, 100)
        << "Vehicle near tunnel . Disable YAW status. gnss type: "
        << latest_lock_on_lane_result_.gnss_type()
        << ", sd_link_is_tunnel: " << sd_link_is_tunnel_
        << ", pose_horizontal_position_std_:" << pose_horizontal_position_std_
        << ", DistanceToLastTunnelPose(pose.GetTranslation()): "
        << scenario_handler_.DistanceToLastTunnelPose(pose.GetTranslation());
    result.match_status = FMM::MM::MATCH_STATUS_NORMAL;
  }

  const FMM::MM::MatchedCandidate pose_matching_result =
      result.opt_candidate_path.back();
  NotifyLockOnRoadUpdate(result, routing_response_request_id_,
                         rtk->measurement_time());
  if (FMM::MM::MATCH_STATUS_YAW == result.match_status) {
    if (FMM::MM::MATCH_STATUS_YAW == result.match_status) {
      // common::ReportEvent(dr::common::LOCK_ON_ROAD,
      // dr::common::LOCK_ON_ROAD_VEHICLE_YAWED);
    }
  }

  // set lor debug info matched position
  for (int i = 0; i < latest_lock_on_lane_debug_.pose_debug_info_size(); i++) {
    const FMM::MM::MatchedCandidate pose_matching_result =
        result.opt_candidate_path[i];
    const Vector2d geo_referenced_point(
        boost::geometry::get<0>(pose_matching_result.c.point),
        boost::geometry::get<1>(pose_matching_result.c.point));
    latest_lock_on_lane_debug_.mutable_pose_debug_info(i)
        ->mutable_matched_position()
        ->set_x(geo_referenced_point.x());
    latest_lock_on_lane_debug_.mutable_pose_debug_info(i)
        ->mutable_matched_position()
        ->set_y(geo_referenced_point.y());
    latest_lock_on_lane_debug_.mutable_pose_debug_info(i)
        ->mutable_matched_position()
        ->set_z(1);
  }

  auto lock_on_road_debug_info_ptr = std::make_shared<LockOnRoadDebugInfo>();
  *lock_on_road_debug_info_ptr = latest_lock_on_lane_debug_;
  publish_lock_on_road_debug_info_signal_(lock_on_road_debug_info_ptr);

  if (link_id_to_link_info_.find(latest_lock_on_lane_result_.sd_link_id()) !=
      link_id_to_link_info_.end()) {
    sd_lane_sum_ =
        link_id_to_link_info_[latest_lock_on_lane_result_.sd_link_id()]
            .f_lane_num();
    sd_link_is_tunnel_ =
        link_id_to_link_info_[latest_lock_on_lane_result_.sd_link_id()]
            .tunnel();
  } else {
    MLOG(WARN) << "Cannot find result sd link in dict, this shouldn't happen.";
  }

  // clang-format off
  constexpr int kInsProcessInterval = 50;
  MLOG_EVERY(INFO, kInsProcessInterval) << "time: " << rtk->measurement_time()
             << ", Pos: " << x << " "<< y
             << ", Heading: " << pose.GetRollPitchYaw().transpose()[2]
             << ", Pos horizontal std: " << pose_horizontal_position_std_
             << ", lock on road status: "<< LockOnRoadResult::Status(result.match_status)
             << ", lock on road sd link index: "<< (latest_lock_on_lane_result_.has_sd_link_index() ? std::to_string(latest_lock_on_lane_result_.sd_link_index()):"-1")
             << ", min dist to routing: "<< min_distance_to_routing_response_
             << ", search radius: " << search_radius
             << ", gnss type: " << gnss_type_
             << ", latest_lock_on_lane_result_.gnss_type(): "<<latest_lock_on_lane_result_.gnss_type()
             << ", lane index est type: "
             << LaneIndexEstimationResult::Type_descriptor()
                    ->FindValueByNumber(latest_lane_index_result_.type())
                    ->name()
             << ", routing id: " << routing_response_request_id_
             << ", sd lane num: " << sd_lane_sum_
             <<", sd link id tunnel?:"<< sd_link_is_tunnel_
             <<", dist to tunnel end: "<<scenario_handler_.DistanceToLastTunnelPose(pose.GetTranslation())
             <<", scenario_handler_.GlobalPoseAccurate(): "<<scenario_handler_.GlobalPoseAccurate();
  // clang-format on
  MLOG_TRACE("CalcLockOnRoadResult", timer.EndMilli());
}

void Localizer::NotifyLockOnRoadUpdate(const MapMatchingResult& result,
                                       const std::string& request_id,
                                       const uint64_t tick_time) {
  latest_lock_on_lane_result_.set_time_us(tick_time);
  latest_lock_on_lane_result_.set_request_id(request_id);
  latest_lock_on_lane_result_.set_status(
      LockOnRoadResult::Status(result.match_status));
  double pos[3];
  double azi = 0.0;

  if (FMM::MM::MATCH_STATUS_YAW == result.match_status ||
      FMM::MM::MATCH_STATUS_NULL == result.match_status) {
    latest_lock_on_lane_result_.set_sd_link_id("0");
    latest_lock_on_lane_result_.set_dr_link_id(0);
    latest_lock_on_lane_result_.set_sd_link_index(-1);
    latest_lock_on_lane_result_.set_sd_distance_to_link_start(0);
    latest_lock_on_lane_result_.set_sd_distance_to_link_end(0);

    const Vector3d rpy = rtk_pose_.GetRollPitchYaw();
    const double yaw = rpy[2];
    // 北为0, 顺时针为正, 0->2pi.
    const double shifted_yaw = yaw > 0 ? yaw : yaw + 2 * M_PI;
    azi = shifted_yaw;
    pos[0] = rtk_pose_.GetTranslation().x();
    pos[1] = rtk_pose_.GetTranslation().y();
    pos[2] = rtk_pose_.GetTranslation().z();
  } else {
    const FMM::MM::MatchedCandidate& pose_matching_result =
        result.opt_candidate_path.back();
    const double linestring_length = pose_matching_result.c.edge->length;
    const double distance_to_link_start = pose_matching_result.c.offset;
    const double distance_to_link_end =
        linestring_length - pose_matching_result.c.offset;

    constexpr double kPoseToRouteStartDistThres = 20;
    if (pose_matching_result.c.edge->index == 0 &&
        distance_to_link_start == 0 &&
        pose_matching_result.c.dist > kPoseToRouteStartDistThres) {
      MLOG(INFO) << "LOR: at the 1st link, distance to link start is 0, and "
                    "dist to matched pt distance>30m, "
                    "set to YAWED.";
      // if matched to 1st link and offset is 0, then we are at the start of the
      // route, do not enable NCA
      //   x
      //    \
      //     \
      //      \
      //       o----------1st link---------o
      latest_lock_on_lane_result_.set_status(LockOnRoadResult::YAWED);
      latest_lock_on_lane_result_.set_sd_link_id("0");
      latest_lock_on_lane_result_.set_dr_link_id(0);
      latest_lock_on_lane_result_.set_sd_link_index(-1);
      latest_lock_on_lane_result_.set_sd_distance_to_link_start(0);
      latest_lock_on_lane_result_.set_sd_distance_to_link_end(0);

      const Vector3d rpy = rtk_pose_.GetRollPitchYaw();
      const double yaw = rpy[2];
      const double shifted_yaw = yaw > 0 ? yaw : yaw + 2 * M_PI;
      azi = shifted_yaw;
      pos[0] = rtk_pose_.GetTranslation().x();
      pos[1] = rtk_pose_.GetTranslation().y();
      pos[2] = rtk_pose_.GetTranslation().z();
    } else if (pose_matching_result.c.edge->index + 1 ==
                   current_routing_response_graph_.size() &&
               distance_to_link_end == 0) {
      MLOG(INFO)
          << "LOR: at the last link, and distance to link end is 0, set YAWED";

      // if matched to the last link and remaining distance is 0, then we are at
      // the end of the route, do not enable NCA
      //                                        x
      //                                       /
      //                                      /
      //                                     /
      //       o----------last link---------o
      latest_lock_on_lane_result_.set_status(LockOnRoadResult::YAWED);
      latest_lock_on_lane_result_.set_sd_link_id("0");
      latest_lock_on_lane_result_.set_dr_link_id(0);
      latest_lock_on_lane_result_.set_sd_link_index(-1);
      latest_lock_on_lane_result_.set_sd_distance_to_link_start(0);
      latest_lock_on_lane_result_.set_sd_distance_to_link_end(0);

      const Vector3d rpy = rtk_pose_.GetRollPitchYaw();
      const double yaw = rpy[2];
      const double shifted_yaw = yaw > 0 ? yaw : yaw + 2 * M_PI;
      azi = shifted_yaw;
      pos[0] = rtk_pose_.GetTranslation().x();
      pos[1] = rtk_pose_.GetTranslation().y();
      pos[2] = rtk_pose_.GetTranslation().z();
    } else {
      MLOG_EVERY(INFO, 100)
          << "LOR: at the " << pose_matching_result.c.edge->index << " link.";

      // input routing response contains UINT64_T link id, during calculation,
      // it is transformed to int64_t so the returned link id is int64_t, we
      // need to convert it back to UINT64_T before converting to string.
      // latest_lock_on_lane_result_.set_sd_link_id(
      // std::to_string(pose_matching_result.c.edge->id));
      const uint64_t link_id_ULL =
          static_cast<uint64_t>(pose_matching_result.c.edge->id);
      latest_lock_on_lane_result_.set_sd_link_id(std::to_string(link_id_ULL));
      latest_lock_on_lane_result_.set_dr_link_id(link_id_ULL);

      latest_lock_on_lane_result_.set_sd_link_index(
          pose_matching_result.c.edge->index);
      latest_lock_on_lane_result_.set_sd_distance_to_link_start(
          distance_to_link_start);
      latest_lock_on_lane_result_.set_sd_distance_to_link_end(
          distance_to_link_end);

      azi = pose_matching_result.c.edge_heading;
      const FMM::MM::CandidateInfo& info =
          pose_matching_result.c
              .candidate_infos[pose_matching_result.c.candidate_index];
      pos[0] = boost::geometry::get<0>(info.point);
      pos[1] = boost::geometry::get<1>(info.point);
      pos[2] = 0.0;
    }
  }

  double lon = 0.0;
  double lat = 0.0;
  ProjectionTransformation proj(projection_);
  proj.UtmToLatLon(pos[0], pos[1], &lat, &lon);
  latest_lock_on_lane_result_.mutable_matched_position_wgs84()->set_x(lon);
  latest_lock_on_lane_result_.mutable_matched_position_wgs84()->set_y(lat);
  latest_lock_on_lane_result_.mutable_matched_position_wgs84()->set_z(pos[2]);

  double lon_gcj02 = 0;
  double lat_gcj02 = 0;
  Wgs84ToGcj02(lon, lat, &lon_gcj02, &lat_gcj02);
  latest_lock_on_lane_result_.mutable_matched_position_gcj02()->set_x(
      lon_gcj02);
  latest_lock_on_lane_result_.mutable_matched_position_gcj02()->set_y(
      lat_gcj02);
  latest_lock_on_lane_result_.mutable_matched_position_gcj02()->set_z(pos[2]);

  /// anticlockwise from true east to clockwise from true north
  azi = FMM::ALGORITHM::regulate_azi(450.0 - azi * 180.0 / M_PI);
  latest_lock_on_lane_result_.set_matched_heading(azi);

  if (ni_id_to_link_properties_.find(
          latest_lock_on_lane_result_.dr_link_id()) !=
      ni_id_to_link_properties_.end()) {
    const auto& link_properties =
        ni_id_to_link_properties_[latest_lock_on_lane_result_.dr_link_id()];
    latest_lock_on_lane_result_.mutable_link_properties()->set_priority(
        link_properties.priority());
    latest_lock_on_lane_result_.mutable_link_properties()->set_tunnel(
        link_properties.tunnel());
    latest_lock_on_lane_result_.mutable_link_properties()->set_elevated(
        link_properties.elevated());
  } else {
    MLOG(WARN) << "LOR: Link Id: " << latest_lock_on_lane_result_.sd_link_id()
               << " not found in local sdmap. dict size: "
               << ni_id_to_link_properties_.size();
  }

  // if current link id is in link_idx_to_segm_idx, set route seg idx
  if (link_idx_to_route_seg_idx_.find(
          latest_lock_on_lane_result_.sd_link_index()) !=
      link_idx_to_route_seg_idx_.end()) {
    const auto route_seg_idx =
        link_idx_to_route_seg_idx_[latest_lock_on_lane_result_.sd_link_index()];
    latest_lock_on_lane_result_.set_route_seg_idx(route_seg_idx);
  } else {
    MLOG(INFO)
        << "LOR DEBUG: link_idx_to_route_seg_idx_ not found for link index: "
        << latest_lock_on_lane_result_.sd_link_index();
  }

  // if current link id is VIRTUAL LINK, se YAW so that NCA -> LCC
  // if latest_lock_on_lane_result_.sd_link_id() is in virtual_links_ids_
  // set YAW
  if (std::find(virtual_links_ids_.begin(), virtual_links_ids_.end(),
                latest_lock_on_lane_result_.sd_link_id()) !=
      virtual_links_ids_.end()) {
    MLOG(INFO) << "LOR DEBUG: current link is virtual link, set YAW. "
                  "sd_link_id: "
               << latest_lock_on_lane_result_.sd_link_id();
    latest_lock_on_lane_result_.set_status(LockOnRoadResult::YAWED);
  }

  // re-init link index based on current route segment
  const auto current_link_index = latest_lock_on_lane_result_.sd_link_index();
  int current_route_segment_start_index = -1;
  for (const auto virtual_link_index : virtual_link_indices_) {
    if (virtual_link_index <= current_link_index) {
      current_route_segment_start_index = virtual_link_index;
    }
  }

  // 0th link is virtual,  we are on 0st link:    YAW,  link idx == -1
  // 0th link is virtual,  we are on 1st link: NORMAL,  link idx ==  0
  // 0th link is !Virtual, we are on 0th link: NORMAL,  link idx ==  0
  // 0th link is !Virtual, we are on 1st link: NORMAL,  link idx ==  1
  // 5th link is virtual,  we are on 5th link:    YAW,  link idx == -1
  // 5th link is virtual,  we are on 6th link: NORMAL,  link idx ==  0
  const auto link_idx_from_current_segm =
      current_link_index - current_route_segment_start_index - 1;

  MLOG(INFO) << "current_route_segment_start_index: "
             << current_route_segment_start_index
             << ", current_link_index: " << current_link_index
             << ", new link index: " << link_idx_from_current_segm
             << ", status: " << latest_lock_on_lane_result_.status();
  latest_lock_on_lane_result_.set_sd_link_index(link_idx_from_current_segm);

  // LogUtil::GetInstance()->RecordLOR(&latest_lock_on_lane_result_);
  auto lock_on_road_ptr = std::make_shared<LockOnRoadResult>();
  *lock_on_road_ptr = latest_lock_on_lane_result_;
  publish_lock_on_road_signal_(lock_on_road_ptr);

  // for ddmm shadowmode
  if (enable_ddmm_) {
    ddmm_shadow_mode_->AddLockOnRoadResult(latest_lock_on_lane_result_);
  }
}

// Note this measurement is in local frame.
void Localizer::HandleOdometryMeasurementMsg(
    const std::shared_ptr<const drivers::gnss::Ins>& odom) {
  ::common::Transformation3 odom_pose(
      odom->position().x(), odom->position().y(), odom->position().z(),
      odom->euler_angles().x(), odom->euler_angles().y(),
      odom->euler_angles().z());

  if (!IsPoseValid(odom_pose)) {
    MLOG(INFO) << "odom pose invalid, neglect.";
    return;
  }

  gnss_span_abnormal_report_.AddOdomMeasurements(odom_pose);
  odom_time_and_poses_queue_.push_back(
      std::make_pair(odom->measurement_time(), odom_pose));
  odom_pos_interpolator_.InsertValue(
      odom->measurement_time(),
      Vector3d(odom->position().x(), odom->position().y(),
               odom->position().z()));

  odom_pose_ = odom_pose;

  Vector3d flu_vel(odom->linear_velocity_flu().x(),
                   odom->linear_velocity_flu().y(),
                   odom->linear_velocity_flu().z());
  odom_time_and_flu_vel_queue_.push_back(
      std::make_pair(odom->measurement_time(), flu_vel));

  const Vector2d pos_xy =
      Vector2d(odom_pose.GetTranslation()[0], odom_pose.GetTranslation()[1]);

  // if ego lane exists and valid
  constexpr double kMinEgoLaneLen = 20;
  if (ego_lane_.point_size() > 2 && Length(ego_lane_) > kMinEgoLaneLen) {
    const double dist_to_ego_lane = Distance(pos_xy, ego_lane_);
    const double signed_distance_to_ego_lane =
        IsLineToTheRight(ego_lane_, odom_pose) ? dist_to_ego_lane
                                               : -dist_to_ego_lane;

    if (config_.lane_estimator_config().use_odom_measurement()) {
      lane_estimator_->AddDistToCenterline(odom->measurement_time(),
                                           signed_distance_to_ego_lane);
    }
  }
}

namespace {
void SetSdmapVersionAndType(
    LockOnRoadResult& lor, const std::string& sdmap_version,
    const LockOnRoadResult_LinkProperties_SdMapSourceType& source_type) {
  if (!sdmap_version.empty()) {
    lor.mutable_link_properties()->set_sdmap_version(sdmap_version);
    lor.mutable_link_properties()->set_sdmap_source_type(source_type);
  }
}
}  // namespace

LockOnRoadResult Localizer::GetLatestLockOnRoadResult() {
  if (!latest_lock_on_lane_result_.navi_status_internal_size()) {
    LockOnRoadResult::NaviStatus default_navi_status;
    default_navi_status.set_source(
        LockOnRoadResult_NaviStatus_Source_LOCK_ON_ROAD);
    latest_lock_on_lane_result_.add_navi_status_internal()->CopyFrom(
        default_navi_status);
  }
  latest_lock_on_lane_result_.mutable_navi_status_internal(0)->set_status(
      latest_lock_on_lane_result_.status());

  SetSdmapVersionAndType(latest_lock_on_lane_result_, sdmap_version_,
                         LockOnRoadResult_LinkProperties::TENCENT_PLUS);

  // if LOR normal, feed LOR status
  if (latest_lock_on_lane_result_.status() == LockOnRoadResult::NORMAL ||
      latest_lock_on_lane_result_.status() == LockOnRoadResult::PASSOVER) {
    routing_scene_type_ = UNKNOWN;

    auto it =
        link_id_to_link_info_.find(latest_lock_on_lane_result_.sd_link_id());
    if (it == link_id_to_link_info_.end()) {
      mm_localizer_->AddRoutingSceneType(routing_scene_type_);
      return latest_lock_on_lane_result_;
    }

    const auto& link = it->second;

    mm_localizer_->AddLinkPR(link.pr());

    for (const auto& formway : link.formways()) {
      MLOG(INFO) << "FORMWAY: "
                 << deeproute::sd_map::LinkData_FormWay_Name(formway);

      if (formway == deeproute::sd_map::LinkData::ROAD_KIND_MAIN_ROAD) {
        routing_scene_type_ = MAIN_ROAD;
        break;
      }
      if (formway == deeproute::sd_map::LinkData::ROAD_KIND_SECONDARY) {
        routing_scene_type_ = SECONDARY_ROAD;
        break;
      }
      if (formway ==
          deeproute::sd_map::LinkData::ROAD_KIND_MAIN_SECONDARY_ENTRANCE) {
        routing_scene_type_ = MAIN_SECONDARY_ENTRANCE;
        break;
      }
      if (formway == deeproute::sd_map::LinkData::ROAD_KIND_INTERSECTION ||
          formway == deeproute::sd_map::LinkData::ROAD_KIND_INNER_CROSS_ROAD ||
          formway ==
              deeproute::sd_map::LinkData::ROAD_KIND_SPECIAL_CONNECTION ||
          formway ==
              deeproute::sd_map::LinkData::ROAD_KIND_INNER_VIRTUAL_CONNECT
          // || formway ==
          // deeproute::sd_map::LinkData::ROAD_KIND_ENTRANCE_AND_EXIT_CONNECT
      ) {
        routing_scene_type_ = JUNCTION;
        break;
      }
    }

    mm_localizer_->AddRoutingSceneType(routing_scene_type_);
  }

  if (latest_lock_on_lane_result_.status() == LockOnRoadResult::YAWED) {
    switch (routing_scene_type_) {
      case MAIN_ROAD:
        latest_lock_on_lane_result_.set_yaw_scene_type(
            LockOnRoadResult::ROUTING_MAIN_ROAD_VEHICLE_SECONDARY_ROAD);
        break;
      case SECONDARY_ROAD:
        latest_lock_on_lane_result_.set_yaw_scene_type(
            LockOnRoadResult::ROUTING_SECONDARY_ROAD_VEHICLE_MAIN_ROAD);
        break;
      case UNKNOWN:
      case MAIN_SECONDARY_ENTRANCE:
      case JUNCTION:
      default:
        latest_lock_on_lane_result_.set_yaw_scene_type(
            LockOnRoadResult::UNKNOWN);
        break;
    }
  }

  // if BLC not at self driving mode, clear LOR status and Yaw scene type
  if (blc_not_self_driving_mode_) {
    MLOG(INFO) << "blc_not_self_driving_mode_: " << blc_not_self_driving_mode_
               << ", clear LOR status and YAW SCENE TYPE";
    latest_lock_on_lane_result_.set_yaw_scene_type(LockOnRoadResult::UNKNOWN);
    mm_localizer_->ResetRoutingMaskYawDecider();
  }

  return latest_lock_on_lane_result_;
}

LaneIndexEstimationResult Localizer::GetLatestLaneIndexEstimationResult() {
  return latest_lane_index_result_;
}

LockOnRoadDebugInfo Localizer::GetLatestLockOnLaneDebugInfo() {
  return latest_lock_on_lane_debug_;
}
perception::RASMap Localizer::GetDebugRasMap() { return debug_ras_map_; }

LockOnRoadResult Localizer::GetLatestDdmmResult() {
  SetSdmapVersionAndType(latest_ddmm_lock_on_lane_result_, sdmap_version_,
                         LockOnRoadResult_LinkProperties::TENCENT_PLUS);

  return latest_ddmm_lock_on_lane_result_;
};

const LockOnRoadDdmmDebugInfo& Localizer::GetLatestDdmmDebugInfo() {
  return latest_ddmm_debug_info_;
}

namespace {

// Most JIRAs happens during junction stop, where road is packed with vehicles
void VehicleStaticCompensation(
    const boost::circular_buffer<int>& obs_lane_index_history,
    const boost::circular_buffer<int>& obs_lane_sum_history,
    int* compensated_lane_index, int* compensated_lane_sum,
    ClosestCurbStatus* compensated_closest_curb_status) {
  std::map<int, int> index_counters, sum_counters;
  for (auto index : obs_lane_index_history) {
    index_counters[index] += 1;
  }
  for (auto sum : obs_lane_sum_history) {
    sum_counters[sum] += 1;
  }

  int most_occurred_lane_index = -1;
  int most_occured_lane_index_num = 0;
  int most_occurred_lane_sum = -1;
  int most_occured_lane_sum_num = 0;
  for (const auto& index_counter : index_counters) {
    if (index_counter.second > most_occured_lane_index_num) {
      most_occured_lane_index_num = index_counter.second;
      most_occurred_lane_index = index_counter.first;
    }
  }

  for (const auto& sum_counter : sum_counters) {
    if (sum_counter.second > most_occured_lane_sum_num) {
      most_occured_lane_sum_num = sum_counter.second;
      most_occurred_lane_sum = sum_counter.first;
    }
  }

  *compensated_lane_index = most_occurred_lane_index;
  *compensated_lane_sum = most_occurred_lane_sum;

  const int right_lane_sum = most_occurred_lane_sum - most_occurred_lane_index;
  *compensated_closest_curb_status = right_lane_sum < most_occurred_lane_index
                                         ? ClosestCurbStatus::RIGHT
                                         : ClosestCurbStatus::LEFT;
}

bool VehicleStatic(const boost::circular_buffer<TimeToPosePair>& poses,
                   double* delta_position) {
  if (poses.empty()) {
    return false;
  }

  *delta_position =
      (poses[0].second.GetTranslation() - poses.back().second.GetTranslation())
          .norm();
  // MLOG(INFO) << "pos diff: " << pos_diff;
  return *delta_position < 1e-2;
}
}  // namespace

void Localizer::RunDataDrivenMapMatching(const uint64_t time) {
  if (!enable_ddmm_) {
    MLOG_EVERY(WARN, 1000) << "DDMM disabled.";
    return;
  } else {
    MLOG_EVERY(WARN, 1000) << "DDMM enabled.";
  }

#ifdef DR_NVIDIA
  if (!projection_.has_latitude_deg() || !projection_.has_longitude_deg() ||
      projection_.latitude_deg() == 0 || projection_.longitude_deg() == 0) {
    MLOG_EVERY(WARN, 100) << "Skip executing ddmm, projection not set :"
                          << projection_.ShortDebugString();
    return;
  }

  ::common::Timer timer;
  LockOnRoadResult ddmm_result;
  const std::string latest_lock_on_road_link_id =
      latest_lock_on_lane_result_.sd_link_id();
  std::lock_guard<std::mutex> lock(local_sd_map_mutex_);

  ddmm_localizer_->Match(time, prev_ins_, local_sd_map_, *rasmap_nn_,
                         latest_lock_on_road_link_id, latest_lane_index_raw_,
                         ddmm_result, latest_ddmm_result_debug_image_,
                         latest_ddmm_debug_info_);

  if (ddmm_localizer_->ValidFrame()) {
    ddmm_shadow_mode_->AddDdmmResult(ddmm_result);
    latest_ddmm_lock_on_lane_result_ = ddmm_shadow_mode_->GetShadowModeResult();
  } else {
    latest_ddmm_lock_on_lane_result_ = ddmm_result;
  }

  const double utm_x =
      latest_ddmm_lock_on_lane_result_.matched_position_wgs84().x();
  const double utm_y =
      latest_ddmm_lock_on_lane_result_.matched_position_wgs84().y();
  double lon = 0.0;
  double lat = 0.0;
  ProjectionTransformation proj(projection_);
  proj.UtmToLatLon(utm_x, utm_y, &lat, &lon);
  latest_ddmm_lock_on_lane_result_.mutable_matched_position_wgs84()->set_x(lon);
  latest_ddmm_lock_on_lane_result_.mutable_matched_position_wgs84()->set_y(lat);

  double lon_gcj02 = 0;
  double lat_gcj02 = 0;
  Wgs84ToGcj02(lon, lat, &lon_gcj02, &lat_gcj02);
  latest_ddmm_lock_on_lane_result_.mutable_matched_position_gcj02()->set_x(
      lon_gcj02);
  latest_ddmm_lock_on_lane_result_.mutable_matched_position_gcj02()->set_y(
      lat_gcj02);

  if (!ddmm_debug_out_dir_.empty()) {
    const std::string seq_results = ddmm_debug_out_dir_ + "/" + "sequence";
    if (!boost::filesystem::exists(seq_results)) {
      boost::filesystem::create_directories(seq_results);
    }
    const std::string file_name =
        seq_results + "/" +
        std::to_string(latest_ddmm_lock_on_lane_result_.time_us()) + ".jpg";

    if (latest_ddmm_result_debug_image_.size().width > 10) {
      cv::imwrite(file_name, latest_ddmm_result_debug_image_);
    } else {
      MLOG(WARN) << "DDMM DEBUG latest_ddmm_result_debug_image_ is empty.";
    }
  }

  if (!latest_ddmm_lock_on_lane_result_.has_sd_link_id()) {
    return;
  }

  // as requested by @jingwei, I need to set this field in both lor and lom.
  if (!sdmap_version_.empty()) {
    latest_ddmm_lock_on_lane_result_.mutable_link_properties()
        ->set_sdmap_version(sdmap_version_);
    latest_ddmm_lock_on_lane_result_.mutable_link_properties()
        ->set_sdmap_source_type(LockOnRoadResult_LinkProperties::TENCENT_PLUS);
  }

  if (ni_id_to_link_properties_.find(
          std::stoull(latest_ddmm_lock_on_lane_result_.sd_link_id())) !=
      ni_id_to_link_properties_.end()) {
    const auto& link_properties = ni_id_to_link_properties_[std::stoull(
        latest_ddmm_lock_on_lane_result_.sd_link_id())];
    latest_ddmm_lock_on_lane_result_.mutable_link_properties()->set_priority(
        link_properties.priority());
    latest_ddmm_lock_on_lane_result_.mutable_link_properties()->set_tunnel(
        link_properties.tunnel());
    latest_ddmm_lock_on_lane_result_.mutable_link_properties()->set_elevated(
        link_properties.elevated());
  } else {
    MLOG(WARN) << "LOR: Link Id: " << latest_lock_on_lane_result_.sd_link_id()
               << " not found in local sdmap. dict size: "
               << ni_id_to_link_properties_.size();
  }

  link_properties_event_pub_.Update(latest_lock_on_lane_result_,
                                    latest_ddmm_lock_on_lane_result_);

  MLOG(INFO) << "LOM DEBUG: "
             << latest_ddmm_lock_on_lane_result_.ShortDebugString();
  MLOG_TRACE("CalcDdmm", timer.EndMilli());

#endif
}

AmapStatus Localizer::GetLatestAmapStatus() { return amap_status_; }

Vector3d Localizer::GetLorInputPoseBlhGcj02() {
  // MLOG(INFO) << "LOR INPUT POSE :" << lor_input_pose_.transpose();
  return lor_input_pose_;
}

void Localizer::LaneIndexEstimation(
    const std::shared_ptr<const perception::RASMap>& ras_map,
    LaneIndexEstimationResult& latest_lane_index_result) {
  //-------------------------------------------------------------------
  // process ras map, extract ego lane index and lane num
  int32_t obs_lane_index = -1;
  int32_t obs_lane_sum = -1;
  double dist_to_egolane = -1;
  deeproute::perception::RASMap processed_ras_map;
  ClosestCurbStatus closest_curb_status = ClosestCurbStatus::NO_CURB;
  double closest_curb_to_its_neighbor_lane_dist =
      std::numeric_limits<double>::max();
  std::vector<int32_t> ordered_left_to_right_lane_ids;
  bool succeeded = false;
  if (config_.lane_estimator_config().process_with_topology()) {
    succeeded = ProcessRasMapByTopology(
        odom_pose_, ras_map, &obs_lane_index, &obs_lane_sum, &ras_ego_lane_id_,
        &ras_map_time_, &dist_to_egolane, &closest_curb_status,
        &closest_curb_to_its_neighbor_lane_dist, &processed_ras_map);

  } else {
    // this branch is no suppose to run, but just in case
    succeeded = ProcessRasMapByRefLine(
        odom_pose_, ras_map, prev_closest_curb_, &obs_lane_index, &obs_lane_sum,
        &ras_ego_lane_id_, &ras_map_time_, &dist_to_egolane,
        &closest_curb_status, &closest_curb_to_its_neighbor_lane_dist,
        &ordered_left_to_right_lane_ids, &processed_ras_map);
  }

  MLOG(INFO) << __func__ << " update rasmap time to " << ras_map_time_;
  latest_lane_index_result.set_ras_map_time(ras_map_time_);
  switch (closest_curb_status) {
    case ClosestCurbStatus::LEFT:
      latest_lane_index_result.set_closest_curb(
          LaneIndexEstimationResult_ClosestCurb_LEFT);
      break;
    case ClosestCurbStatus::RIGHT:
      latest_lane_index_result.set_closest_curb(
          LaneIndexEstimationResult_ClosestCurb_RIGHT);
      break;
    case ClosestCurbStatus::NO_CURB:
      latest_lane_index_result.set_closest_curb(
          LaneIndexEstimationResult_ClosestCurb_NO_CURB);
      break;
  }

  // assign ego lane centerline to ego lane
  if (succeeded) {
    for (const auto& lane : ras_map->lanes()) {
      if (lane.id() == ras_ego_lane_id_) {
        ego_lane_ = lane.centerline();
      }
    }
  }

  // if no curb detected for this frame, use previous curb status
  if (succeeded && closest_curb_status != ClosestCurbStatus::NO_CURB) {
    prev_closest_curb_ = closest_curb_status;
  }

  // if process ras map failed, no need to continue.
  if (!succeeded) {
    latest_lane_index_result.set_type(
        LaneIndexEstimationResult::Type::
            LaneIndexEstimationResult_Type_INVALID);
    auto lane_index_result_ptr = std::make_shared<LaneIndexEstimationResult>();
    *lane_index_result_ptr = latest_lane_index_result;
    publish_lane_index_signal_(lane_index_result_ptr);
    MLOG_EVERY(WARN, 100) << "Process ras map failed with proto: "
                          << latest_lane_index_result.ShortDebugString();
    return;
  }

  // enable a long window if and only if vehicle is static
  double delta_position = -1;
  const bool vehicle_static =
      VehicleStatic(odom_time_and_poses_queue_, &delta_position);
  if (succeeded && vehicle_static) {
    obs_lane_index_history_.push_back(obs_lane_index);
    obs_lane_sum_history_.push_back(obs_lane_sum);
    VehicleStaticCompensation(obs_lane_index_history_, obs_lane_sum_history_,
                              &obs_lane_index, &obs_lane_sum,
                              &closest_curb_status);
  } else {
    obs_lane_index_history_.clear();
    obs_lane_sum_history_.clear();
  }

  if (obs_lane_index != -1) {
    // adjust lane index estimation considering closest curb and sd map
    // according to
    // https://rqk9rsooi4.feishu.cn/docx/OWCwdhwsdoIvqqxGH6lcYsESncb

    // if (!AdjustMatchStatus(sd_lane_sum_, obs_lane_sum, obs_lane_index,
    //                        closest_curb_status, &obs_lane_index_)) {
    //   succeeded = false;
    // }

    obs_lane_index_ = obs_lane_index;
    obs_lane_sum_ = obs_lane_sum;
  }

  if (succeeded && enable_lane_estimation_) {
    lane_estimator_->AddPerceptionMeasurement(
        /*ras map time*/ ras_map_time_,
        /*lane index from ras map*/ obs_lane_index_,
        /*lane sum from ras map*/ obs_lane_sum_,
        /*ego lane id*/ ras_ego_lane_id_);

    const bool lane_index_success = lane_estimator_->GetLaneIndex() > 0;

    latest_lane_index_result.set_sd_lane_index(lane_estimator_->GetLaneIndex());
    latest_lane_index_result.set_sd_lane_sum(lane_estimator_->GetLaneNum());
    // available all time, for internal use.
    latest_lane_index_raw_ = latest_lane_index_result;

    if (!lock_on_road_succeeded_ || !lane_index_success) {
      latest_lane_index_result.set_type(
          LaneIndexEstimationResult::Type::
              LaneIndexEstimationResult_Type_INVALID);
      latest_lane_index_result.clear_sd_lane_index();
      latest_lane_index_result.clear_sd_lane_sum();
      latest_lane_index_result.clear_ego_lane_id();
    } else if (closest_curb_status == ClosestCurbStatus::NO_CURB ||
               closest_curb_to_its_neighbor_lane_dist > 5) {
      latest_lane_index_result.set_type(
          LaneIndexEstimationResult::Type::
              LaneIndexEstimationResult_Type_UNRELIABLE);
    } else {
      latest_lane_index_result.set_type(lane_estimator_->GetStatus());
    }

    latest_lane_index_result.set_ego_lane_id(lane_estimator_->GetEgoLaneId());
  } else if (succeeded && !enable_lane_estimation_) {
    latest_lane_index_result.set_sd_lane_index(obs_lane_index_);
    latest_lane_index_result.set_sd_lane_sum(obs_lane_sum_);
    latest_lane_index_result.set_type(
        Type::LaneIndexEstimationResult_Type_GOOD);
    latest_lane_index_result.set_ego_lane_id(ras_ego_lane_id_);
  } else {
    latest_lane_index_result.set_type(
        LaneIndexEstimationResult::Type::
            LaneIndexEstimationResult_Type_INVALID);
  }

  // --------------------process finished, publish result--------------------
  auto lane_index_result_ptr = std::make_shared<LaneIndexEstimationResult>();
  *lane_index_result_ptr = latest_lane_index_result_;

  publish_lane_index_signal_(lane_index_result_ptr);
  // ------------------------------------------------------------------------
}

void Localizer::HandleRasMapMeasurementMsg(
    const std::shared_ptr<const deeproute::perception::RASMap>& ras_map) {
  // this should not happen, in case of system failure, we should stop
  // processing. This is the only return in this function.
  if (!ras_map) {
    MLOG(WARN) << "ras_map ptr empty.";
    common::ReportEvent(dr::common::LOCK_ON_ROAD,
                        dr::common::LOCK_ON_ROAD_INPUT_RAS_MAP_INVALID);
    return;
  }

  LaneIndexEstimation(ras_map, latest_lane_index_result_);
}

void Localizer::HandleGnssDataMeasurementMsg(
    const std::shared_ptr<const drivers::gnss::GnssPosition>& gnss) {
  gnss_type_ = gnss->position_type();
}

std::unique_ptr<LocalizerBase> CreateLocalizer(
    const LockOnRoadConfig& config,
    const ::common::Transformation3& vehicle_to_imu_transform) {
  return std::make_unique<Localizer>(config, vehicle_to_imu_transform);
}

}  // namespace localization
}  // namespace deeproute
