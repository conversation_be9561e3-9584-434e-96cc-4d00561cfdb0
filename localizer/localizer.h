#pragma once

#include <memory>

#include <boost/signals2.hpp>
#include <network/type.hpp>

#include "compliance/compliance.pb.h"
#include "drapi/operation_status.pb.h"
#include "drivers/gnss/gnss_raw.pb.h"
#include "drivers/gnss/ins.pb.h"
#include "drivers/sensor_image.pb.h"
#include "lock_on_road/lock_on_road.pb.h"
#include "lock_on_road/lock_on_road_debug.pb.h"
#include "perception/deeproute_perception_ras_map.pb.h"
#include "perception/perception_nn.pb.h"
#include "proto/lock_on_road_config.pb.h"
#include "routing/navinfo_routing.pb.h"

#include "common/point.h"
#include "common/time_types.h"
#include "geometry/geometry.h"
#include "lam_common/proto_adapter/blc_adapter.h"
#include "transform/transformation.h"

namespace deeproute {
namespace localization {

using TimeToPosePair =
    std::pair<::common::TimeMicro, ::common::Transformation3>;
using TimeToVector3DPair = std::pair<::common::TimeMicro, Vector3d>;
using TimeToPointCloudPair = std::pair<::common::TimeMicro, PointCloudXYZIRT>;
using CompressedImage = deeproute::drivers::CompressedImage;

class LocalizerBase {
 public:
  using PublishLockOnRoadSignal =
      boost::signals2::signal<void(const std::shared_ptr<LockOnRoadResult>&)>;
  using PublishLockOnRoadSignalType = PublishLockOnRoadSignal::slot_type;

  using PublishLaneIndexSignal = boost::signals2::signal<void(
      const std::shared_ptr<LaneIndexEstimationResult>&)>;
  using PublishLaneIndexSignalType = PublishLaneIndexSignal::slot_type;

  using PublishLockOnRoadDebugInfoSignal = boost::signals2::signal<void(
      const std::shared_ptr<LockOnRoadDebugInfo>&)>;
  using PublishLockOnRoadDebugInfoSignalType =
      PublishLockOnRoadDebugInfoSignal::slot_type;

  virtual ~LocalizerBase() = default;

  virtual void HandleRtkInsMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::Ins>& rtk) = 0;

  virtual void HandleRtkSensorInsMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::SensorsIns>& sensor_ins) = 0;

  virtual void HandleOdometryMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::Ins>& odom) = 0;

  virtual void HandleRasMapMeasurementMsg(
      const std::shared_ptr<const deeproute::perception::RASMap>& ras_map) = 0;

  virtual void HandleGnssDataMeasurementMsg(
      const std::shared_ptr<const drivers::gnss::GnssPosition>& gnss) = 0;

  virtual void HandleGlobalRoutingMsg(
      const std::shared_ptr<const deeproute::navinfo::SDRoutingResponse>&
          routing_response) = 0;

  virtual void HandleRasMapNN(
      const std::shared_ptr<const perception::NnFrame>& rasmap_nn) = 0;

  virtual void HandleSdMapOnboard(
      const std::shared_ptr<const sd_map::SDMapOnboard>& sdmap_onboard) = 0;

  virtual void HandleAmapMsg(
      const std::shared_ptr<const map_onboard_adapter::RealTimeAmap>&
          amap_data) = 0;

  virtual void HandleCompliance(
      const std::shared_ptr<const compliance::Compliance>& compliance) = 0;

  virtual void HandleBlcStatus(
      const std::shared_ptr<const dr::operationstatus::OperationStatus>
          operation_status) = 0;

  virtual bool SetProjectionPoint(const double lon_deg, const double lat_deg,
                                  const double scale_factor) = 0;

  virtual LockOnRoadResult GetLatestLockOnRoadResult() = 0;
  virtual LaneIndexEstimationResult GetLatestLaneIndexEstimationResult() = 0;
  virtual LockOnRoadDebugInfo GetLatestLockOnLaneDebugInfo() = 0;
  virtual perception::RASMap GetDebugRasMap() = 0;
  virtual LockOnRoadResult GetLatestDdmmResult() = 0;
  virtual const LockOnRoadDdmmDebugInfo& GetLatestDdmmDebugInfo() = 0;
  virtual AmapStatus GetLatestAmapStatus() = 0;
  virtual Vector3d GetLorInputPoseBlhGcj02() = 0;
};

std::unique_ptr<LocalizerBase> CreateLocalizer(
    const LockOnRoadConfig& config,
    const ::common::Transformation3& vehicle_to_imu_transform);

}  // namespace localization
}  // namespace deeproute
