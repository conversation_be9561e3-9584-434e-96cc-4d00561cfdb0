#pragma once

#include <network/type.hpp>

#include "lock_on_road/lock_on_road.pb.h"

namespace deeproute {
namespace localization {

class MMConfidenceEstimator {
 public:
  MMConfidenceEstimator() = default;
  ~MMConfidenceEstimator() = default;

  // add lateral position
  inline void AddDistanceToRoutingResponse(const double distance) {
    min_distance_to_routing_response_ = distance;
  }
  // add routing response
  inline void AddRoutingResponse(
      const FMM::NETWORK::CustomGraph& custom_graph) {
    custom_graph_ = custom_graph;
  }
  // add gnss position raw
  inline void AddPose(
      const drivers::gnss::SensorsIns& gnss_position) {
    gnss_pose_ = gnss_position;
  }
  // add LOR
  void AddLor(const deeproute::localization::LockOnRoadResult& lor);

  // getter
  LockOnRoadResult GetLockOnRoadResult();

 private:
  double GetTravelledDistanceInTunnel();

 private:
  double min_distance_to_routing_response_ = -1;
  FMM::NETWORK::CustomGraph custom_graph_;
  LockOnRoadResult lock_on_road_result_;
  drivers::gnss::SensorsIns gnss_pose_;

  bool in_tunnel_ = false;
  int tunnel_start_link_idx_ = -1;
  int tunnel_end_link_idx_ = -1;
  double route_distance_in_tunnel_;
};

}  // namespace localization

}  // namespace deeproute
