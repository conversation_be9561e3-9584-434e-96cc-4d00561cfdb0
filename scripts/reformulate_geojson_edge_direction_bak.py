import time
from tracemalloc import start
import geoj<PERSON>
import geopandas
from matplotlib.pyplot import axis


import shapely.wkt
import shapely.ops
from shapely.errors import ShapelyDeprecationWarning
import warnings
warnings.filterwarnings("ignore", category=ShapelyDeprecationWarning)


# global start


def reverse_geom(geom):
    def _reverse(x, y, z=None):
        # if z:
        #     print("has z")
        #     return x[::-1], y[::-1], z[::-1]
        return x[::-1], y[::-1]

    return shapely.ops.transform(_reverse, geom)


def convert_double_direction_geojson(in_path, out_path):
    map = geopandas.read_file(in_path)

    edge_num = len(map)
    print("edge num:", edge_num)
    print(map)

    max_edge_id = map['ID'].astype('float64').max()

    map_id = 10086
    reverse_edge_row_index = edge_num

    global start
    start = time.time()

    node_dict = []

    for row_index, row in map.iterrows():

        if(row_index % int(edge_num/10) == 0):
            cur_time = time.time()
            print("Time taken: ", cur_time - start)
            start = cur_time

        direction = row['Direction']

        # columns=['MapID', 'ID', 'SnodeID', 'EnodeID', 'geometry'])

        # 2: 顺方向：单向通行，通行方向为起点到终点方向
        if direction == '2':
            # row = [map_id, row['ID'],
            #        row['SnodeID'], row['EnodeID'], row['geometry']]
            # geo_dict[row_index] = row
            node_dict.append(
                {"MapID": map_id,
                 "ID": row['ID'],
                 "SnodeID": row['SnodeID'],
                 "EnodeID": row['EnodeID'],
                 'geometry': row['geometry']
                 }
            )
        # 3: 逆方向：单向通行，通行方向为终点到起点方向
        elif direction == '3':
            # row = [map_id, str(int(max_edge_id)),
            #        row['EnodeID'], row['SnodeID'], reverse_geom(row['geometry'])]
            # geo_dict[row_index] = row
            # print("row['EnodeID']: ", row['EnodeID'], type(row['EnodeID']))
            node_dict.append(
                {"MapID": map_id,
                 "ID": str(int(max_edge_id)),
                 "EnodeID": row['EnodeID'],
                 "SnodeID": row['SnodeID'],
                 "geometry": reverse_geom(row['geometry'])
                 }
            )
        # 0: 未调查：默认为双方向都可以通行
        # 1: 双向：双方向可以通行
        elif direction in ['0', '1']:
            # row_1 = [map_id, row['ID'],
            #          row['SnodeID'], row['EnodeID'], row['geometry']]
            # geo_dict[row_index] = row_1
            # row_2 = [map_id, str(int(max_edge_id)),
            #          row['EnodeID'], row['SnodeID'], reverse_geom(row['geometry'])]
            # geo_dict[reverse_edge_row_index] = row_2

            node_dict.append(
                {"MapID": map_id,
                 "ID": row['ID'],
                 "SnodeID": row['SnodeID'],
                 "EnodeID": row['EnodeID'],
                 'geometry': row['geometry']
                 }
            )
            node_dict.append(
                {"MapID": map_id,
                 "ID": str(int(max_edge_id)),
                 "EnodeID": row['EnodeID'],
                 "SnodeID": row['SnodeID'],
                 'geometry': reverse_geom(row['geometry'])
                 }
            )

            reverse_edge_row_index += 1
            max_edge_id += 1

        max_edge_id += 1

    print("start writing.")

    dest = geopandas.GeoDataFrame(node_dict)

    print("---------------------------------")
    print(dest[0:2])
    print("Dest size: ", len(dest))
    dest.to_file(out_path, driver="GeoJSON")
    print("write to: ", out_path)


if __name__ == '__main__':
    assert(geojson.__version__ == "2.5.0")
    # in_path = "/home/<USER>/map_matching/data/sz_single/sd_link.geojson"
    # out_path = "/home/<USER>/map_matching/data/sz_single/local_sd_reformulated_according_to_manual.geojson"

    in_path = "/home/<USER>/map_matching/data/sz_full/local_sd.geojson"
    out_path = "/home/<USER>/map_matching/data/sz_full/local_sd_reformulated_according_to_manual_2.geojson"
    convert_double_direction_geojson(in_path, out_path)
