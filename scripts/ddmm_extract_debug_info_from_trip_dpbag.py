      
#!/usr/bin/python3.8

# import geojson
from geojson import Point, Feature, FeatureCollection, dump, LineString
import rosbag
import sys
import os
import struct
import numpy as np
import pyproj
from pygcj.pygcj import GCJProj
import numpy as np
from dplib import Dp<PERSON>ng<PERSON>
from dpbag import DpBag

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto/")
from drivers.sensor_image_pb2 import CompressedImage
# fmt: on

# from semantic_map.map_pb2 import Map
# from drivers.gnss.ins_pb2 import Ins
# from drivers.gnss.ins_pb2 import SensorsIns
# from drivers.gnss.gnss_pb2 import Gnss
# from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from lock_on_road.lock_on_road_pb2 import LockOnRoadResult
# from routing.navinfo_routing_pb2 import SDRoutingResponse

# fmt: on

__lock_on_map_topic = "/localization/lock_on_map"
__lock_on_map_debug_topic = "/localization/lock_on_map_debug_image"
__car_state_topic = "/canbus/car_state"


def proto_compressed_to_cv_mat(proto_data):
    format = proto_data.format
    data = proto_data.data
    
    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decode = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)

    return img_decode

def ReadMessages(dp, output_dir):
    topic_list = [__lock_on_map_debug_topic, __car_state_topic]

    print("---------------------------------")
    print("Processing: ", dp)
    print("Output path: ", output_dir)
    print("---------------------------------")

    from dpbag import strip_header

    debug_num = len(list(dp.read_messages(topics=__lock_on_map_debug_topic)))
    print("Total debug_num: ", debug_num)

    bag_file = os.path.join(output_dir, 'out.bag')
    bag = rosbag.Bag(bag_file, 'w')

    time_to_lock_on_map = {}
    time_to_lock_on_map_debug = {}
    time_to_car_state = {}
    
    succ_images = []
    failed_images = []

    for topic, msg, t in dp.read_messages(topics=topic_list):

        data = strip_header(msg.data)
        if topic ==  __lock_on_map_topic:
            
            lom = LockOnRoadResult()
            lom.ParseFromString(data)
            time_to_lock_on_map[lom.time_us] = lom
        
        elif topic == __lock_on_map_debug_topic:
            
            compressed_image = CompressedImage()
            compressed_image.ParseFromString(data)
            ts = int(timestamp_sec * 1e6)
            time_to_lock_on_map_debug[ts] = compressed_image
            
    for time, lom in time_to_lock_on_map.items():
        
        if time not in time_to_lock_on_map_debug:
            print(f"lom time {time} not found in debug time list.")
            continue
        
        lom_debug = time_to_lock_on_map_debug[time]
        
        image = proto_compressed_to_cv_mat(lom_debug)
                    
        if lom.shadow_mode_result.status = LockOnRoadResult.ShadowMode.Status.MISMATCH:
            failed_images.append((time, image))
        else:
            succ_images.append((time, image))
            
    
    fail_out = os.path.join(output_dir, "fail")
    os.mkdir(fail_out)
    
    succ_out = os.path.join(output_dir, "succ")
    os.mkdir(succ_out)
    
    for time_and_img in failed_images:
        cv2.imwrite(os.path.joint(fail_out, str(time_and_img[0])+".png"), time_and_img[1])
    
    for time_and_img in succ_images:
        cv2.imwrite(os.path.joint(fail_out, str(time_and_img[0])+".png"), time_and_img[1])
    
if __name__ == '__main__':

    import sys

    import argparse
    parser = argparse.ArgumentParser(
        description="Usage: python3.8 extract_debug_info_from_trip_dpbag.py <trip_id> <output_dir>")
    parser.add_argument("trip_id",
                        type=str,
                        help="trip id")
    parser.add_argument("output_dir",
                        type=str,
                        help="output_dir"
                        )
    parser.add_argument("username",
                        type=str,
                        help="username"
                        )
    parser.add_argument("password",
                        type=str,
                        help="password"
                        )

    args = parser.parse_args()

    dp = DpEngine(args.username, args.password)
    print(dp.get_frame_status(int(args.trip_id)))

    bag = DpBag(username=args.username, password=args.password,
                trip_id=int(args.trip_id))

    print("username: ", args.username)
    print("password: ", args.password)

    ReadMessages(bag, args.output_dir)
    