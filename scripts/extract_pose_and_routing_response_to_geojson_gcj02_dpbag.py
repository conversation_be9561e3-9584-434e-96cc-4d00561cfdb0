#!/usr/bin/python3.8
# import geojson
from geojson import Point, Feature, FeatureCollection, dump, LineString
import rosbag
import sys
import os
import struct
import numpy as np
import pyproj
import math
from pygcj.pygcj import GCJProj
import cv2
import numpy as np

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
# from drivers.sensor_image_pb2 import CompressedImage
# from drivers.sensor_image_pb2 import CompressedImage 
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from lock_on_road.lock_on_road_pb2 import LaneIndexEstimationResult, LockOnRoadResult
from routing.navinfo_routing_pb2 import SDRoutingResponse
from map.sd_map_pb2 import SDMapOnboard
from map.routing_pb2 import RoutingRequest
from drapi.gwm_navigation_pb2 import RealTimeData
# fmt: on

from dplib import DpEngine
from dpbag import DpBag


__debug_topic = "/localization/lock_on_road_debug"
lock_on_lane_topic = "/localization/lane_index"
__ins_topic = "/sensors/gnss/pose"
__routing_response_topic = "/map/routing/internal/response"
__lock_on_road_topic = "/localization/lock_on_road"


class GcjToLocalUtmTransformer(object):
    __gcj_proj: GCJProj = None

    def __init__(self):
        self.__gcj_proj = GCJProj()

    def WgsToGcj(self, lon, lat):
        gcj_lat, gcj_lon = self.__gcj_proj.wgs_to_gcj(lat, lon)
        return gcj_lat, gcj_lon

def convert_poses_to_geojson(time_to_poses_gcj02, output_path):
    features = []

    for time, pose in time_to_poses_gcj02.items():
        features.append(
            Feature(geometry=Point((pose[1], pose[0])), properties={"time": time}))

    feature_collection = FeatureCollection(features)

    print("wrinting to ", output_path)
    with open(output_path, 'w') as f:
        dump(feature_collection, f)


def convert_routing_request_and_routing_response_to_geojson(order_index, rr, output_dir):

    features_request = rr.request
    # print(features_request)

    import os
    out_path = os.path.join(output_dir, "routing_requst_order_" + str(order_index) + "_request_id_" + str(
        rr.request_id) + ".geojson")

    features = []

    request_info = features_request.request_info
    features.append(
        Feature(geometry=Point((request_info.start_point.lon, request_info.start_point.lat)), properties={"ID": 0}))

    idx = 1
    for end_pt in request_info.end_points:
        features.append(
            Feature(geometry=Point((end_pt.lon, end_pt.lat)), properties={"ID": idx}))
        idx += 1

    feature_collection = FeatureCollection(features)

    print("writing routing request to ", out_path)
    with open(out_path, 'w') as f:
        dump(feature_collection, f)

    #################################################################################################################
    import os
    out_path = os.path.join(output_dir, "routing_response_order_" + str(order_index) + "_request_id_" + str(
        rr.request_id) + ".geojson")
    features_rr = []

    route = rr.result[0].route
    for seg in route.segm:
        seg_pts = []
        for shape_pt_gcj02 in seg.shape_points:
            seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        feature = Feature(geometry=LineString(seg_pts),
                          properties={"ID": seg.ni_id})
        features_rr.append(feature)

    feature_collection = FeatureCollection(features_rr)

    print("writing routing response to ", out_path)
    with open(out_path, 'w') as f:
        dump(feature_collection, f)


def ReadMessages(dp, output_dir):
    topic_list = [__ins_topic, 
                #   __routing_response_topic, 
                  __lock_on_road_topic]

    print("---------------------------------")
    print("Processing: ", dp)
    print("Output path: ", output_dir)
    print("---------------------------------")

    from dpbag import strip_header

    time_to_poses_gcj02 = {}
    ordered_routing_responses = []
    lors = []

    proj = GcjToLocalUtmTransformer()

    gnss_step = 100

    ins_sum = len(list(dp.read_messages(topics=__ins_topic)))
    print("Total num: ", ins_sum)

    idx = 0
    for topic, msg, t in dp.read_messages(topics=topic_list):

        data = strip_header(msg.data)
        
        if topic == __ins_topic:
            
            idx += 1
            if (idx % 1e2) == 0:
                print("reading ins msg: " + str(idx) + " / " + str(ins_sum))
            
            gnss_step += 1
            if gnss_step % 100 != 0:
                continue
            else:
                gnss_step = 0

            gnss = SensorsIns()
            gnss.ParseFromString(data)
            if gnss.HasField('imu_frame_position_llh'):
                latlon = [gnss.imu_frame_position_llh.lat,
                          gnss.imu_frame_position_llh.lon]

            elif gnss.HasField('vehicle_frame_position_llh'):
                latlon = [gnss.vehicle_frame_position_llh.lat,
                          gnss.vehicle_frame_position_llh.lon]

            # lat_gcj02, lon_gcj02 = proj.WgsToGcj(latlon[1], latlon[0])
            lat_gcj02, lon_gcj02 = latlon[0], latlon[1]

            time_to_poses_gcj02[gnss.measurement_time] = [
                lat_gcj02, lon_gcj02]

        elif topic == __debug_topic:
            lock_on_road_debug = LockOnRoadDebugInfo()
            lock_on_road_debug.ParseFromString(data)
        elif topic == lock_on_lane_topic:
            lane_index = LaneIndexEstimationResult()
            lane_index.ParseFromString(data)
        elif topic == __lock_on_road_topic:
            lor = LockOnRoadResult()
            lor.ParseFromString(data)
            lors.append(lor)
        elif topic == __routing_response_topic:
            routing_response = SDRoutingResponse()
            routing_response.ParseFromString(data)

            if routing_response.request_id not in [request_id_and_response[0] for request_id_and_response in ordered_routing_responses]:
                ordered_routing_responses.append(
                    [routing_response.request_id, routing_response])

    print("Loaded ", len(time_to_poses_gcj02), " poses")
    print("Loaded ", len(ordered_routing_responses), " routing responses")
    print("Loaded: ", len(lors), "lock on road results")
    return time_to_poses_gcj02, ordered_routing_responses, lors


def write_msg_to_out_dir(time_to_poses_gcj02, request_id_to_routing_responses, all_bag_lock_on_road_results, output_dir):
    print("total poses: ", len(time_to_poses_gcj02))
    print("total responses: ", len(request_id_to_routing_responses))
    pose_output_path = os.path.join(output_dir, "poses.geojson")
    print("writting pose to: ", pose_output_path)
    convert_poses_to_geojson(
        time_to_poses_gcj02, pose_output_path)

    for order_index, [_, rr] in enumerate(request_id_to_routing_responses):
        convert_routing_request_and_routing_response_to_geojson(
            order_index, rr, output_dir)

    pose_output_path = os.path.join(output_dir, "lock_on_road_results.txt")
    with open(pose_output_path, "w") as f:
        for lor in all_bag_lock_on_road_results:
            f.write(str(lor))
    f.close()


if __name__ == '__main__':

    import sys

    import argparse
    parser = argparse.ArgumentParser(
        description="Usage: python3.8 extract_pose_and_routing_response_to_geojson_gcj02_trip.py <trip_id> <output_dir>")
    parser.add_argument("trip_id",
                        type=str,
                        help="trip id")
    parser.add_argument("output_dir",
                        type=str,
                        help="output_dir"
                        )
    parser.add_argument("username",
                        type=str,
                        help="username"
                        )
    parser.add_argument("password",
                        type=str,
                        help="password"
                        )

    args = parser.parse_args()

    dp = DpEngine(args.username, args.password)
    print(dp.get_frame_status(int(args.trip_id)))

    bag = DpBag(username=args.username, password=args.password,
                trip_id=int(args.trip_id))

    print("username: ", args.username)
    print("password: ", args.password)

    all_bag_poses, all_bag_routing_responses, all_bag_lock_on_road_results = ReadMessages(
        bag, args.output_dir)

    write_msg_to_out_dir(
        all_bag_poses, all_bag_routing_responses, all_bag_lock_on_road_results, args.output_dir)

    