# from lmc.sd_map import SdMapClient, SdMapProvider
# from common.geometry_pb2 import PointLLH, Point3D
# import matplotlib.pyplot as pyplot

# if __name__ == "__main__":
#     client = SdMapClient()

#     # 按点和半径请求
#     rep_2 = client.query_links_by_circle(PointLLH(lon=114.058379, lat=22.507783), radius=1000)
#     print(rep_2)
    

import numpy as np
import re
from google.protobuf import text_format
import sys
from geojson import Point, Feature, LineString, FeatureCollection, dump
import os
import matplotlib.pyplot as plt
from pylab import rcParams
rcParams['figure.figsize'] = 10, 10

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from map.routing_pb2 import RoutingRequest
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on

def convert_rr_to_txt(rr_txt):
    f = open(rr_txt, "r")
    rr = SDRoutingResponse()
    rr = text_format.Parse(f.read(), rr)

    out_path = os.path.join(os.path.dirname(
        rr_txt), "routing_response.geojson")

    features_rr = []

    route = rr.result[0].route
    for seg in route.segm:
        gcj_pts = []
        for shape_pt_gcj02 in seg.shape_points:
            gcj_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        linestring = LineString(gcj_pts)
        features_rr.append(
            Feature(geometry=linestring, properties={"ID": seg.ni_id}))

    feature_collection = FeatureCollection(features_rr)

    print("link size: ", len(route.segm))
    with open(out_path, 'w') as f:
        dump(feature_collection, f)



if __name__ == '__main__':
    import sys

    convert_rr_to_txt(sys.argv[1])
