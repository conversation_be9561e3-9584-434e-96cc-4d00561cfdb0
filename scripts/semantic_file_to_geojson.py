#!/usr/bin/python3.8
from __future__ import print_function
import sys
import json

sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")

from semantic_map.map_pb2 import Map


def main():
    semantic_file = "/opt/deeproute/onboard/maps/semantic-map/semantic-map.bin"
    with open(semantic_file, "rb") as f:
        map = Map()
        map.ParseFromString(f.read())
    content = {
        "type": "FeatureCollection",
        "name": "dr hd map",
        "features": []
    }
    print("map lane boundary size: ", len(map.lane_boundaries))
    for lane_b in map.lane_boundaries:
        feature = {"type": "Feature", "properties": {
            "ID": f"{lane_b.id}"}, "geometry": {"type": "LineString", "coordinates": []}}
        for point in lane_b.boundary.point:
            feature["geometry"]["coordinates"].append([point.x, point.y])
        content["features"].append(feature)
    print("write file")
    with open("sd.geojson", "w") as f:
        json.dump(content, f)
    print("done")


if __name__ == "__main__":
    main()
