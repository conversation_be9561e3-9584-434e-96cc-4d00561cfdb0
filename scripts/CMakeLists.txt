add_executable(convert_wgs84_to_gcj02_from_txt
        cplusplus/convert_wgs84_to_gcj02_from_txt.cpp
)
target_link_libraries(convert_wgs84_to_gcj02_from_txt
        ${Boost_LIBRARIES}
        common_math
        localization_mm
        common_transform
        ${GTEST_BOTH_LIBRARIES}
        lane_localization_proto
        common_geographiclib
        gflags
)

install(TARGETS
        convert_wgs84_to_gcj02_from_txt
        ARCHIVE DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        LIBRARY DESTINATION ${CATKIN_PACKAGE_LIB_DESTINATION}
        RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION})