#!/usr/bin/python3.8
from google.protobuf import text_format
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, Feature, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj
import os

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
from map.routing_pb2 import RoutingRequest
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on


def convert_rr_to_txt(rr_txt):
    f = open(rr_txt, "r")
    rr = SDRoutingResponse()
    rr = text_format.Parse(f.read(), rr)

    features_request = rr.request
    print(features_request)

    import os
    out_path = os.path.join(os.path.dirname(
        rr_txt), "routing_requst.geojson")

    features = []

    request_info = features_request.request_info
    features.append(
        Feature(geometry=Point((request_info.start_point.lon, request_info.start_point.lat)), properties={"ID": 0}))

    idx = 1
    for end_pt in request_info.end_points:
        features.append(
            Feature(geometry=Point((end_pt.lon, end_pt.lat)), properties={"ID": idx}))
        idx += 1

    feature_collection = FeatureCollection(features)

    print("wrinting to ", out_path)
    with open(out_path, 'w') as f:
        dump(feature_collection, f)

    #################################################################################################################
    import os
    out_path = os.path.join(os.path.dirname(
        rr_txt), "routing_response.geojson")
    
    features_rr = []

    route = rr.result[0].route
    for seg in route.segm:
        for shape_pt_gcj02 in seg.shape_points:
            features_rr.append(
                Feature(geometry=Point((shape_pt_gcj02.lon, shape_pt_gcj02.lat)), properties={"ID": seg.ni_id}))

    feature_collection = FeatureCollection(features_rr)

    # print("")
    with open(out_path, 'w') as f:
        dump(feature_collection, f)


if __name__ == '__main__':

    import sys
    if len(sys.argv) != 2:
        print(sys.argv[0], "wps_file_path")
        sys.exit()

    convert_rr_to_txt(sys.argv[1])
