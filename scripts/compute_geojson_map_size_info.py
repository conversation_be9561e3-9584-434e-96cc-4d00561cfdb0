import numpy as np
from geojson import Point, Feature, FeatureCollection, dump
import time
from tracemalloc import start
import geojson
import geopandas
from matplotlib.pyplot import axis
import shapely.wkt

def compute(map_path):
    map = geopandas.read_file(map_path)

    edge_num = len(map)
    print("edge num:", edge_num)
    
    xs = []
    ys = []
    for index, row in map.iterrows():
        geo = row['geometry']
        geo = shapely.wkt.loads(str(geo))
        
        for pt in geo.coords:
            # print(pt[0], pt[1])
            xs.append(pt[0])
            ys.append(pt[1])
        
    print("min x: ", min(xs))
    print("max x: ", max(xs))
    print("min y: ", min(ys))
    print("max y: ", max(ys))

if __name__ == '__main__':
    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "map_path")
        sys.exit()

    compute(sys.argv[1])
