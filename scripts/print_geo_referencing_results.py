#!/usr/bin/python3.8

import matplotlib.pyplot as plt
import rosbag
import sys

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
sys.path.insert(1, "../build/proto")
# from routing.routing_pb2 import RoutingResponse
from geo_referencing_result_pb2 import GeoReferencingResult
# fmt: on


def load_results(bag_path):

    id_to_dists = {}
    dists = []
    times = []
    start_time = None
    with rosbag.Bag(bag_path) as bag:
        for topic, msg, t in bag.read_messages(topics=["/localization/geo_ref_result"], raw=True):
            # skip first 4 bytes
            data = msg[1][4:]
            result = GeoReferencingResult()
            result.ParseFromString(data)

            # print(result.time_us)
            # print(result)
            id = result.sd_link_id
            if id not in id_to_dists:
                id_to_dists[id] = []
            else:
                id_to_dists[id].append(result.sd_distance_to_link_start)

            dists.append(result.sd_distance_to_link_start)

            if start_time == None:
                start_time = result.time_us

            dt = (result.time_us - start_time)/1e6
            times.append(dt)

    # print(id_to_dists)

    return times, dists


def save_t_and_dist_to_csv(times, dists):
    f = open("time_and_dists.txt", "w")

    # check(len(times) == len(dists))

    for i in range(len(times)):
        f.write(str(times[i]) + " " + str(dists[i]) + "\n")
    # f.write("Now the file has more content!")
    f.close()


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag")
        sys.exit()

    times, dists = load_results(sys.argv[1])
    save_t_and_dist_to_csv(times, dists)

    plt.plot(times, dists)
    plt.show()
