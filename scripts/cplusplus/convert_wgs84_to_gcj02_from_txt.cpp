#include <fstream>
#include <iostream>
#include <numeric>
#include <string>

#include <boost/algorithm/string.hpp>
#include <boost/filesystem.hpp>
#include <gflags/gflags.h>

#include "common/log.h"
#include "joint/ll_utils.h"

DEFINE_string(txt_path, "", "");

void Convert(const std::string& wps84_file) {
  std::ifstream file(wps84_file);
  std::string combined_arg;

  std::string line;
  while (std::getline(file, line)) {
    std::vector<std::string> strs;
    boost::split(strs, line, boost::is_any_of(" "));

    double lat = std::stod(strs[0]);
    double lon = std::stod(strs[1]);
    double gcj_lon;
    double gcj_lat;
    Wgs84ToGcj02(lon, lat, &gcj_lon, &gcj_lat);
    MLOG(INFO) << "gcj02 latlon: " << gcj_lat << ", " << gcj_lon;
    combined_arg +=
        std::to_string(gcj_lat) + "," + std::to_string(gcj_lon) + ",";
  }
  combined_arg.pop_back();
  MLOG(INFO) << "wps: \n" << combined_arg;
}

int main(int argc, char** argv) {
  gflags::ParseCommandLineFlags(&argc, &argv, true);

  Convert(FLAGS_txt_path);
  return 0;
}
