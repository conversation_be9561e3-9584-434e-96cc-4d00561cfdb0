#!/usr/bin/python3.8
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, LineString, Feature, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj
import os

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from routing.routing_pb2 import RoutingResponse
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
# fmt: on


class GeoJsonTools(object):
    __features = None
    __type = None
    __name = None
    __feature_dict = {}
    __feature_list = []
    __feature_coords = []
    __utm_transformer = GcjToLocalUtmTransformer()
    __kdtree = None

    def __init__(self, path):
        gj = geojson.load(open(path))
        self.__features = gj["features"]
        self.__type = gj["type"]
        self.__name = gj["name"]
        assert(len(self.__features))
        self.PrepareData()

    def PrepareData(self):
        for feature in self.__features:
            coordinates_utm = []
            for coord in feature['geometry']['coordinates']:
                utm = self.__utm_transformer.GcjToLocalUtm(coord[1], coord[0])
                coordinates_utm.append(utm)
            self.__feature_list.append(feature)
            self.__feature_coords.append(np.mean(coordinates_utm, 0))
            self.__feature_dict[feature["properties"]["ID"]] = feature
            # print("feature: ", feature['geometry']['coordinates'])

        self.__kdtree = KDTree(np.array(self.__feature_coords), leaf_size=2)

    def GetFeatureById(self, id):
        pprint.pprint(self.__feature_dict[id])
        return self.__feature_dict[id]

    def FindAdjascentLinksByPoses(self, poses):
        links = []

        # find links
        for pose in poses:
            pose = np.array([float(pose.x), float(pose.y)])
            dist, ind = self.__kdtree.query([pose], k=1)

            queried_link = self.__feature_list[ind[0][0]]

            coord_utms = []
            for coord in queried_link['geometry']['coordinates']:
                coord_utm = self.__utm_transformer.GcjToLocalUtm(
                    coord[1], coord[0])
                coord_utms.append(coord_utm)

            queried_link['geometry']['coordinates_utm'] = [
                np.mean(coord_utms, 0)[0], np.mean(coord_utms, 0)[1]]

            links.append(queried_link)
        print("Raw queried links num: ", len(links))
        # return links

        # convert coord in links from BLT to UTM
        # mainly for visualization
        ids = []
        filtered_links = []
        for link in links:
            id = link["properties"]["ID"]
            if id in ids:
                continue

            ids.append(id)
            link['geometry']['coordinates'] = link['geometry']['coordinates_utm']
            del link['geometry']['coordinates_utm']
            filtered_links.append(link)

        return filtered_links


class BagWalker(object):
    __local_responses = []
    __localization_poses = []
    __gnss_poses = []
    __debug_messages = []
    __localization_topic = "/localization/pose"
    __planning_topic = "/planner/routing_response"
    __debug_topic = "/localization/lock_on_road_debug"
    __gnss_topic = "/sensors/gnss/pose"
    __bag_path = None

    def __init__(self, path, lon=107.633242, lat=29.988549):
        self.__bag_path = path
        self.ReadMessages()
        self.__utm_transformer = LocalUtmTransformer(lon, lat)

    def ReadMessages(self):
        topic_list = [self.__localization_topic,
                      self.__planning_topic, self.__gnss_topic]
        with rosbag.Bag(self.__bag_path) as bag:
            for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
                # skip first 4 bytes
                data = msg[1][4:]
                if topic == self.__localization_topic:
                    ins = Ins()
                    ins.ParseFromString(data)
                    self.__localization_poses.append(ins)
                elif topic == self.__gnss_topic:
                    gnss = SensorsIns()
                    gnss.ParseFromString(data)
                    self.__gnss_poses.append(gnss)
                elif topic == self.__planning_topic:
                    routing_response = RoutingResponse()
                    routing_response.ParseFromString(data)
                    self.__local_responses.append(routing_response)
                elif topic == self.__debug_topic:
                    lock_on_road_debug = LockOnRoadDebugInfo()
                    lock_on_road_debug.ParseFromString(data)
                    self.__debug_messages.append(lock_on_road_debug)

        print("Loaded: ", len(self.__localization_poses), " localization poses")
        print("Loaded: ", len(self.__local_responses), " routing responses")

    def GetAllRoutingResponses(self):
        return self.__local_responses

    def GetAllLocalizationPoses(self, skip_step=10):
        if not skip_step:
            return self.__localization_poses
        skipped = []
        for idx, pose in enumerate(self.__localization_poses):
            if idx % skip_step == 0:
                skipped.append(pose)

        return skipped

    def GetAllGnssPoses(self):
        return self.__gnss_poses

    def GetAllGnssPosesLonLat(self):
        gnss_poses_blh = []
        for gnss in self.__gnss_poses:
            gnss_poses_blh.append(
                [gnss.imu_frame_position_llh.lon, gnss.imu_frame_position_llh.lat])

        return gnss_poses_blh

        # f.write(str(gnss.measurement_time) + " " +
        #         str(gnss.imu_frame_position_llh.lon) + " " +
        #         str(gnss.imu_frame_position_llh.lat) + " " +
        #         str(gnss.imu_frame_position_llh.height) + "\n")
    def WriteLockOnRoadDebugToGeoJson(self, path):
        features = []

        path = os.path.join(path, "lock_on_road_debug.geojson")
        for debugs in self.__debug_messages:
            debug = debugs.pose_debug_info[-1]

            start = Point((debug.position.x, debug.position.y))
            target = Point((debug.matched_position.x,
                           debug.matched_position.y))
            line = LineString([(debug.position.x, debug.position.y),
                               (debug.matched_position.x,
                                debug.matched_position.y)])
            features.append(start)
            features.append(target)
            features.append(line)

        feature_collection = FeatureCollection(features)
        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WritePosesUtmToGeoJson(self, path):
        assert(len(self.__localization_poses))
        features = []

        path = os.path.join(path, "localization_poses_utm.geojson")
        for ins in self.__localization_poses:
            point = Point((ins.position.x, ins.position.y))
            features.append(
                Feature(geometry=point, properties={"timestamp": ins.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WritePosesUtmToTxt(self, path):
        assert(len(self.__localization_poses))

        path = os.path.join(path, "localization_poses_utm.txt")
        f = open(path, "a")
        for ins in self.__localization_poses:
            f.write(str(ins.measurement_time) + " " +
                    str(ins.position.x) + " " +
                    str(ins.position.y) + " " +
                    str(ins.position.z) + " " +
                    str(ins.linear_velocity_enu.x) + " " +
                    str(ins.linear_velocity_enu.y) + " " +
                    str(ins.linear_velocity_enu.z) + " " +
                    str(ins.angular_velocity_enu.x) + " " +
                    str(ins.angular_velocity_enu.y) + " " +
                    str(ins.angular_velocity_enu.z) + " " +
                    str(ins.euler_angles.x) + " " +
                    str(ins.euler_angles.y) + " " +
                    str(ins.euler_angles.z) + " " +
                    "\n")

        f.close()

    def WritePosesLatLongToTxt(self, path):
        assert(len(self.__localization_poses))

        path = os.path.join(path, "localization_poses_latlong.txt")
        f = open(path, "a")
        for ins in self.__localization_poses:
            f.write(str(ins.measurement_time) + " " +
                    str(ins.position_llh.lat) + " " +
                    str(ins.position_llh.lon) + "\n")

        f.close()

    def WriteGnssMeasurementsUtmToTxt(self, path):
        assert(len(self.__gnss_poses))

        path = os.path.join(path, "gnss_poses_utm.txt")
        f = open(path, "a")
        for gnss in self.__gnss_poses:
            # print(gnss)

            utm = self.__utm_transformer.LlToLocalUtm(
                lat=gnss.vehicle_frame_position_llh.lat, lon=gnss.vehicle_frame_position_llh.lon)

            f.write(str(gnss.measurement_time) + " " +
                    str(utm[0]) + " " +
                    str(utm[1]) + " " +
                    str(gnss.vehicle_frame_position_llh.height)
                    + "\n")

        f.close()

    def WriteGnssMeasurementsUtmToGeojson(self, path):
        assert(len(self.__gnss_poses))
        features = []

        path = os.path.join(path, "gnss_poses_utm.geojson")
        for gnss in self.__gnss_poses:

            utm = self.__utm_transformer.LlToLocalUtm(
                lat=gnss.imu_frame_position_llh.lat, lon=gnss.imu_frame_position_llh.lon)

            point = Point((utm[0], utm[1]))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WriteGnssMeasurementsLatLongToTxt(self, path):
        assert(len(self.__gnss_poses))

        path = os.path.join(path, "gnss_poses_longlat.txt")
        f = open(path, "a")
        for gnss in self.__gnss_poses:
            # print(gnss)
            # f.write(str(gnss.measurement_time) + " " +
            # str(gnss.vehicle_frame_position_llh.lon) + " " +
            # str(gnss.vehicle_frame_position_llh.lat) + " " +
            # str(gnss.vehicle_frame_position_llh.height) + "\n")
            f.write(str(gnss.measurement_time) + " " +
                    str(gnss.imu_frame_position_llh.lon) + " " +
                    str(gnss.imu_frame_position_llh.lat) + " " +
                    str(gnss.imu_frame_position_llh.height) + "\n")
        f.close()

    def WriteGnssLatLonToGeoJson(self, path):
        assert(len(self.__gnss_poses))
        features = []

        path = os.path.join(path, "gnss_poses_latlon.geojson")
        for gnss in self.__gnss_poses:

            # print(gnss)
            point = Point((gnss.imu_frame_position_llh.lat,
                          gnss.imu_frame_position_llh.lon))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)


def GetRoadIdsFromRoutingResponse(response):
    ids = []
    for road in response.road:
        ids.append(road.id)
    return ids


def LoadLaneDictFromSemanticMap(path):
    file = open(path, 'rb')
    map = Map()
    map.ParseFromString(file.read())

    lane_dict = {}
    for lane in map.lane:
        lane_dict[str(lane.id)] = [point for point in lane.centerline.point]

    print("Total semantic map lane num: ", len(lane_dict))
    return lane_dict


def GetSumPointsFromSemanticMapAndLaneIds(lane_dict, lane_ids):
    total_center_pts = []
    success = 0
    failed = 0
    for lane_id in lane_ids:
        if lane_id in lane_dict:
            total_center_pts += lane_dict[lane_id]
            success += 1
        else:
            failed += 1
            print(lane_id, " doesnt exist in the lane dict.")
    print("Successful: ", success)
    print("Failed: ", failed)
    return total_center_pts


def GetCenterLineByLaneId(lane_dict, id):
    return lane_dict[id]


def ConvertPointsToGeoJson(points):
    assert(len(points))
    features = []

    for point in points:
        point = Point((point.x, point.y))
        features.append(
            Feature(geometry=point, properties={"country": "China"}))

    print("Added ", len(features), " points!")
    feature_collection = FeatureCollection(features)

    with open('routing_response_lane_center_points.geojson', 'w') as f:
        dump(feature_collection, f)


def WriteFeaturesToFile(features, path):
    feature_collection = FeatureCollection(features)
    # pprint.pprint(feature_collection)
    with open(path, 'w') as f:
        dump(feature_collection, f)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag output")
        sys.exit()

    if not os.path.exists(sys.argv[2]):
        os.mkdir(sys.argv[2])

    bw = BagWalker(sys.argv[1])

    # save localization poses to geo json file
    # bw.WriteGnssMeasurementsUtmToTxt(sys.argv[2])
    bw.WriteGnssMeasurementsUtmToGeojson(sys.argv[2])
    bw.WriteGnssMeasurementsLatLongToTxt(sys.argv[2])
    # bw.WriteGnssLatLonToGeoJson(sys.argv[2])
    # bw.WritePosesUtmToTxt(sys.argv[2])
    bw.WritePosesUtmToGeoJson(sys.argv[2])
    bw.WriteLockOnRoadDebugToGeoJson(sys.argv[2])
    # bw.WritePosesLatLongToTxt(sys.argv[2])

    # # get all routing responses, find all lanes corresponding to the 1st routing response
    # # and save lane center line points
    # all_routing_responses = bw.GetAllRoutingResponses()
    # # get corresponding lane ids of the 1st routing response
    # planned_road_ids = GetRoadIdsFromRoutingResponse(all_routing_responses[0])
    # # get all lanes from semantic map
    # lane_dict = LoadLaneDictFromSemanticMap(
    #     "/media/songhaoran/data/lane_loc_data/semantic-map.bin")
    # # get planned lane center points
    # sum_pts = GetSumPointsFromSemanticMapAndLaneIds
    #     lane_dict, planned_road_ids)
    # # save all routing response center points
    # ConvertPointsToGeoJson(sum_pts)

    # gjt = GeoJsonTools(
    #     "/home/<USER>/lane-based-localization-benchmark/data/siwei_sd_small.geojson")
    # feature = gjt.GetFeatureById('1001050')
    # all_links = gjt.FindAdjascentLinksByPoses(sum_pts)
    # print("num of links found: ", len(all_links))
    # WriteFeaturesToFile(all_links, "queried_links.geojson")
