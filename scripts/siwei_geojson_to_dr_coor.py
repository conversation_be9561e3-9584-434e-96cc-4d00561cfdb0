#!/usr/bin/python3.8
from __future__ import annotations
import pyproj
from pygcj.pygcj import GCJProj
import json


class LocalUtmTransformer(object):
    __lon: float = None
    __lat: float = None
    __transformer = None
    __center_y = None

    def __init__(self, origin_longitude: float = 113.8810761, origin_latitude: float = 22.5608197):
        self.__lon = origin_longitude
        self.__lat = origin_latitude
        tmerc_crs = pyproj.CRS.from_proj4(
            "+proj=tmerc +k_0=0.99999999999999 +lon_0=" + str(self.__lon) + "")
        # EPSG:4326 is WGS84
        self.__transformer = pyproj.Transformer.from_crs(
            "EPSG:4326", tmerc_crs)
        utm_center_x, self.__center_y = self.__transformer.transform(
            self.__lat, self.__lon)

    def LlToLocalUtm(self, lat: float, lon: float) -> tuple[float, float]:
        x, y = self.__transformer.transform(lat, lon)
        y -= self.__center_y
        return x, y


class GcjToLocalUtmTransformer(object):
    __gcj_proj: GCJProj = None
    __local_utm_proj: LocalUtmTransformer = None

    def __init__(self, origin_longitude: float = 113.8810761, origin_latitude: float = 22.5608197):
        self.__local_utm_proj = LocalUtmTransformer(
            origin_longitude, origin_latitude)
        self.__gcj_proj = GCJProj()

    def GcjToLocalUtm(self, lat: float, lon: float) -> tuple[float, float]:
        wgs_lat, wgs_lon = self.__gcj_proj.gcj_to_wgs(lat, lon)
        return self.__local_utm_proj.LlToLocalUtm(wgs_lat, wgs_lon)
    
    def GcjToWgs(self, lon, lat):
        wgs_lon, wgs_lat = self.__gcj_proj.gcj_to_wgs(lat, lon)
        return wgs_lon, wgs_lat
    
    def WgsToGcj(self, lon, lat):
        gcj_lat, gcj_lon = self.__gcj_proj.wgs_to_gcj(lat, lon)
        return gcj_lat, gcj_lon


def main():
    sd_geojson_file = "./data/siwei_sd.geojson"
    with open(sd_geojson_file, "r") as f:
        sd_map = json.load(f)

    print("processing")
    proj = GcjToLocalUtmTransformer()
    for feature in sd_map["features"]:
        for coord in feature["geometry"]["coordinates"]:
            coord[0], coord[1] = proj.GcjToLocalUtm(coord[1], coord[0])
            print(coord)
    print(sd_map["features"][0]["geometry"]["coordinates"][0])
    sd_map.pop("crs")

    print("write file")
    with open("out_sd.geojson", "w") as f:
        json.dump(sd_map, f)
    print("write done")


if __name__ == "__main__":
    main()
