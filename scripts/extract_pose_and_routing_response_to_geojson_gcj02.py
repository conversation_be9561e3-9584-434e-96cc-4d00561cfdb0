#!/usr/bin/python3.8
# import geojson
from geojson import Point, Feature, FeatureCollection, dump, LineString
import rosbag
import sys
import os
import struct
import numpy as np
import pyproj
import math
from pygcj.pygcj import GCJProj
import cv2
import numpy as np

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
from drivers.sensor_image_pb2 import CompressedImage
from drivers.sensor_image_pb2 import CompressedImage 
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from lock_on_road.lock_on_road_pb2 import LaneIndexEstimationResult, LockOnRoadResult
from routing.navinfo_routing_pb2 import SDRoutingResponse
from map.sd_map_pb2 import SDMapOnboard, LinkData
from map.routing_pb2 import RoutingRequest
from drapi.gwm_navigation_pb2 import RealTimeData
from perception.deeproute_perception_ras_map_pb2 import RASMap
from drivers.gnss.gnss_raw_pb2 import GnssPosition
# fmt: on

__debug_topic = "/localization/lock_on_road_debug"
lock_on_lane_topic = "/localization/lane_index"
__ins_topic = "/sensors/gnss/pose"
__routing_response_topic = "/map/routing/internal/response"
__lock_on_road_topic = "/localization/lock_on_road"
__lock_on_map_topic = "/localization/lock_on_map"
__sd_map_topic = "/map/sd_map"
__graph_match_request = "/map/routing/request"
__camera_1_topic = "/sensors/camera/camera_1_raw_data/compressed_proto"
amap_topic = "/gwm/blc/amap_navigation"
rasmap_topic = "/perception/ras_map"
gnss_position_topic = "/sensors/gnss/raw_gnss_position"

def proto_compressed_to_cv_mat(proto_data):
    format = proto_data.format
    data = proto_data.data

    print(f"format {format}")
    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decoded = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)

    print(f"img_decoded shape {np.shape(img_decoded)}")
    return img_decoded


def strip_header(string_data):
    if len(string_data) < 4:
        return string_data
    if (
        string_data[0] != 36
        or string_data[1] != 36
        or string_data[2] != 36
        or string_data[3] != 36
    ):
        # without header
        return string_data
    else:
        # with header
        prefix_size = 4
        header_size_field_size = 4
        info_size = prefix_size + header_size_field_size
        header_size = struct.unpack(
            "I", string_data[prefix_size : prefix_size + header_size_field_size]
        )[0]
        return string_data[info_size + header_size :]


class GcjToLocalUtmTransformer(object):
    __gcj_proj: GCJProj = None

    def __init__(self):
        self.__gcj_proj = GCJProj()

    def WgsToGcj(self, lon, lat):
        gcj_lat, gcj_lon = self.__gcj_proj.wgs_to_gcj(lat, lon)
        return gcj_lat, gcj_lon


def convert_poses_to_geojson(time_to_poses_gcj02, time_to_rasmaps, all_time_to_raw_gnss, output_path):
    
    def count_same_direction_lanes(ego_lane, lane_id_to_lane, max_depth=20):
        # Count right lanes
        right_temp_lane = ego_lane
        right_lane_sum = 0
        right_topo_depth = 0
        
        while hasattr(right_temp_lane, 'right_neighbor_id') and not right_temp_lane.is_right_neighbor_reverse:
            right_neighbor_id = right_temp_lane.right_neighbor_id
            if right_neighbor_id not in lane_id_to_lane:
                print(f"Right lane id {right_neighbor_id} not found, skipping.")
                break

            right_temp_lane = lane_id_to_lane[right_neighbor_id]

            if right_temp_lane.virtual:
                print("Right lane is virtual, skipping.")
                continue
            
            if right_temp_lane.passable_confidence < 0.5:
                print("Right lane is passable, skipping.")
                continue
            
            if right_topo_depth >= max_depth:
                print("Topology depth limit reached for right lanes.")
                break

            right_topo_depth += 1
            right_lane_sum += 1

        # Count left lanes
        left_temp_lane = ego_lane
        left_lane_sum = 0
        left_topo_depth = 0

        while hasattr(left_temp_lane, 'left_neighbor_id') and not left_temp_lane.is_left_neighbor_reverse:
            left_neighbor_id = left_temp_lane.left_neighbor_id
            if left_neighbor_id not in lane_id_to_lane:
                print(f"Left lane id {left_neighbor_id} not found, skipping.")
                break

            left_temp_lane = lane_id_to_lane[left_neighbor_id]

            if left_temp_lane.virtual:
                print("Left lane is virtual, skipping.")
                continue

            if left_temp_lane.passable_confidence < 0.5:
                print("Left lane is passable, skipping.")
                continue
            
            if left_topo_depth >= max_depth:
                print("Topology depth limit reached for left lanes.")
                break

            left_topo_depth += 1
            left_lane_sum += 1

        return left_lane_sum, right_lane_sum


    def rasmap_to_lane_sums(rasmap):
        lane_id_to_lane = {}
        ego_lane = None
        for lane in rasmap.lanes:
            lane_id_to_lane[lane.id] = lane
            if lane.is_ego_lane:
                ego_lane = lane
        
        if not ego_lane:
            print("Ego lane not found!")
            return -1

        left_lane_sum, right_lane_sum = count_same_direction_lanes(ego_lane, lane_id_to_lane)
        
        print(f"Left lanes: {left_lane_sum}")
        print(f"Right lanes: {right_lane_sum}")
        return left_lane_sum + right_lane_sum + 1
            
    
    features = []
    
    idx = 0
    step = 100
    for time, pose in time_to_poses_gcj02.items():
        if idx % step == 0:
            
            if time in time_to_rasmaps:
                rasmap_lane_num = rasmap_to_lane_sums(time_to_rasmaps[time])          
            else:
                rasmap_lane_num = -1
            
            if time in all_time_to_raw_gnss:
                raw_gnss = all_time_to_raw_gnss[time]
                print(raw_gnss)
                std = f"std_lat {raw_gnss.std_latitude:.2f} std_lon {raw_gnss.std_longitude:.2f}"
            else:
                std = -1
            
            fused_std_x, fused_std_y = np.sqrt(pose[2])[0], np.sqrt(pose[2])[1]

            forward_speed = pose[3]
            heading = pose[4]
            features.append(
                Feature(
                    geometry=Point((pose[1], pose[0])),
                    properties={
                        "time": time, 
                        "id": len(features),
                        "heading":heading,
                        "debug": f"lane_num: {rasmap_lane_num}\n raw std: {std} \n fused: {fused_std_x:.1f} {fused_std_y:.1f} \n spd: {forward_speed:.1f}m/s",
                    },
                )
            )

        idx += 1

    feature_collection = FeatureCollection(features)

    print("wrinting to ", output_path)
    with open(output_path, "w") as f:
        dump(feature_collection, f)


def convert_routing_request_and_routing_response_to_geojson(
    order_index, rr, output_dir
):
    import os

    out_path = os.path.join(
        output_dir,
        f"routing_response_order_{order_index}_request_id_{rr.request_id}_route_seg_{rr.segmented_route_num}.geojson",
    )
    features_rr = []

    route = rr.result[0].route
    main_link_ids = set()

    for idx, seg in enumerate(route.segm):
        # main link
        seg_pts = []
        for shape_pt_gcj02 in seg.shape_points:
            seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        feature = Feature(
            geometry=LineString(seg_pts),
            properties={"ID": idx, 
                        "type": "main", 
                        "ni_id": seg.ni_id,
                        "virtual_link": seg.virtual_link,
                        "debug": f"id: {seg.ni_id}, \n virtual: {seg.virtual_link} \n flane_num {seg.f_lane_num}"},
        )
        features_rr.append(feature)
        main_link_ids.add(seg.ni_id)

        # # out link
        # for out_link in seg.out_link_infos:
        #     if out_link.link_attr.ni_id in main_link_ids:
        #         continue

        #     seg_pts = []
        #     for shape_pt_gcj02 in out_link.link_attr.shape_points:
        #         seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        #     feature = Feature(geometry=LineString(seg_pts), properties={
        #         "ID": 2,
        #         "type": "out_link",
        #         "ni_id": out_link.link_attr.ni_id,
        #         "from_link_id": seg.ni_id,
        #         "pass links": [link.ni_id for link in out_link.ps_infos]
        #     })
        #     features_rr.append(feature)

        #     # pass infos of out link
        #     for out_link_pass_info in out_link.ps_infos:
        #         seg_pts = []
        #         for shape_pt_gcj02 in out_link_pass_info.shape_points:
        #             seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        #         feature = Feature(geometry=LineString(seg_pts), properties={
        #             "ID": 2,
        #             "type": "out_link",
        #             "ni_id": out_link_pass_info.ni_id,
        #             "from_link_id": seg.ni_id
        #         })
        #         features_rr.append(feature)

        # # in link
        # for in_link in seg.in_link_infos:
        #     if in_link.link_attr.ni_id in main_link_ids:
        #         continue

        #     seg_pts = []
        #     for shape_pt_gcj02 in in_link.link_attr.shape_points:
        #         seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        #     feature = Feature(geometry=LineString(seg_pts), properties={
        #         "ID": 1,
        #         "type": "in_link",
        #         "ni_id": in_link.link_attr.ni_id,
        #         "from_link_id": seg.ni_id,
        #         "pass links": [link.ni_id for link in in_link.ps_infos]
        #     })
        #     features_rr.append(feature)

        #     # pass infos of in link
        #     for in_link_pass_info in in_link.ps_infos:
        #         seg_pts = []
        #         for shape_pt_gcj02 in in_link_pass_info.shape_points:
        #             seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
        #         feature = Feature(geometry=LineString(seg_pts), properties={
        #             "ID": 1,
        #             "type": "out_link",
        #             "ni_id": in_link_pass_info.ni_id,
        #             "from_link_id": seg.ni_id
        #         })
        #         features_rr.append(feature)

    feature_collection = FeatureCollection(features_rr)

    # write rr as geojson
    print("writing routing response to ", out_path)
    with open(out_path, "w") as f:
        dump(feature_collection, f)

    # write rr as proto cfg
    out_path = os.path.join(
        output_dir,
        f"routing_response_order_{order_index}_request_id_{rr.request_id}_route_seg_{rr.segmented_route_num}.bin",
    )
    print("writing routing response proto to ", out_path)
    with open(out_path, "wb") as f:
        f.write(rr.SerializeToString())

    # write rr.request as json
    # out_path = os.path.join(output_dir, f"routing_request_order_{order_index}_request_id_{rr.request_id}_reqest.json")
    # print("writing routing request to ", out_path)
    # with open(out_path, "w") as f:
    # f.write(str(rr.request.SerializeToString()))


def find_bag_mean_latlon(bag_path):

    latlons = []
    with rosbag.Bag(bag_path) as bag:
        for topic, msg, t in bag.read_messages(topics=[__ins_topic], raw=True):

            data = strip_header(msg[1][4:])
            if topic == __ins_topic:

                gnss = SensorsIns()
                gnss.ParseFromString(data)
                if gnss.HasField("imu_frame_position_llh"):
                    latlon = [
                        gnss.imu_frame_position_llh.lat,
                        gnss.imu_frame_position_llh.lon,
                    ]

                elif gnss.HasField("vehicle_frame_position_llh"):
                    latlon = [
                        gnss.vehicle_frame_position_llh.lat,
                        gnss.vehicle_frame_position_llh.lon,
                    ]

                latlons.append([latlon[0], latlon[1]])

    return np.mean(latlons, axis=0)


def ReadMessages(path, output_dir):
    topic_list = [
        __ins_topic,
        __debug_topic,
        lock_on_lane_topic,
        __routing_response_topic,
        __lock_on_road_topic,
        __lock_on_map_topic,
        __sd_map_topic,
        __graph_match_request,
        amap_topic,
        rasmap_topic,
        gnss_position_topic,
        __camera_1_topic,
    ]

    print("---------------------------------")
    print("Processing: ", path)
    print("Output path: ", output_dir)
    print("---------------------------------")

    idx = 0

    time_to_poses_gcj02 = {}
    time_to_rasmap = {}
    time_to_raw_gnss = {}
    
    amap_datas = []
    ordered_routing_responses = []
    lors = []
    lors_debug = []
    loms = []
    sdmaps = []
    graph_match_requests = []
    camera_1_imgs = []
    
    rasmap = None
    raw_gnss = None
    
    proj = GcjToLocalUtmTransformer()

    gnss_step = 100

    with rosbag.Bag(path) as bag:
        for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
            idx += 1

            data = strip_header(msg[1][4:])
            if topic == __ins_topic:
                # gnss_step += 1
                # if gnss_step % 100 != 0:
                #     continue
                # else:
                #     gnss_step = 0

                gnss = SensorsIns()
                gnss.ParseFromString(data)
                # print(gnss)
                if gnss.HasField("imu_frame_position_llh"):
                    latlon = [
                        gnss.imu_frame_position_llh.lat,
                        gnss.imu_frame_position_llh.lon,
                    ]

                elif gnss.HasField("vehicle_frame_position_llh"):
                    latlon = [
                        gnss.vehicle_frame_position_llh.lat,
                        gnss.vehicle_frame_position_llh.lon,
                    ]

                # lat_gcj02, lon_gcj02 = proj.WgsToGcj(latlon[1], latlon[0])
                # time_to_poses_gcj02[gnss.measurement_time] = [lat_gcj02, lon_gcj02]

                if gnss.reference_coodinate_system == 0:
                    lat_gcj02, lon_gcj02 = proj.WgsToGcj(latlon[1], latlon[0])
                    time_to_poses_gcj02[gnss.measurement_time] = [lat_gcj02, lon_gcj02, gnss.position_covariance, gnss.linear_acceleration_flu.x, gnss.roll_pitch_azimuth.z]
                else:
                    time_to_poses_gcj02[gnss.measurement_time] = [latlon[0], latlon[1], gnss.position_covariance, gnss.linear_acceleration_flu.x, gnss.roll_pitch_azimuth.z]
                
                if rasmap is not None:
                    time_to_rasmap[gnss.measurement_time] = rasmap
                else:
                    print("RASMAP NONE.")
                    
                if raw_gnss is not None:
                    time_to_raw_gnss[gnss.measurement_time] = raw_gnss
                else:
                    print("RAW GNSS NONE.")
                    
            elif topic == gnss_position_topic:
                raw_gnss = GnssPosition()
                raw_gnss.ParseFromString(data)
                # print(f"raw_gnss {raw_gnss}")
            elif topic == amap_topic:
                amap = RealTimeData()
                amap.ParseFromString(data)
                amap_datas.append(amap)
            elif topic == rasmap_topic:
                rasmap = RASMap()
                rasmap.ParseFromString(data)
            elif topic == __debug_topic:
                lock_on_road_debug = LockOnRoadDebugInfo()
                lock_on_road_debug.ParseFromString(data)
                # print(lock_on_road_debug)
                lors_debug.append(lock_on_road_debug)
            elif topic == lock_on_lane_topic:
                lane_index = LaneIndexEstimationResult()
                lane_index.ParseFromString(data)
            elif topic == __lock_on_road_topic:
                lor = LockOnRoadResult()
                lor.ParseFromString(data)
                lors.append(lor)
            elif topic == __lock_on_map_topic:
                lom = LockOnRoadResult()
                lom.ParseFromString(data)
                loms.append(lom)
                # print(f"lom {lom}")
            elif topic == __sd_map_topic:
                sdmap = SDMapOnboard()
                sdmap.ParseFromString(data)
                sdmaps.append(sdmap)
            elif topic == __graph_match_request:
                request = RoutingRequest()
                request.ParseFromString(data)
                graph_match_requests.append(request)
            elif topic == __camera_1_topic:
                compressed_image = CompressedImage()
                compressed_image.ParseFromString(data)

                img = proto_compressed_to_cv_mat(compressed_image)

                print(f"img shape: {img.shape}")

                camera_1_imgs.append(img)

            elif topic == __routing_response_topic:
                routing_response = SDRoutingResponse()
                routing_response.ParseFromString(data)
                print(routing_response.request_id, routing_response.segmented_route_num)

                rr_key = str(routing_response.request_id) + str(
                    routing_response.segmented_route_num
                )
                if rr_key not in [
                    request_id_and_response[0]
                    for request_id_and_response in ordered_routing_responses
                ]:
                    ordered_routing_responses.append([rr_key, routing_response])

    print("Loaded ", len(time_to_poses_gcj02), " poses")
    print("Loaded ", len(ordered_routing_responses), " routing responses")
    print("Loaded: ", len(lors), "lock on road results")
    print("Loaded: ", len(loms), "lock on map results")
    print("Loaded: ", len(sdmaps), "sdmap results")
    print("Loaded: ", len(graph_match_requests), "graph match requests")
    print("Loaded: ", len(amap_datas), "amap_datas")
    return (
        time_to_poses_gcj02,
        ordered_routing_responses,
        lors,
        lors_debug,
        loms,
        sdmaps,
        graph_match_requests,
        camera_1_imgs,
        amap_datas,
        time_to_rasmap,
        time_to_raw_gnss
    )


def write_msg_to_out_dir(
    time_to_poses_gcj02,
    request_id_to_routing_responses,
    all_bag_lock_on_road_results,
    all_bag_lock_on_road_debug_results,
    all_bag_lock_on_map_results,
    all_bag_sdmaps,
    all_bag_graph_match_requests,
    all_bag_camera_1_imgs,
    all_amap_data,
    all_time_to_rasmaps,
    all_time_to_raw_gnss,
    output_dir,
):
    print("total poses: ", len(time_to_poses_gcj02))
    print("total responses: ", len(request_id_to_routing_responses))
    pose_output_path = os.path.join(output_dir, "poses.geojson")
    print("writting pose to: ", pose_output_path)
    convert_poses_to_geojson(time_to_poses_gcj02, all_time_to_rasmaps, all_time_to_raw_gnss, pose_output_path)

    camera_1_output_dir = os.path.join(output_dir, "camera_1_imgs")
    if not os.path.exists(camera_1_output_dir):
        os.makedirs(camera_1_output_dir)
    for idx, img in enumerate(all_bag_camera_1_imgs):
        out_path = os.path.join(camera_1_output_dir, f"{idx}.png")
        cv2.imwrite(out_path, img)
    
    for order_index, [_, rr] in enumerate(request_id_to_routing_responses):
        convert_routing_request_and_routing_response_to_geojson(
            order_index, rr, output_dir
        )
        rr_outpath = os.path.join(output_dir, f"{order_index}_routing_response.cfg")
        with open(rr_outpath, "w") as f:
            f.write(str(rr))
        f.close()

    pose_output_path = os.path.join(output_dir, "lock_on_road_results.txt")
    with open(pose_output_path, "w") as f:
        for lor in all_bag_lock_on_road_results:
            f.write(str(lor))
    f.close()

    pose_debug_output_path = os.path.join(output_dir, "lock_on_road_debug_results.txt")
    with open(pose_debug_output_path, "w") as f:
        for lor_debug in all_bag_lock_on_road_debug_results:
            f.write(str(lor_debug))
    f.close()

    pose_output_path = os.path.join(output_dir, "lock_on_map_results.txt")
    with open(pose_output_path, "w") as f:
        for lom in all_bag_lock_on_map_results:
            f.write(str(lom))
    f.close()

    for idx, sdmap in enumerate(all_bag_sdmaps):
        sdmap_output_path = os.path.join(output_dir, f"{idx}_sdmap.cfg")
        with open(sdmap_output_path, "w") as f:
            f.write(str(sdmap))
        f.close()

    proj = GcjToLocalUtmTransformer()

    lor_time_to_matched_gcj02 = {}
    lor_time_to_lor_status = {}
    for lor in all_bag_lock_on_road_results:
        if math.isnan(lor.matched_position_wgs84.y) or math.isnan(
            lor.matched_position_wgs84.y
        ):
            # print(lor)
            continue

        gcj02_lat, gcj02_lon = proj.WgsToGcj(
            lor.matched_position_wgs84.x, lor.matched_position_wgs84.y
        )
        lor_time_to_matched_gcj02[lor.time_us] = [gcj02_lat, gcj02_lon]
        lor_time_to_lor_status[lor.time_us] = lor
    lom_time_to_matched_gcj02 = {}
    lom_time_to_lom_status = {}
    lom_matched_links = set()
    for lom in all_bag_lock_on_map_results:
        if math.isnan(lom.matched_position_wgs84.y) or math.isnan(
            lom.matched_position_wgs84.y
        ):
            # print(lom)
            continue

        gcj02_lat, gcj02_lon = proj.WgsToGcj(
            lom.matched_position_wgs84.x, lom.matched_position_wgs84.y
        )
        lom_time_to_matched_gcj02[lom.time_us] = [gcj02_lat, gcj02_lon]
        lom_time_to_lom_status[lom.time_us] = lom.status
        lom_matched_links.add(lom.sd_link_id)

    features_lor = []
    features_lom = []
    idx = 0
    for time, pose_gcj02 in time_to_poses_gcj02.items():
        idx += 1

        if time not in lor_time_to_matched_gcj02:
            # print("time not found in matched: ", time)
            continue
        matched_gcj02 = lor_time_to_matched_gcj02[time]
        
        #print(lor_time_to_lor_status[time].navi_status_internal)
        
        features_lor.append(
            Feature(
                geometry=LineString(
                    [
                        [pose_gcj02[1], pose_gcj02[0]],
                        [matched_gcj02[1], matched_gcj02[0]],
                    ]
                ),

                properties={"time": time, 
                            "lor_final_status": lor_time_to_lor_status[time].status,
                            # "debug_string":,
                            #"internal_lor_status": lor_time_to_lor_status[time].navi_status_internal[0].status,
                            #"internal_local_routing_status": "UNKNOWN" if len(lor_time_to_lor_status[time].navi_status_internal) < 2 else lor_time_to_lor_status[time].navi_status_internal[1].status,
                            },
                
                
                
            )
        )

        if time not in lom_time_to_matched_gcj02:
            # print("time not found in matched: ", time)
            continue
        matched_lom_gcj02 = lom_time_to_matched_gcj02[time]
        features_lom.append(
            Feature(
                geometry=LineString(
                    [
                        [pose_gcj02[1], pose_gcj02[0]],
                        [matched_lom_gcj02[1], matched_lom_gcj02[0]],
                    ]
                ),
                properties={"time": time, "status": lom_time_to_lom_status[time]},
            )
        )

    feature_collection = FeatureCollection(features_lor)
    out_path = os.path.join(output_dir, "lor_matching_results.geojson")
    print("writing routing request to ", out_path)
    with open(out_path, "w") as f:
        dump(feature_collection, f)

    feature_collection = FeatureCollection(features_lom)
    out_path = os.path.join(output_dir, "lom_matching_results.geojson")
    print("writing routing request to ", out_path)
    with open(out_path, "w") as f:
        dump(feature_collection, f)

    def write_all_sdmaps_and_matched_links(
        all_bag_sdmaps, link_ids, sdmap_out_path, lom_matched_out_path
    ):
        features = []
        features_lom_matched = []
        sdmap_ids = []
        existing_drlink_ids = []
        for _, sdmap in enumerate(all_bag_sdmaps):
            if sdmap.id in sdmap_ids:
                continue
            else:
                print(f"sdmap id: {sdmap.id}, link size: {len(sdmap.sd_map.links)}")
                sdmap_ids.append(sdmap.id)

            for link_index, link in enumerate(sdmap.sd_map.links):
                if link.dr_link_id in existing_drlink_ids:
                    continue
                else:
                    existing_drlink_ids.append(link.dr_link_id)

                seg_pts = []
                for shape_pt_gcj02 in link.points:
                    seg_pts.append((shape_pt_gcj02.lon, shape_pt_gcj02.lat))
                # feature = Feature(
                #     geometry=LineString(seg_pts),
                #     properties={
                #         "dr link ID": link.dr_link_id,
                #         "navinfo ID": link.navinfo_id,
                #         "spl": link.speed_limit,
                #         "flane_num": link.forward_lane_num,
                #         "priority": link.priority,
                #         "usage": link.usage,
                #         "formway": ", ".join(link.formways),  # Join the list into a single string
                #         "debug_string": f"spl {link.speed_limit}\n flane_num {link.forward_lane_num}",
                #     },
                # )

                # Function to convert enum values to their names
                def formway_names(formways):
                    return [LinkData.FormWay.Name(fw) for fw in formways]
                
                def contains_inner_road(formways):
                    return LinkData.FormWay.ROAD_KIND_INNER_REGION in formways
                
                def contains_elevated_road(formways):
                    return LinkData.FormWay.ROAD_KIND_ELEVATED in formways
                
                def contains_tunnel_road(formways):
                    return LinkData.FormWay.ROAD_KIND_TUNNEL in formways
                
                # Assuming link.formways is a list of integers corresponding to the enum values
                feature = Feature(
                    geometry=LineString(seg_pts),
                    properties={
                        "dr link ID": link.dr_link_id,
                        "navinfo ID": link.navinfo_id,
                        "spl": link.speed_limit,
                        "flane_num": link.forward_lane_num,
                        "priority": link.priority,
                        "usage": link.usage,
                        "is_elevated": contains_elevated_road(link.formways),
                        "is_tunnel": contains_tunnel_road(link.formways),
                        "formway": ", ".join(formway_names(link.formways)),
                        "contains_inner_road": contains_inner_road(link.formways),
                        "debug_string": f"spl {link.speed_limit}\n flane_num {link.forward_lane_num}",
                    },
                )

                features.append(feature)

                # print(f"link.navinfo_id  {link.navinfo_id}, type({type(link.navinfo_id )})")
                # print(f"link_ids id{link_ids})")
                if str(link.navinfo_id) in link_ids:
                    features_lom_matched.append(feature)

        feature_collection = FeatureCollection(features)
        print("writing sdmap to ", sdmap_out_path)
        with open(sdmap_out_path, "w") as f:
            dump(feature_collection, f)

        feature_collection = FeatureCollection(features_lom_matched)
        print("writing sdmap to ", lom_matched_out_path)
        with open(lom_matched_out_path, "w") as f:
            dump(feature_collection, f)
            
    sdmap_out_path = os.path.join(output_dir, "sdmap.geojson")
    lom_matched_out_path = os.path.join(output_dir, "lom_matched_links.geojson")
    print(f"all_bag_sdmaps size {len(all_bag_sdmaps)}")
    write_all_sdmaps_and_matched_links(
        all_bag_sdmaps, lom_matched_links, sdmap_out_path, lom_matched_out_path
    )

    def write_all_graph_match_requests(all_bag_graph_match_requests, out_dir):
        existing_request_ids = []
        for request in all_bag_graph_match_requests:
            if request.request_id in existing_request_ids:
                continue
            else:
                existing_request_ids.append(request.request_id)

            out_path = os.path.join(
                out_dir, f"graph_match_request_{request.request_id}.json"
            )
            with open(out_path, "w") as f:
                f.write(str(request))

    write_all_graph_match_requests(all_bag_graph_match_requests, output_dir)


    def save_amap_data_to_geojson(all_amap_data, out_path):
        
        features = []
        for idx, amap_data in enumerate(all_amap_data):
            feature = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [amap_data.curr_position.longitude, amap_data.curr_position.latitude]
                },
                "properties": {
                    # "timestamp": datetime.fromtimestamp(amap_data.timestamp).isoformat(),
                    "idx": idx,
                    # "heading": amap_data.heading,
                    # "accuracy": amap_data.accuracy
                    "voice_command": amap_data.navigation_voice_command,
                    "is_parallel_road": amap_data.has_parallel_road,
                    "lane_sum": amap_data.curr_lane_num,
                    "amap": f"para: {amap_data.has_parallel_road}, \n ln: {amap_data.curr_lane_num} \n lact: {len(amap_data.lane_actions.background_lanes)}" 
                }
            }
            features.append(feature)
        

        feature_collection = FeatureCollection(features)
        
        with open(out_path, "w") as f:
            dump(feature_collection, f)
        
        print(f"GeoJSON file saved to: {out_path}")
        
    out_path = os.path.join(output_dir, f"amap_data.geojson")        
    save_amap_data_to_geojson(all_amap_data, out_path)
    
    # write amap infos to txt
    out_path = os.path.join(output_dir, f"amap_data.cfg")
    with open(out_path, "w") as f:
        for amap_idx, amap_data in enumerate(all_amap_data):
            f.write(f"\n\n\n -------{amap_idx}---------\n\n\n")
            f.write(str(amap_data))
    f.close()

    # save debug info utm pose to geojson
    def save_debug_info_to_geojson(all_bag_lock_on_road_debug_results, out_path):
        features = []
        for idx, debug_info in enumerate(all_bag_lock_on_road_debug_results):
            last_debug_info = debug_info.pose_debug_info[-1]
            feature = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [last_debug_info.position.x, last_debug_info.position.y]
                },
                "properties": {
                    "idx": idx,
                    # "heading": debug_info.pose.heading,
                    # "accuracy": debug_info.pose.accuracy
                }
            }
            features.append(feature)
        
        feature_collection = FeatureCollection(features)
        
        with open(out_path, "w") as f:
            dump(feature_collection, f)
        
        print(f"GeoJSON file saved to: {out_path}")
    
    out_path = os.path.join(output_dir, f"lor_debug_utm_poses.geojson")        
    save_debug_info_to_geojson(all_bag_lock_on_road_debug_results, out_path)
    
if __name__ == "__main__":

    import sys

    import argparse

    parser = argparse.ArgumentParser(
        description="Usage: python3.8 extract_pose_and_routing_response_to_geojson_gcj02.py <rosbag_path> <output_dir>"
    )
    parser.add_argument("rosbag_file", type=str, help="rosbag file to parse", nargs="+")
    parser.add_argument("output_dir", type=str, help="output_dir")
    args = parser.parse_args()

    all_bag_poses = {}
    all_time_to_rasmaps = {}
    all_time_to_raw_gnss = {}
    
    all_bag_routing_responses = []
    all_bag_lock_on_road_results = []
    all_bag_lock_on_road_debug_results = []
    all_bag_lock_on_map_results = []
    all_bag_sdmaps = []
    all_bag_graph_match_requests = []
    all_bag_camera_1_imgs = []
    all_amap_datas = []
    # for rosbag_file in args.rosbag_file:
    import tqdm
    for rosbag_file in tqdm.tqdm(args.rosbag_file):
        (
            single_bag_poses,
            single_bag_routing_responses,
            single_bag_lock_on_road_results,
            single_bag_lock_on_road_debug_results,
            single_bag_lom_results,
            sdmaps,
            graph_match_requests,
            camera_1_imgs,
            amap_data,
            single_bag_rasmaps,
            time_to_raw_gnss
        ) = ReadMessages(rosbag_file, args.output_dir)

        # add single bag poses to all bag poses
        all_bag_poses.update(single_bag_poses)
        
        all_time_to_rasmaps.update(single_bag_rasmaps)
        
        all_time_to_raw_gnss.update(time_to_raw_gnss)
        
        # add single bag lock on road results to all bag lock on road results
        all_bag_lock_on_road_results += single_bag_lock_on_road_results

        all_bag_lock_on_road_debug_results += single_bag_lock_on_road_debug_results

        all_bag_lock_on_map_results += single_bag_lom_results

        all_bag_sdmaps += sdmaps

        all_bag_graph_match_requests += graph_match_requests

        all_bag_camera_1_imgs += camera_1_imgs

        all_amap_datas += amap_data
        
        # add non repeated single bag routing responses to all bag routing responses
        existing_request_ids = [
            request_id_and_response[0]
            for request_id_and_response in all_bag_routing_responses
        ]
        for single_bag_routing_response in single_bag_routing_responses:
            if single_bag_routing_response[0] not in existing_request_ids:
                all_bag_routing_responses.append(single_bag_routing_response)

    write_msg_to_out_dir(
        all_bag_poses,
        all_bag_routing_responses,
        all_bag_lock_on_road_results,
        all_bag_lock_on_road_debug_results,
        all_bag_lock_on_map_results,
        all_bag_sdmaps,
        all_bag_graph_match_requests,
        all_bag_camera_1_imgs,
        all_amap_datas,
        all_time_to_rasmaps,
        all_time_to_raw_gnss,
        args.output_dir,
    )
