#!/usr/bin/python3.8
# import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
# from sklearn.neighbors import KDTree
# from geojson import Point, Feature, LineString, MultiPoint, FeatureCollection, dump
# from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
# from pygcj.pygcj import GCJProj
import os

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from routing.routing_pb2 import RoutingResponse
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from lock_on_road.lock_on_road_pb2 import LaneIndexEstimationResult
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on

__localization_topic = "/localization/pose"
__planning_topic = "/planner/routing_response"
__debug_topic = "/localization/lock_on_road_debug"
lock_on_lane_topic = "/localization/lane_index"
__gnss_topic = "/sensors/gnss/pose"
__routing_response_topic = "/map/routing/internal/response"
__bag_path = None

def ReadMessages(path, output_dir):
    topic_list = [__gnss_topic, __debug_topic, lock_on_lane_topic, __routing_response_topic]

    print("Processing: ", path)

    idx = 0
    with rosbag.Bag(path) as bag:
        for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
            idx += 1

            # if idx % 10 != 0:
            # continue

            # print(topic)
            # skip first 4 bytes
            data = msg[1][4:]
            if topic == __gnss_topic:
                gnss = SensorsIns()
                gnss.ParseFromString(data)
                # self.__gnss_poses.append(gnss)
            elif topic == __debug_topic:
                lock_on_road_debug = LockOnRoadDebugInfo()
                lock_on_road_debug.ParseFromString(data)
            elif topic == lock_on_lane_topic:
                lane_index = LaneIndexEstimationResult()
                lane_index.ParseFromString(data)
                print(lane_index)
            elif topic == __routing_response_topic:
                routing_response = SDRoutingResponse()
                routing_response.ParseFromString(data)
                # print(routing_response)
                rr_path = os.path.join(os.path.dirname(output_dir), str(routing_response.request_id) + "_routing_response.txt")
                with open(rr_path, "w") as f:
                    f.write(str(routing_response))
                f.close()

if __name__ == '__main__':

    import sys
    # if len(sys.argv) == 1:
        # print(sys.argv[0], "bag output")
        # sys.exit()
    
    ReadMessages(sys.argv[1], sys.argv[2])