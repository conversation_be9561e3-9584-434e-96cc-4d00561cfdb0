#!/usr/bin/python3.8
from google.protobuf import text_format
import sys
from geojson import Point, Feature, FeatureCollection, dump
import os
import re


def process(output):

    line = "114.058454,22.507526;114.058379,22.507783;114.05818,22.508465"
    line = re.split(";|,", line)
    pt_num = int(len(line)/2)

    # convert to pt list
    pts = []
    for i in range(pt_num):
        pts.append([float(line[i*2]), float(line[i*2+1])])

    print(pts)
    
    features = []
    for idx, pt in enumerate(pts):
        features.append(
            Feature(geometry=Point((pt[1], pt[0])), properties={"ID": idx}))

    feature_collection = FeatureCollection(features)
    print("writing routing request to ", output)
    with open(output, 'w') as f:
        dump(feature_collection, f)


if __name__ == '__main__':
    process(sys.argv[1])
