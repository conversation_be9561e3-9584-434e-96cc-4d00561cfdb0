#!/usr/bin/python3.8
from __future__ import print_function
import sys
import json


if __name__ == "__main__":
    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "txt")
        sys.exit()

    file = open(sys.argv[1]).readlines()
    start = (file[0].split()[0], file[0].split()[1])
    end = (file[1].split()[0], file[1].split()[1])
    print(start, end)

    # poses
    poses = {
        "type": "FeatureCollection",
        "name": "dr hd map",
        "features": []
    }
    feature = {"type": "Feature", "properties": {
        "ID": 0}, "geometry": {"type": "LineString", "coordinates": [[float(start[1]), float(start[0])], [float(end[1]), float(end[0])]]}}
    poses['features'].append(feature)
    with open("start_to_end.geojson", "w") as f:
        print(json.dump(poses, f))
    # print(poses)

    # edges
    content = {
        "type": "FeatureCollection",
        "name": "dr hd map",
        "features": []
    }
    for line in file:
        line = line.split()[1:]
        # print(line)
        feature = {"type": "Feature", "properties": {
            "ID": f"{line[0]}"}, "geometry": {"type": "LineString", "coordinates": []}}

        pt_num = int(len(line) / 2)
        for i in range(pt_num):
            lat = float(line[2*i])
            lon = float(line[2*i+1])
            feature["geometry"]["coordinates"].append([lat, lon])
        content["features"].append(feature)
    with open("queried_links.geojson", "w") as f:
        print(json.dump(content, f))
    # print(content)
