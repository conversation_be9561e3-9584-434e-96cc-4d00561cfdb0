#!/bin/bash
bag_path=$1
for file in ${bag_path}/*.bag
do
    echo "Filtering ${file}"
    output_file="${file}_filtered.bag"
    echo "Output file: ${output_file}"
    rosbag filter "${file}" "${output_file}" "topic == '/sensors/gnss/pose' or topic == '/sensors/gnss/raw_gnss_position' or topic == '/map/routing/internal/response' or topic == '/perception/ras_map_nn' or topic == '/map/sd_map' or topic == '/map/sd_horizon_map' or topic == '/localization/routing_mask' or topic == '/localization/lane_mask'"
done
