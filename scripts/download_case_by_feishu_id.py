import click
import requests
import json
from drfile.drfile_client import DrFileClient
from drfile.app_context import ClientConfiguration
import os
from drfile.modules.sdk.model.request.file_transfer_request import GetBagsRequest

def get_bag_list(fs_ids, project_key):
    # FEISHU api
    url = 'https://mlp-northstar.deeproute.cn/api/northstar/v1/feishu/get_feishu_issue_bag_list'
    headers = {'Content-Type': 'application/json'}
    
    bag_lists = []
    print(fs_ids)
    # save bag list from api
    for fs_id in fs_ids:
        print(f"processing {fs_id}")
        try:
            data = {
               "project_key": project_key,
               "issue_ids": [fs_id]
            }
            print(f"request {data}")
                        
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            data = json.loads(response.text)
            # import pdb; pdb.set_trace()
            bag_list = data['data']['issues'][0]['bag_list']
            # filter only light bag
            
            bag_list = [item for item in bag_list if 'Light' in item]
            if len(bag_list) == 0:
                bag_list = data['data']['issues'][0]['bag_list']
            
            bag_lists.append(bag_list)

        except requests.exceptions.HTTPError as err:
            print(f"Error for FEISHU ID {fs_id}: {err}")
        except json.JSONDecodeError as err:
            print(f"Error parsing JSON response for FEISHU ID {fs_id}: {err}")
        except KeyError as err:
            print(f"Error getting bag_list from response for FEISHU ID {fs_id}: {err}")
    return bag_lists

def download(download_baglist, target_dir,  fs_id):
    try:
        client = DrFileClient(ClientConfiguration(
                endpoint='https://drplatform-backend.deeproute.cn')
            )
        folder_path = os.path.join(target_dir, fs_id)
        print(f"Downloading to {folder_path}")

        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
        client.download_bags(GetBagsRequest(download_baglist, namespace='trip'), folder_path)
        return True
    except Exception as err:
        print(f"Error downloading bags for FEISHU ID {fs_id}: {err}")
        return False



def download_bags(fs_ids, target_dir, project_key):
    """
    Download bags from FEISHU IDs.
    """
    bag_lists = get_bag_list(fs_ids, project_key)
    
    # sum results
    success_flags = []
    for i, fs_id in enumerate(fs_ids):
        try:
            print(f"Bag list for {fs_id}: {bag_lists[i]}")
            success = download(bag_lists[i], target_dir, fs_id)
            success_flags.append(success)
        except IndexError as err:
            print(f"Error accessing bag list for FEISHU ID {fs_id}: {err}")
    
    num_success = sum(success_flags)
    num_total = len(success_flags)
    # Summary false
    print(f"--**----**--Downloaded {num_success} out of {num_total} FEISHU IDs successfully.--**----**--")


if __name__ == '__main__':
    import sys

    import argparse

    parser = argparse.ArgumentParser(
        description="Usage: python3.8 extract_pose_and_routing_response_to_geojson_gcj02.py <rosbag_path> <output_dir>"
    )
    parser.add_argument("project_key", default="l2driving", type=str, help="output_dir")
    parser.add_argument("rosbag_file", type=str, help="rosbag file to parse", nargs="+")
    parser.add_argument("output_dir", type=str, help="output_dir")
    args = parser.parse_args()
    download_bags(args.rosbag_file, args.output_dir, "l2driving")
