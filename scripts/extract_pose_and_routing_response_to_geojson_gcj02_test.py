      
#!/usr/bin/python3.8
# import geojson
from geojson import Point, Feature, FeatureCollection, dump, LineString
import rosbag
import sys
import os
import struct
import numpy as np
import pyproj
from pygcj.pygcj import GCJProj

# fmt: off
sys.path.insert(0, "/opt/deeproute/data_driven_map_matching/include/proto/")
sys.path.insert(1, "/opt/deeproute/data_driven_map_matching/include/data_driven_map_matching/proto")

from lam_common.sd_map.sd_map_service_pb2 import QuerySDMapRep,SDMap
# from semantic_map.map_pb2 import Map
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on

__debug_topic = "/localization/lock_on_road_debug"
lock_on_lane_topic = "/localization/lane_index"
__ins_topic = "/sensors/gnss/pose"
__routing_response_topic = "/map/routing/internal/response"
__lock_on_road_topic = "/localization/lock_on_road"

ORIGIN_LON_LAT = {}
ORIGIN_LON_LAT["shenzhen"] = (113.8810761, 22.5608197)
ORIGIN_LON_LAT["wuhan"] = (114.08084, 30.444827)
ORIGIN_LON_LAT["hangzhou"] = (120.**************, 30.***************)
ORIGIN_LON_LAT["guangzhou"] = (113.**************, 23.***************)
ORIGIN_LON_LAT["xiangyang"] = (112.122426, 32.009016)
ORIGIN_LON_LAT["nanjing"] = (118.803012, 32.049065)
ORIGIN_LON_LAT["suzhou"] = (120.*************, 31.***************)
ORIGIN_LON_LAT["xiamen"] = (117.********, 24.********)
ORIGIN_LON_LAT["beijing"] = (24.********, 40.239119)
ORIGIN_LON_LAT["chongqing"] = (107.633242, 29.988549)
ORIGIN_LON_LAT["shanghai"] = (121.452082, 31.220613)
ORIGIN_LON_LAT["fremont"] = (-121.943079, 37.46199)


def strip_header(string_data):
    if len(string_data) < 4:
        return string_data
    if string_data[0] != 36 or string_data[1] != 36 or string_data[2] != 36 or string_data[3] != 36:
        # without header
        return string_data
    else:
        # with header
        prefix_size = 4
        header_size_field_size = 4
        info_size = prefix_size + header_size_field_size
        header_size = struct.unpack(
            "I", string_data[prefix_size: prefix_size + header_size_field_size])[0]
        return string_data[info_size + header_size:]

def convert_routing_responses_to_sdmap(all_routing_responses):
    
    link_ids = set()
    non_repeated_links = []
    for rr in all_routing_responses:
        for link in rr.sd_links:
            # assert(link.link_frame == GCJ02)
            if link.navinfo_id not in link_ids:
                non_repeated_links.add(link)
    
    sd_map_query = QuerySDMapRep()
    sd_map = sd_map_query.sd_map()
    for link in non_repeated_links:
        sd_map.append(link)
    
    return sd_map

def ReadMessages(path, output_dir):
    topic_list = [__ins_topic, __debug_topic,
                  lock_on_lane_topic, __routing_response_topic, __lock_on_road_topic]

    print("---------------------------------")
    print("Processing: ", path, type(path))
    print("Output path: ", output_dir)
    print("---------------------------------")

    idx = 0

    time_to_poses_gcj02 = {}
    ordered_routing_responses = []
    lors = []

    with rosbag.Bag(path) as bag:
        for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
            idx += 1

            data = strip_header(msg[1][4:])

            if topic == __routing_response_topic:
                routing_response = SDRoutingResponse()
                routing_response.ParseFromString(data)

                ordered_routing_responses.append(routing_response)
                
    

    return time_to_poses_gcj02, ordered_routing_responses, lors


if __name__ == '__main__':

    import sys

    import argparse
    parser = argparse.ArgumentParser(
        description="Usage: python3.8 extract_pose_and_routing_response_to_geojson_gcj02.py <rosbag_path> <output_dir>")
    parser.add_argument("rosbag_file",
                        type=str,
                        help="rosbag file to parse")
    parser.add_argument("output_dir",
                        type=str,
                        help="output_dir"
                        )
    args = parser.parse_args()

    ReadMessages(args.rosbag_file, args.output_dir)

    