#!/usr/bin/env python
import sys
import rosbag
from rospy import rostime
import os


def merge(bags, root):
    output_path = os.path.join(root, 'merged.bag')
    merged = rosbag.Bag(output_path, 'w')
    for bag in bags:
        print("processing bag: ", bag)
        bag = rosbag.Bag(bag)
        for topic, msg, t in bag.read_messages(topics=['/perception/objects','/sensors/lidar/combined_point_cloud', '/sensors/gnss/pose', '/sensors/gnss/gnss', '/localization/lock_on_map_debug_image', '/perception/ras_map']):
            merged.write(topic, msg)
        bag.close()

    merged.close()


if __name__ == "__main__":

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag_path")
        sys.exit()

    path = sys.argv[1]
    bags = os.listdir(path)
    bags.sort()
    # bags = sorted(bags, key=lambda x: int(os.path.splitext(x)[0]))
    bags = [os.path.join(path, file) for file in bags]
    print(bags)
    merge(bags, sys.argv[1])
