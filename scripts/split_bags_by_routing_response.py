#!/usr/bin/python3.8

from posixpath import split
from tracemalloc import start
import geojson
import pprint
import rosbag
import sys
import os
from geojson import Point, Feature, FeatureCollection, dump
import shutil

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
sys.path.insert(1, "./build/proto")
from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from routing.routing_pb2 import RoutingResponse
from ground_truth_pb2 import GroundTruthMeasurements, GroundTruthMeasurement
# fmt: on


def SplitBag(input_bag, output_bag, start, end):
    command = "rosbag filter " + input_bag + " " + output_bag + " " +\
        " 't.secs >=" + str(start) + " and " + "t.secs <= " + str(end) + "'"
    print(command)
    os.system(command)


def MoveBags(bags, target_dir):
    for idx, bag in enumerate(bags):
        # target_path = os.path.join(target_dir, str(idx))
        print("Moving ", bag, " to ", target_dir)
        shutil.move(bag, target_dir)


def GetBagStartAndEndTime(bag_path):
    bag = rosbag.Bag(bag_path)
    start_time = bag.get_start_time()
    end_time = bag.get_end_time()
    return start_time, end_time


def ConvertTime(t):
    sec = t.secs
    nsec = t.nsecs
    ts = sec+1e-9*nsec
    return ts


if __name__ == '__main__':

    if len(sys.argv) == 1:
        print(sys.argv[0], " input_bag_dir  output_bag_dir")
        sys.exit()

    import os
    bags = os.listdir(path=sys.argv[1])
    bags.sort()

    routing_responses = []
    for rosbag_file in bags:
        rosbag_file = os.path.join(sys.argv[1], rosbag_file)

        print("processing bag path: ", rosbag_file)

        bag_of_same_route = True
        start_time = None
        end_time = None
        with rosbag.Bag(rosbag_file) as bag:
            for topic, msg, t in bag.read_messages(topics=["/planner/routing_response"], raw=True):
                # skip first 4 bytes
                data = msg[1][4:]
                routing_response = RoutingResponse()
                routing_response.ParseFromString(data)
                ts = ConvertTime(t)

                if start_time == None:
                    start_time = ts

                if not hasattr(routing_response, "road") or len(routing_response.road) == 0:
                    print("empty routing response")
                    continue

                if len(routing_responses) == 0:
                    print(f"new routing response found")
                    routing_responses.append(routing_response)
                elif routing_responses[-1].header.timestamp_sec == routing_response.header.timestamp_sec:
                    continue
                else:
                    print(f"new routing response found")
                    bag_of_same_route = False
                    end_time = ts

                    # split currrent bag, save to given path, add saved path.
                    new_name = os.path.basename(
                        rosbag_file) + "_" + str(start_time) + "_"+str(end_time)+".bag"
                    splited_bag_path = os.path.join(
                        sys.argv[1], new_name)
                    SplitBag(rosbag_file, splited_bag_path,
                             start=start_time, end=end_time)

                    # move accumulated bags to target dir
                    target_dir = os.path.join(
                        sys.argv[2], str(len(routing_responses)))
                    if not os.path.exists(target_dir):
                        os.mkdir(target_dir)
                    target_path = os.path.join(
                        target_dir, new_name)
                    MoveBags([splited_bag_path], target_path)

                    routing_responses.append(routing_response)

                    start_time = None
                    end_time = None

        if(bag_of_same_route):
            target_dir = os.path.join(
                sys.argv[2], str(len(routing_responses)))
            if not os.path.exists(target_dir):
                os.mkdir(target_dir)
            target_path = os.path.join(
                target_dir, os.path.basename(rosbag_file) + ".bag")
            MoveBags([rosbag_file], target_path)
