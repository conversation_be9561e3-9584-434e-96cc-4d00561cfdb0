#!/usr/bin/python3.8
import sys

sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")

from semantic_map.map_pb2 import Map


def main():
    if len(sys.argv) == 1:
        print(sys.argv[0], "semantic_bin_file")
        sys.exit()
    with open(sys.argv[1], "rb") as f:
        map = Map()
        map.ParseFromString(f.read())
        print(map)


if __name__ == "__main__":
    main()
