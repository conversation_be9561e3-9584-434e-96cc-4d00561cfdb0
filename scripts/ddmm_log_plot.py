import re
import matplotlib.pyplot as plt
import numpy as np


input_filename = 'lor.INFO'
top_values_count = 20 

pattern = re.compile(r'inference_time = (\d+\.\d+)')
inference_times = []

with open(input_filename, 'r') as file:
    for line in file:
        match = pattern.search(line)
        if match:
            inference_time = float(match.group(1))
            inference_times.append(inference_time)


mean_value = np.mean(inference_times)
std_value = np.std(inference_times)
min_value = np.min(inference_times)
max_value = np.max(inference_times)

largest_values = sorted(enumerate(inference_times), key=lambda x: x[1], reverse=True)[:top_values_count]

top_indexes = [index for index, _ in largest_values]
top_values = [value for _, value in largest_values]


plt.figure(figsize=(8, 6))
plt.scatter(range(len(inference_times)), inference_times, label='All values', marker='o', color='blue')


plt.scatter(top_indexes, top_values, label=f'Top {top_values_count} values', marker='x', color='red')

for i, value in largest_values:
    plt.annotate(f'{value:.2f}', (i, value), textcoords="offset points", xytext=(0, 10), ha='center')

stats_text = f'Mean: {mean_value:.2f}\nSTD: {std_value:.2f}\nMin: {min_value:.2f}\nMax: {max_value:.2f}'
plt.text(0.05, 0.85, stats_text, transform=plt.gca().transAxes, bbox=dict(facecolor='white', alpha=0.5))

plt.title('Inference Times')
plt.xlabel('Index')
plt.ylabel('Time(ms)')
plt.legend()
plt.grid(True)
plt.show()
