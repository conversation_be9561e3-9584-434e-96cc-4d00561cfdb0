import geojson
import geopandas
from matplotlib.pyplot import axis


import shapely.wkt
import shapely.ops


def reverse_geom(geom):
    def _reverse(x, y, z=None):
        if z:
            return x[::-1], y[::-1], z[::-1]
        return x[::-1], y[::-1]

    return shapely.ops.transform(_reverse, geom)


def convert_double_direction_geojson(in_path, out_path):
    map = geopandas.read_file(in_path)

    edge_num = len(map)
    print("edge num:", edge_num)
    print(map)

    max_edge_id = map['ID'].astype('float64').max()

    dest = geopandas.GeoDataFrame(
        columns=['MapID', 'ID', 'SnodeID', 'EnodeID', 'geometry'])

    map_id = 10086
    reverse_edge_row_index = edge_num

    for row_index, row in map.iterrows():

        if(row_index % int(edge_num/10) == 0):
            print("row_index: ", row_index)

        direction = row['Direction']

        # 2: 顺方向：单向通行，通行方向为起点到终点方向
        if direction == '2':
            dest.loc[row_index] = [map_id, row['ID'],
                                   row['SnodeID'], row['EnodeID'], row['geometry']]
        # 3: 逆方向：单向通行，通行方向为终点到起点方向
        elif direction == '3':
            dest.loc[row_index] = [map_id, str(int(max_edge_id)),
                                   row['EnodeID'], row['SnodeID'], reverse_geom(row['geometry'])]
        # 0: 未调查：默认为双方向都可以通行
        # 1: 双向：双方向可以通行
        elif direction in ['0', '1']:
            dest.loc[row_index] = [map_id, row['ID'],
                                   row['SnodeID'], row['EnodeID'], row['geometry']]
            dest.loc[reverse_edge_row_index] = [map_id, str(int(max_edge_id)),
                                                row['EnodeID'], row['SnodeID'], reverse_geom(row['geometry'])]
            reverse_edge_row_index += 1
            max_edge_id += 1

        max_edge_id += 1

    print("---------------------------------")
    print(dest[0:2])

    dest.to_file(out_path, driver="GeoJSON")
    print("write to: ", out_path)


if __name__ == '__main__':
    assert(geojson.__version__ == "2.5.0")

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "in out")
        sys.exit()
        
    convert_double_direction_geojson(sys.argv[1], sys.argv[2])
