#!/usr/bin/python3.8

import rosbag
import sys
import os
import struct
import numpy as np
from dpbag import strip_header
import cv2

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto/")
from drivers.sensor_image_pb2 import CompressedImage
# fmt: on

__lock_on_map_debug_topic = "/localization/lock_on_map_debug_image"


def proto_compressed_to_cv_mat(proto_data):
    format = proto_data.format
    data = proto_data.data
    
    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decode = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)

    return img_decode

def process_bag(bag_path, output_video_path):
    topic_list = [__lock_on_map_debug_topic]
    
    bag_file = bag_path
    bag = rosbag.Bag(bag_file)

    fourcc = cv2.VideoWriter_fourcc(*'XVID')
    frame_rate = 30
    frame_size = (640*3, 640)

    out = cv2.VideoWriter(output_video_path, fourcc, frame_rate, frame_size)
    
    for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
        
        data = msg[1][4:]
        
        data = strip_header(data)
        compressed_image = CompressedImage()
        compressed_image.ParseFromString(data)

        mat = proto_compressed_to_cv_mat(compressed_image)
        mat = cv2.resize(mat, frame_size)

        out.write(mat)
        
    out.release()
    bag.close()

if __name__ == '__main__':
    import sys

    if len(sys.argv) != 3:
        print("Usage: python ddmm_convert_bag_to_video.py <path_to_bag_file> <out_video_path>")
    else:
        process_bag(sys.argv[1], sys.argv[2])
