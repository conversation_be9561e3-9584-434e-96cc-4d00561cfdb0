#!/usr/bin/python3.8
import geojson
import pprint
import rosbag
import sys
from geojson import Point, Feature, FeatureCollection, dump

sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from semantic_map.map_pb2 import Map
from routing.routing_pb2 import RoutingResponse


def GetRoadIdsFromRoutingResponse(response):
    ids = []
    for road in response.road:
        ids.append(road.id)
    return ids


def LoadLaneDictFromSemanticMap(path):
    file = open(path, 'rb')
    map = Map()
    map.ParseFromString(file.read())

    lane_dict = {}
    for lane in map.lane:
        lane_dict[str(lane.id)] = [point for point in lane.centerline.point]

    print("lane num: ", len(lane_dict))
    return lane_dict


def GetSumPointsFromSemanticMapAndLaneIds(lane_dict, ids):
    pts = []
    success = 0
    failed = 0
    for id in ids:
        if id in lane_dict:
            pts += lane_dict[id]
            success += 1
            print("Found lane id", id, " in the lane dict.")
        else:
            failed += 1
            print(id, " doesnt exist in the lane dict.")
    print("Successed: ", success)
    print("Failed: ", failed)
    return pts


def ConvertPointsToGeoJson(points):
    assert(len(points))
    features = []
    
    for point in points:
        point = Point((point.x, point.y))
        features.append(Feature(geometry=point, properties={"country": "China"}))
    
    print("Added ", len(features), " points!")
    feature_collection = FeatureCollection(features)

    with open('test.geojson', 'w') as f:
        dump(feature_collection, f)
    
    
if __name__ == '__main__':
    if len(sys.argv) == 1:
        print(sys.argv[0], "rosbag1 rosbag2 ...")
        sys.exit()
    features = []
    rosbag_files = sys.argv[1:]
    # rosbag_files = ["/media/zyuzhi/zyuzhi_data/data/lane_based_localization/YR-ALX-7_20220612_015444.Light_Topic_Group/YR-ALX-7_20220612_015444.Light_Topic_Group.bag"]
    routing_responses = []
    for rosbag_file in rosbag_files:
        print(f"parsing {rosbag_file}")
        with rosbag.Bag(rosbag_file) as bag:
            for topic, msg, t in bag.read_messages(topics=["/planner/routing_response"], raw=True):
                # skip first 4 bytes
                data = msg[1][4:]
                routing_response = RoutingResponse()
                routing_response.ParseFromString(data)
                if not hasattr(routing_response, "road") or len(routing_response.road) == 0:
                    print("empty routing response")
                    continue
                if len(routing_responses) == 0:
                    print(f"new routing response found")
                    routing_responses.append(routing_response)
                elif routing_responses[-1].header.timestamp_sec == routing_response.header.timestamp_sec:
                    continue
                else:
                    print(f"new routing response found")
                    routing_responses.append(routing_response)

    print("Loaded: ", len(routing_responses), " routing responses")
    planned_road_ids = GetRoadIdsFromRoutingResponse(routing_responses[0])

    lane_dict = LoadLaneDictFromSemanticMap(
        "/opt/deeproute/onboard/maps/semantic-map/semantic-map.bin")
    sum_pts = GetSumPointsFromSemanticMapAndLaneIds(lane_dict, planned_road_ids)
    ConvertPointsToGeoJson(sum_pts)
