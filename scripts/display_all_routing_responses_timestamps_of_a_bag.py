#!/usr/bin/python3.8

import rosbag
import sys

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
sys.path.insert(1, "./build/proto")
from routing.routing_pb2 import RoutingResponse
# fmt: on

if __name__ == '__main__':

    if len(sys.argv) == 1:
        print(sys.argv[0], " input_bag_path")
        sys.exit()

    with rosbag.Bag(sys.argv[1]) as bag:
        for topic, msg, t in bag.read_messages(topics=["/planner/routing_response"], raw=True):
            # skip first 4 bytes
            data = msg[1][4:]
            routing_response = RoutingResponse()
            routing_response.ParseFromString(data)

            print(routing_response.header.timestamp_sec)
