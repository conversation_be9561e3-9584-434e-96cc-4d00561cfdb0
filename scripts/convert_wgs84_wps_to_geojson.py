#!/usr/bin/python3.8
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, Feature, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj
import os



def convert(wps_txt):
    
    proj = LocalUtmTransformer(107.633242, 29.988549)
    file = open(wps_txt).readlines()
    wps = []

    import os
    out_path = os.path.join(os.path.dirname(
        wps_txt), "wps.geojson")
    features = []

    for line in file:
        
        line = line.split()
        print(line)
        
        lat = line[0]
        lon = line[1]
        
        utm_x, utm_y = proj.LlToLocalUtm(lat=lat, lon=lon)

        point = Point((utm_x, utm_y))
        features.append(
            Feature(geometry=point, properties={"timestamp": 1}))

        feature_collection = FeatureCollection(features)

        with open(out_path, 'w') as f:
            dump(feature_collection, f)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "wps output")
        sys.exit()

    convert(sys.argv[1])
