#!/usr/bin/python3.8
import numpy as np
import sys
import os
from geojson_tools_2 import <PERSON>gWalker


def process_folder(dir, out):
    folders = os.listdir(dir)
    folders.sort()
    n = len(folders)

    # chongqing
    # bw = BagWalker(region_lat=29.988549, region_lon=107.633242)
    # shenzhen
    bw = BagWalker()

    for idx, folder in enumerate(folders):
        bag_path = os.path.join(dir, folder)
        print(bag_path)
        print(idx, "/", n)

        bw.ReadMessages(bag_path)

        # if idx % 10 == 0:
        #     bw.WriteGnssMeasurementsUtmToGeojson(out)
        #     bw.WriteGnssMeasurementsLatLongToTxt(out)
        #     bw.WriteGnssLonLatGCJ02ToGeoJson(out)
        #     bw.WriteLockOnRoadDebugToGeoJson(out)

    # bw.WriteLockOnRoadDebugToGeoJson(out)
    # bw.WriteGnssLonLatGCJ02ToGeoJson(out)
    bw.WriteGnssMeasurementsUtmToGeojson(out)
    bw.WriteGnssMeasurementsLatLongToTxt(out)

    print("All done.")


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "input_dir output")
        sys.exit()

    if not os.path.exists(sys.argv[2]):
        os.mkdir(sys.argv[2])

    process_folder(sys.argv[1], sys.argv[2])
