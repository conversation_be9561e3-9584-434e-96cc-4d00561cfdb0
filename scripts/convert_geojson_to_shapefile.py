import geopandas


# input = "/home/<USER>/map_matching/data/sz_small/modified_according_to_manual/sd_reformulated_according_to_manual.geojson"
# output = "/home/<USER>/map_matching/data/sz_small/modified_according_to_manual/sd_reformulated_according_to_manual.shp"
# myshpfile = geopandas.read_file(input)
# myshpfile.to_file(output)

def convert(infile, outfile):
    myshpfile = geopandas.read_file(infile)
    myshpfile.to_file(outfile)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "in out")
        sys.exit()

    convert(sys.argv[1], sys.argv[2])
