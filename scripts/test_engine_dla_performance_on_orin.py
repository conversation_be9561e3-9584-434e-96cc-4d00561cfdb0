
import sys
import os
sys.path.append("/opt/deeproute/common/inference_engine")
from frontend_python.engine_interface import deeproute_engine
from frontend_python.basic_types import *
import numpy as np

libso = "/opt/deeproute/common/inference_engine/lib/libplugins.so"
dla_model_path = "/opt/deeproute/ddmm-model/weights/ddmm.dla"

input_shape_0 = [1, 9, 160, 160]
output_shape_0 = [1, 1, 160, 160]
output_shape_1 = [1, 2, 1, 1]

out_shape = [output_shape_0, output_shape_1]

infer_engine = deeproute_engine.DeeprouteInference()
infer_engine.load_library(libso)
infer_engine.set_enable_dla(True)
infer_engine.create_engine_v2()

infer_engine.set_model_framework_type(MODEL_FRAMEWORK_PYTORCH)
offline_engine = os.fsencode(dla_model_path)
infer_engine.load_offline_engine(offline_engine)

def setup_engine_max_input_shape(infer_engine, input_names, max_input_shape_list):
    for i, input_name in enumerate(input_names):
        infer_engine.register_max_input_shape(input_name, max_input_shape_list[i])
        
setup_engine_max_input_shape(infer_engine, [b"input_data_0"], [input_shape_0])

db_size = 4
out_buffer = []
for i in range(len(out_shape)):
    buffer = infer_engine.allocate_host_memory(np.prod(np.array(out_shape[i])) * db_size)
    infer_engine.register_output(os.fsencode("output_data_" + str(i)), buffer)
    print(os.fsencode("output_data_" + str(i)))
    out_buffer.append(buffer)
infer_engine.build_engine()

infer_engine.register_infer_input_shape(b"input_data_0", input_shape_0)
print(f"Build engine done.")


all_infer_times = []
num=2000
for i in range(num):
    input_data = np.random.rand(1, 9, 160, 160).astype(np.float32)
    infer_engine.register_infer_input_data(b"input_data_0", input_data)
    infer_engine.inference(1, 0)
    infer_time = infer_engine.get_infer_time()
    all_infer_times.append(infer_time)
    print(f"{i}/{num} inference_time = {infer_time}")


import matplotlib.pyplot as plt
time_steps = range(len(all_infer_times))
plt.plot(time_steps, all_infer_times)
plt.xlabel('Epochs')
plt.ylabel('Milliseconds (ms)')
plt.title('DLA processing time')
plt.grid(True)
plt.savefig('plot_ms_values.png')  # Change the file extension as needed (e.g., .jpg, .pdf)
plt.close()
