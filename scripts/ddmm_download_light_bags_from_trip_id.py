#!/usr/bin/python3

from dplib import Dp<PERSON>ng<PERSON>, DownloadService
import argparse

def download_bags(trip_id, save_root):
    eng = DpEngine()
    ds = DownloadService()
    trip_bag_list = eng.get_bag_list(trip_id=trip_id)
    
    all_light_bags = []
    for bag in trip_bag_list:
        if 'Light_Topic_Group' in bag:
            all_light_bags.append(bag)
    
    index = 0
    for bag in trip_bag_list:
        if 'Light_Topic_Group' in bag:
            index += 1
            print(index, "/", len(all_light_bags))
            ds.download([bag], save_root)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download bags from a specific trip.")
    parser.add_argument("--trip_id", type=int, required=True, help="Trip ID to download bags from.")
    parser.add_argument("--save_root", type=str, required=True, help="Root directory to save downloaded bags.")

    args = parser.parse_args()

    download_bags(args.trip_id, args.save_root)
