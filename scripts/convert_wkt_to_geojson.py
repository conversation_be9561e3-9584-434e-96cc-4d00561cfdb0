import geojson
import shapely.wkt
from geojson import Point, Feature, FeatureCollection, dump

s = 'LINESTRING(18656.3572655 -4236.49656991,18656.5076072 -4236.26298779,18656.6238003 -4236.08246134,18656.807705 -4235.7967333,18656.9573032 -4235.56430623,18657.110139 -4235.3268491,18657.260594 -4235.09309085,18657.3812569 -4234.90561995,18657.5609062 -4234.62650327,18657.7115752 -4234.39241264,18657.8615634 -4234.15937976,18658.0115458 -4233.92635572,18658.1715334 -4233.67778711,18658.3122909 -4233.45909556,18658.4613171 -4233.22755726,18658.6120062 -4232.99343538,18658.7627116 -4232.75928826,18658.8835498 -4232.57154493,18659.0644579 -4232.29047249,18659.2152917 -4232.05612573,18659.3652181 -4231.82318885,18659.5160362 -4231.58886651,18659.6767672 -4231.3391427,18659.8175337 -4231.12043733,18659.9688529 -4230.88533654,18660.1197804 -4230.65084424,18660.270028 -4230.41740831,18660.4245079 -4230.17739662,18660.5717234 -4229.94867156,18660.7222654 -4229.71477821,18660.8730455 -4229.48051496,18661.0240559 -4229.24589389,18661.184954 -4228.99591047,18661.3260742 -4228.7766555,18661.4767571 -4228.54254328,18661.6276654 -4228.30808085,18661.7806407 -4228.07040692,18661.8918919 -4227.89755866,18662.0822637 -4227.60178283,18662.2339573 -4227.36610025,18662.3843911 -4227.13237501,18662.5355595 -4226.89750835,18662.6965641 -4226.64735958,18662.8376198 -4226.42820489,18662.9887312 -4226.19342673,18663.1395523 -4225.95909978,18663.2900815 -4225.72522642,18663.4017333 -4225.55175576,18663.5920541 -4225.25605903,18663.7438699 -4225.02018654,18663.89471 -4224.78583009,18664.0460766 -4224.55065558,18664.2075081 -4224.29984354,18664.3488064 -4224.0803119,18664.4988561 -4223.84718332,18664.6493303 -4223.61339533,18664.8003687 -4223.37873074,18664.9188143 -4223.19470468,18665.0626872 -4222.97117309,18665.250013 -4222.68012957,18665.4362048 -4222.39084796,18665.5499659 -4222.21410026,18665.7101995 -4221.96514934,18665.8847404 -4221.69396948,18666.0367202 -4221.45784224,18666.1875825 -4221.22345113,18666.3376005 -4220.99037192,18666.4181136 -4220.8652807,18666.6038217 -4220.57675072,18666.7898458 -4220.28772967,18666.9403702 -4220.05386363,18667.0904878 -4219.82062965,18667.2158968 -4219.62578491,18667.3900084 -4219.35527202,18667.5413123 -4219.12019499,18667.6919987 -4218.88607718,18667.8418938 -4218.65318889,18667.9250152 -4218.52404528,18668.0538129 -4218.32393551,18668.2553725 -4218.01077734,18668.4410402 -4217.72231004,18668.5915005 -4217.48854367,18668.7422701 -4217.25429668,18668.889435 -4217.0256503,18669.0421659 -4216.78835607,18669.1921246 -4216.55536899,18669.3423876 -4216.32190914,18669.3964237 -4216.2379546,18669.4020293 -4216.22685266,18669.5274146 -4215.97852788,18669.6527236 -4215.73035402,18669.7782593 -4215.48173141,18669.9032783 -4215.23413191,18670.0286394 -4214.98585504,18670.1525188 -4214.7405127,18670.2774936 -4214.49300079,18670.4026087 -4214.24521117,18670.5295474 -4213.99380973,18670.6529442 -4213.74942316,18670.7782662 -4213.50122373,18670.9033408 -4213.25351419,18671.0286639 -4213.00531247,18671.1535319 -4212.75801212,18671.2787026 -4212.51011229,18671.4042408 -4212.26148469,18671.5291576 -4212.01408774,18671.6547018 -4211.76544822,18671.7906166 -4211.49626974,18671.9056539 -4211.26843904,18672.0313285 -4211.01954123,18672.1564444 -4210.7717499,18672.28154 -4210.52399886,18672.3861781 -4210.31676378,18672.5320203 -4210.02792414,18672.6572386 -4209.77992998,18672.7825827 -4209.53168678,18672.9078818 -4209.28353254,18673.0345393 -4209.03268811,18673.1578431 -4208.78848584,18673.2829864 -4208.54064029,18673.408499 -4208.29206322,18673.5333744 -4208.04474828,18673.6582892 -4207.79735515,18673.7835402 -4207.54929644,18673.9086978 -4207.30142258,18674.0340234 -4207.05321586,18674.1943666 -4206.74222152,18674.3266803 -4206.49015015,18674.452258 -4206.25091152,18674.5810167 -4206.00561279,18674.7107541 -4205.75844947,18674.8392693 -4205.51361454,18674.9683923 -4205.26762185,18675.0975889 -4205.02148888,18675.2274918 -4204.77401014,18675.356589 -4204.52806662,18675.485917 -4204.28168321,18675.6255106 -4204.01574286,18675.7431337 -4203.79165861,18675.8720728 -4203.54601627,18676.0012373 -4203.29994441,18676.1300182 -4203.05460328,18676.2591694 -4202.8085569,18676.3876491 -4202.56378961,18676.5130824 -4202.32482613,18676.6418815 -4202.07945037,18676.7705859 -4201.83425506,18676.9105022 -4201.56769995,18677.028409 -4201.34307512,18677.1574227 -4201.09729064,18677.2861672 -4200.85201881,18677.4149855 -4200.60660666,18677.5433033 -4200.36214777,18677.7067034 -4200.05085361,18677.8357816 -4199.80494623,18677.9645023 -4199.55971983,18678.0590312 -4199.37963237,18678.1995509 -4199.11192769,18678.3515753 -4198.82230542,18678.4807289 -4198.57625436,18678.6092859 -4198.33133983,18678.7041114 -4198.15068727,18678.8673148 -4197.83976788,18678.9960526 -4197.59450885,18679.1275488 -4197.34399497,18679.2557663 -4197.09972711,18679.3849033 -4196.85370781,18679.5172781 -4196.60152004,18679.6427783 -4196.36242894,18679.7717656 -4196.11669474,18679.9012068 -4195.87009569,18680.030221 -4195.62431028,18680.1597791 -4195.37748847,18680.2894076 -4195.13053269,18680.4204981 -4194.88079174,18680.5502708 -4194.63356122,18680.6799536 -4194.38650183,18680.8128431 -4194.13333349,18680.9393053 -4193.89240981,18681.0686258 -4193.64604078,18681.1980147 -4193.39954148,18681.3280039 -4193.15189848,18681.4576913 -4192.90483046,18681.5875957 -4192.65734908,18681.7172113 -4192.41041784,18681.8457415 -4192.16555434,18681.9760645 -4191.91727539,18682.1085508 -4191.6648753,18682.2350076 -4191.42396173,18682.3655261 -4191.17531051,18682.4955493 -4190.92760274,18682.6258249 -4190.67941404,18682.7557829 -4190.43183054,18682.8826614 -4190.19011382,18683.0125109 -4189.94273695,18683.1425394 -4189.69501904,18683.2727752 -4189.4469063,18683.4064937 -4189.19215857,18683.5330425 -4188.95106987,18683.6633547 -4188.70281159,18683.7935648 -4188.45474779,18683.9237767 -4188.20668059,18684.0535408 -4187.95946641,18684.1859724 -4187.70717038,18684.3160406 -4187.45937689,18684.4462289 -4187.21135466,18684.5768168 -4186.96257104,18684.6740677 -4186.77729799,18684.8375758 -4186.46579791,18684.9678265 -4186.21765681,18685.0982478 -4185.96919059,18685.2285207 -4185.72100716,18685.3587319 -4185.47294116,18685.4887775 -4185.2251908,18685.6203483 -4184.97453477,18685.750772 -4184.72606403,18685.880596 -4184.47873572,18686.0138512 -4184.22487073,18686.1407727 -4183.98307201,18686.2707565 -4183.73543922,18686.4010203 -4183.48727321,18686.5310494 -4183.23955411,18686.6617599 -4182.99053701,18686.7919779 -4182.74245822,18686.9236407 -4182.49162678,18687.0544821 -4182.24236031,18687.1844426 -4181.99477199,18687.3178516 -4181.74061397,18687.4453541 -4181.49770836,18687.5402599 -4181.31690294,18687.7053812 -4181.00232967,18687.8357614 -4180.75394172,18687.9660405 -4180.50574652,18688.1284573 -4180.19632547,18688.2262497 -4180.01002082,18688.357667 -4179.75965706,18688.4882153 -4179.51094902,18688.6208143 -4179.25833401,18688.746927 -4179.01807625,18688.8776959 -4178.76894778,18689.0380441 -4178.46346789,18689.1375259 -4178.27394465,18689.2973935 -4177.96938014,18689.4269016 -4177.72265379,18689.5264525 -4177.53299887,18689.6515559 -4177.29466378,18689.8198792 -4176.97399031,18689.914648 -4176.79344593,18690.0718906 -4176.49388235,18690.201856 -4176.2462847)'

# Convert to a shapely.geometry.polygon.Polygon object
g1 = shapely.wkt.loads(s)

g2 = geojson.Feature(geometry=g1, properties={})

print(g2)

with open('poses.geojson', 'w') as f:
   dump(g2, f)