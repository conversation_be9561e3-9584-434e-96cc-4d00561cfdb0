#!/usr/bin/python3.8
import sys
import json
from google.protobuf import json_format
import rosbag

sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")

from routing.routing_pb2 import RoutingResponse


def main():
    if len(sys.argv) == 1:
        print(sys.argv[0], "rosbag")
        sys.exit()
    with rosbag.Bag(sys.argv[1]) as bag:
    # with rosbag.Bag("/media/zyuzhi/zyuzhi_data/data/jira/LOC-258/YR-MAR-5_20220619_070613.Necessary.bag") as bag:
        for topic, msg, t in bag.read_messages(topics="/planner/routing_response", raw=True):
            # skip first 4 bytes
            data = msg[1][4:]
            routing_response = RoutingResponse()
            routing_response.ParseFromString(data)
            routing_response_str = json_format.MessageToJson(routing_response)
            print(routing_response_str)
            break


if __name__ == "__main__":
    main()
