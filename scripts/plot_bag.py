import numpy as np
import rosbag
import copy


# fmt: off
import sys
sys.path.insert(0, "/home/<USER>/lane-based-localization-benchmark/build/proto")
from semantic_map.map_pb2 import Map
from geo_referencing_result_pb2 import GeoReferencingResults, GeoReferencingResult
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from routing.routing_pb2 import RoutingResponse
# fmt: on


def change_detection(values):
    change_pts = []

    prev_value = values[0]
    for idx, value in enumerate(values[1:]):
        if value != prev_value:
            change_pts.append(idx + 1)

        prev_value = value

    return change_pts


def plot(bag_path):

    lock_on_lane_topic = "/localization/geo_ref_result"
    topic_list = [lock_on_lane_topic]
    print("Processing: ", bag_path)

    # invalid = []
    # edge_types = []
    dist_to_edge_starts = []
    gnss_types = []
    prev_edge = -1
    all_dists = []
    tss = []
    poses = []
    vss = []
    
    lane_idx_obs = []
    lane_idx_est = []
    with rosbag.Bag(bag_path) as bag:
        for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):

            # skip first 4 bytes
            data = msg[1][4:]
            if topic == lock_on_lane_topic:
                geo = GeoReferencingResult()
                geo.ParseFromString(data)
                
                print(geo)
                
                if geo.sd_link_id != prev_edge:
                    prev_edge = geo.sd_link_id

                    dist_to_edge_starts.clear()

                all_dists.append(geo.sd_distance_to_link_start)
                poses.append([geo.position.x, geo.position.y, geo.position.z])
                dist_to_edge_starts.append(int(geo.sd_distance_to_link_start))
                gnss_types.append(geo.gnss_type)
                tss.append(geo.time_us)
                
                lane_idx_obs.append(int(geo.obs_lane_index))
                lane_idx_est.append(int(geo.sd_lane_index))

    import matplotlib.pyplot as plt
    gnss_type_change_pts = change_detection(gnss_types)
    print(gnss_type_change_pts)

    for i in range(len(all_dists)-2):
        prev = all_dists[i]
        mid = all_dists[i+1]
        next = all_dists[i+2]
        if(mid < prev and mid < next):
            print(i+1)

    t1 = tss[891]
    t2 = tss[1017]
    print(abs(t1-t2)/1e6)

    p1 = np.array(poses[891])
    p2 = np.array(poses[1017])
    eu_dist = np.linalg.norm(p1-p2)
    print(eu_dist)  # 135.23m

    sp1 = all_dists[891]
    sp2 = all_dists[1017]
    print(sp1-sp2)  # 188m

    ax1 = plt.subplot()
    l1, = ax1.plot(all_dists, color='red')
    ax2 = ax1.twinx()
    l2, = ax2.plot(gnss_types, color='orange')
    plt.legend([l1, l2], ["dists to link start", "gnss type"])

    for gnss_type_change_pt in gnss_type_change_pts:
        plt.axvline(x=gnss_type_change_pt, color='b', ls="--")
        plt.text(gnss_type_change_pt, 1, "11")

    plt.show()
    
    if len(lane_idx_obs) and len(lane_idx_est):
        plt.plot(lane_idx_est)
        plt.plot(lane_idx_obs)
        plt.show()
    

if __name__ == '__main__':
    
    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag")
        sys.exit()

    plot(sys.argv[1])
