import matplotlib.pyplot as plt
import shapefile as shp  # Requires the pyshp package
from fmm import Network, NetworkGraph, STMATCH, STMATCHConfig
from shapely.wkt import loads
from shapely.geometry import Point, LineString


def pose_file_to_times(pose_file):
    file = open(pose_file).readlines()
    times = []
    for idx, line in enumerate(file):

        line = line.split()
        t = line[0]

        times.append(t)

    print("time len: ", len(times))

    return times


def pose_file_to_wkt(pose_file):

    file = open(pose_file).readlines()

    positions = []
    for idx, line in enumerate(file):

        line = line.split()
        x = float(line[1])
        y = float(line[2])

        if abs(x) > 1e6 or abs(y) > 1e6:
            continue

        positions.append([x, y])

    print("pose len: ", len(positions))

    wkt = LineString(positions).wkt
    return wkt


def write_gt_to_file(times, traj, all_edge_ids, matched_pts, output_path):
    f = open(output_path, "w")

    pts_num = len(traj.coords)
    assert(pts_num == len(all_edge_ids))
    assert(pts_num == len(matched_pts.coords))

    for i in range(pts_num):
        line = str(times[i]) + " " + str(round(traj.coords[i][0], 6)) + " " + str(round(traj.coords[i][1], 6)) + " " + str(all_edge_ids[i]) + \
            " " + str(round(matched_pts.coords[i][0], 6)) + \
            " " + str(round(matched_pts.coords[i][1], 6)) + "\n"
        f.write(line)

    f.close()


def run(map_path, pose_txt_file_path, output_path):

    network = Network(map_path)
    graph = NetworkGraph(network)
    print(graph.get_num_vertices())
    model = STMATCH(network, graph)
    wkt = pose_file_to_wkt(pose_txt_file_path)
    config = STMATCHConfig()
    config.k = 8
    config.gps_error = 5
    config.radius = 20
    config.vmax = 30
    config.factor = 1.5
    result = model.match_wkt(wkt, config)
    print("Opath ", list(result.opath), len(result.opath))
    print("Cpath ", list(result.cpath), len(result.cpath))
    # print("WKT ", result.mgeom.export_wkt())

    all_single_edge_ids = list(result.opath)
    traj = loads(wkt)
    matched_pts = loads(result.pgeom.export_wkt())

    dpi = 96
    plt.figure(figsize=(1680/dpi, 1680/dpi), dpi=dpi)
    sf = shp.Reader(map_path)
    for shape in sf.shapeRecords():

        edge_linestring = LineString(shape.shape.points)
        if(edge_linestring.distance(traj) > 100):
            continue

        x = [i[0] for i in shape.shape.points[:]]
        y = [i[1] for i in shape.shape.points[:]]

        print(shape.shape)
        plt.plot(x, y, color='g')

    # pts = []
    xs = []
    ys = []
    for traj_pt in traj.coords:
        xs.append(traj_pt[0])
        ys.append(traj_pt[1])
    plt.scatter(xs, ys, color='r', alpha=0.5, s=2)

    mxs = []
    mys = []
    for matched_pt in matched_pts.coords:
        mxs.append(matched_pt[0])
        mys.append(matched_pt[1])
    plt.scatter(mxs, mys, color='b', alpha=0.5, s=2)

    for i in range(len(mxs)):
        plt.plot([xs[i], mxs[i]], [ys[i], mys[i]], alpha=0.1)

    import os
    image_path = os.path.join(output_path, "result_image.png")
    plt.savefig(image_path)

    gt_path = os.path.join(output_path, "gt.txt")
    times = pose_file_to_times(pose_txt_file_path)
    write_gt_to_file(times, traj, all_single_edge_ids, matched_pts, gt_path)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "map_path, input_txt_pose_path,  output")
        sys.exit()

    bw = run(sys.argv[1], sys.argv[2], sys.argv[3])
