#!/usr/bin/python3.8
import sys
import matplotlib.pyplot as plt

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
from perception.deeproute_perception_ras_map_pb2 import RASMap
# fmt: on

from mpl_toolkits import mplot3d
import numpy as np

def plot(file):
    f = open(file, "rb")
    ras = RASMap()
    ras.ParseFromString(f.read())
    
    fig = plt.figure()
    ax = plt.axes(projection='3d')

    for lane in ras.lanes:
        xs = []
        ys = []
        zs = []
        for pt in lane.centerline.point:
            xs.append(pt.x)
            ys.append(pt.y)
            zs.append(pt.z)
        
        print("Lane is virutal?:", lane.virtual)
        ax.scatter3D(xs, ys, zs, c='r', cmap='Greens')
        # ax.scatter3D([lane.centerline.point[0].x], [lane.centerline.point[0].y], [lane.centerline.point[0].z], c='r', cmap='Greens', s=1)
        # ax.scatter3D([lane.centerline.point[-1].x], [lane.centerline.point[-1].y], [lane.centerline.point[-1].z], c='g', cmap='Greens', s=1)
        
    
    for boudary in ras.boundary:
        xs = []
        ys = []
        zs = []
        for pt in boudary.boundary.point:
            xs.append(pt.x)
            ys.append(pt.y)
            zs.append(pt.z)
        ax.scatter3D(xs, ys, zs, c='g', cmap='Greens')
        
    for edge in ras.edge:
        xs = []
        ys = []
        zs = []
        for pt in edge.polyline.point:
            xs.append(pt.x)
            ys.append(pt.y)
            zs.append(pt.z)
        # plt.plot(xs, ys)
        print(edge.id)
        ax.scatter3D(xs, ys, zs, c='b', cmap='Greens')
        # ax.scatter3D([edge.polyline.point[0].x], [edge.polyline.point[0].y], [edge.polyline.point[0].z], c='r', cmap='Greens', s=15)
        # ax.scatter3D([edge.polyline.point[-1].x], [edge.polyline.point[-1].y], [edge.polyline.point[-1].z], c='g', cmap='Greens', s=15)

    ax.set_xlabel('X-axis', fontweight ='bold')
    ax.set_ylabel('Y-axis', fontweight ='bold')
    ax.set_zlabel('Z-axis', fontweight ='bold')
    plt.title("red: lane center line \n green: lane boundary \n blue: road edge")
    plt.show()

if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "cfg")
        sys.exit()

    plot(sys.argv[1])
