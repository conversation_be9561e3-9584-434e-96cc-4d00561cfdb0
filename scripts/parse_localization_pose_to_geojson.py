#!/usr/bin/python3.8
import geojson
import pprint
import rosbag
import sys
from geojson import Point, Feature, FeatureCollection, dump

sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")

from drivers.gnss.ins_pb2 import Ins


if __name__ == '__main__':
    if len(sys.argv) == 1:
        print(sys.argv[0], "rosbag1 rosbag2 ...")
        sys.exit()
    features = []
    rosbag_files = sys.argv[1:]
    msg_count = 0
    for rosbag_file in rosbag_files:
        print(f"parsing {rosbag_file}")
        with rosbag.Bag(rosbag_file) as bag:
            for topic, msg, t in bag.read_messages(topics="/localization/pose", raw=True):
                msg_count += 1
                if msg_count % 50 != 0:
                    continue
                data = msg[1][4:]
                ins = Ins()
                ins.ParseFromString(data)
                features.append(Feature(geometry=Point(
                    (ins.position.x, ins.position.y))))
    
    print("Added ", len(features), " points!")
    feature_collection = FeatureCollection(features)

    with open('localization_pose.geojson', 'w') as f:
        dump(feature_collection, f)
