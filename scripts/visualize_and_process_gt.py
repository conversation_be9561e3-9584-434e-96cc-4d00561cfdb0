#!/usr/bin/python3.8
from email import message
from time import sleep
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, Feature, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj

import rospy
from std_msgs.msg import Int64
from std_srvs.srv import SetBool
from geometry_msgs.msg import Point
from visualization_msgs.msg import Marker

import shapely
from shapely.geometry import LineString as sLineString
from shapely.geometry import Point as sPoint


# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
sys.path.insert(1, "./build/proto")
from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from routing.routing_pb2 import RoutingResponse
from ground_truth_pb2 import GroundTruthMeasurements, GroundTruthMeasurement
# fmt: on


def LoadLaneBoundaryDictFromSemanticMap(path):
    file = open(path, 'rb')
    map = Map()
    map.ParseFromString(file.read())
    
    boundary_dict = {}
    for boundary in map.lane_boundaries:
        boundary_dict[boundary.id] = []
        for idx, pt in enumerate(boundary.boundary.point):
            # if(idx%5 == 0):
            #     boundary_dict[boundary.id].append(pt)
            boundary_dict[boundary.id].append(pt)
            
    lane_dict = {}
    for lane in map.lane:
        left_boundary_pts = boundary_dict[lane.left_boundary_id]
        right_boundary_pts = boundary_dict[lane.right_boundary_id]
        
        # print("lane.right_boundary.boundary.point size: ", len(lane.right_boundary.boundary.point))
        # print("left_boundary_pt: ", left_boundary_pts)
        # print("right_boundary_pt: ", right_boundary_pts)
        lane_dict[lane.id] = left_boundary_pts + right_boundary_pts

    print("Total semantic map lane num: ", len(lane_dict))
    return lane_dict

def LoadGt(path):
    gt = GroundTruthMeasurements()
    with open(path, 'rb') as f:
        gt.ParseFromString(f.read())

    for gt in gt.ground_truth:
        print(gt)


class Visualizer(object):
    __link_dict = {}
    __origin_coord = None
    __utm_transformer = GcjToLocalUtmTransformer()
    __color_list = [[1, 0, 0], [0, 1, 0]]
    pose_pub = rospy.Publisher("/dr/localization/poses", Marker, queue_size=10)
    lane_pub = rospy.Publisher("/dr/same_direction_lanes", Marker, queue_size=10)
    link_pub = rospy.Publisher("/siwei/routing_links", Marker, queue_size=10)
    pose_to_link_pub = rospy.Publisher(
        "/siwei/pose_to_link_projections", Marker, queue_size=10)
    __kdtree = None
    __link_ids = []
    __link_mean_positions = []

    __gts = []
    __gt = None

    __all_same_direction_lane_ids = []
    __all_same_direction_lane_points = []
    __lane_dict = None

    def __init__(self, poses: str, siwei_sd_link: str, dr_hd_map: str):
        # load gts
        self.__gt = GroundTruthMeasurements()
        with open(poses, 'rb') as f:
            self.__gt.ParseFromString(f.read())

        for gt in self.__gt.ground_truth:
            if self.__origin_coord == None:
                self.__origin_coord = [gt.position.x, gt.position.y]

            self.__gts.append(gt)

        # process links
        links = geojson.load(open(siwei_sd_link))
        links = links["features"]
        for link in links:
            link_id = link["properties"]["ID"]
            self.__link_ids.append(link_id)

            self.__link_dict[link_id] = []
            for coord in link['geometry']['coordinates']:
                assert(self.__origin_coord is not None)

                self.__link_dict[link_id].append(
                    np.array(coord) - np.array(self.__origin_coord))

            self.__link_mean_positions.append(
                np.mean(self.__link_dict[link_id], 0))

        print("Loaded ", len(self.__gts), " gts.")

        self.__lane_dict = LoadLaneBoundaryDictFromSemanticMap(dr_hd_map)
        for gt in self.__gts:
            left_lane_ids = [id for id in gt.left_lane_ids]
            right_lane_ids = [id for id in gt.right_lane_ids]
            all_lane_ids = left_lane_ids + right_lane_ids + [gt.current_lane_id]
            # print("all_lane_ids: ", all_lane_ids)
            for lane_id in all_lane_ids:
                self.__all_same_direction_lane_ids.append(lane_id)

        for lane_id in self.__all_same_direction_lane_ids:
            lane_boundary_points = self.__lane_dict[lane_id]
            # print("lane_boundary_points: ", lane_boundary_points)
            self.__all_same_direction_lane_points += lane_boundary_points

    def SaveProcessedGt(self, path):
        f = open(path, "wb")
        f.write(self.__gt.SerializeToString())
        f.close()

    def FindLaneIndexByPose(self, pose):
        pass

    def FindNearestLinkByPose(self, pose):

        point = sPoint(pose[0], pose[1])

        all_polys = []
        for link_id in self.__link_dict:
            link_pts = self.__link_dict[link_id]
            polyline = sLineString([link_pt for link_pt in link_pts])
            all_polys.append(polyline)

        min_poly = min(all_polys, key=point.distance)

        index = all_polys.index(min_poly)
        queried_link_id = self.__link_ids[index]

        return queried_link_id, None, None

    def FindProjectedPointOnPolyLine(self, link_id, pose):
        link_id = str(link_id)
        assert(link_id in self.__link_dict)

        link_points = self.__link_dict[link_id]

        polyline = sLineString([link_pt for link_pt in link_points])
        list(polyline.coords)

        pt = sPoint(pose[0], pose[1])

        # length along the link
        distance = polyline.project(pt)

        projected_pt = polyline.interpolate(polyline.project(pt))

        return distance, projected_pt

    def CreateTxt(self, id, msg, position):
        marker = Marker()
        marker.header.frame_id = "map"
        marker.header.stamp = rospy.Time.now()
        marker.id = id
        marker.action = Marker.ADD
        marker.type = Marker.TEXT_VIEW_FACING
        marker.ns = "links"
        marker.scale.z = 2
        marker.color.r = 1
        marker.color.g = 0
        marker.color.b = 0
        marker.color.a = 1
        marker.pose.orientation.w = 1
        marker.text = msg
        marker.pose.position.x = position.x
        marker.pose.position.y = position.y
        marker.pose.position.z = 5
        return marker

    def PublishLocalizationPoses(self):
        idx = 0

        marker_poses = Marker()
        marker_poses.header.frame_id = "map"
        marker_poses.header.stamp = rospy.Time.now()
        marker_poses.id = idx
        marker_poses.action = Marker.ADD
        marker_poses.type = Marker.POINTS
        marker_poses.ns = "links"
        marker_poses.scale.x = 1
        marker_poses.scale.y = 1
        marker_poses.color.r = 1
        marker_poses.color.g = 1
        marker_poses.color.b = 0
        marker_poses.color.a = 1
        marker_poses.pose.orientation.w = 1

        for gt_id, gt in enumerate(self.__gts):

            if gt_id % 50 != 0:
                continue

            point = Point()
            point.x = gt.position.x - np.array(self.__origin_coord)[0]
            point.y = gt.position.y - np.array(self.__origin_coord)[1]
            point.z = 1
            marker_poses.points.append(point)

            idx += 1

            link_id, _, _ = self.FindNearestLinkByPose(
                [point.x, point.y])
            distance_along_link, projected_pt = self.FindProjectedPointOnPolyLine(
                link_id, [point.x, point.y])

            self.__gt.ground_truth[gt_id].link_id = np.int32(link_id)
            self.__gt.ground_truth[gt_id].distance_to_link_start = distance_along_link

            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = rospy.Time.now()
            marker.id = idx
            marker.action = Marker.ADD
            marker.type = Marker.LINE_STRIP
            marker.ns = "links"
            marker.scale.x = 1
            marker.color.r = 0
            marker.color.g = 0
            marker.color.b = 1
            marker.color.a = 1
            marker.pose.orientation.w = 1

            marker.points.append(point)

            target_pt = Point()
            target_pt.x = projected_pt.x
            target_pt.y = projected_pt.y
            target_pt.z = 1

            src_to_tgt_distance = np.linalg.norm(
                np.array([point.x, point.y])-np.array([projected_pt.x, projected_pt.y]))
            if src_to_tgt_distance > 30:
                continue

            marker.points.append(target_pt)

            msg = str(distance_along_link)[:4] + "+"+str(self.__gt.ground_truth[gt_id].lane_index + 1) + \
                "/" + \
                str(self.__gt.ground_truth[gt_id].lane_sum) + \
                "+\n" + str(link_id)
            self.pose_to_link_pub.publish(
                self.CreateTxt(gt_id, msg, projected_pt))
            self.pose_to_link_pub.publish(marker)

        for _ in range(5):
            self.pose_pub.publish(marker_poses)
            sleep(0.1)
            
    def PubSameDirectionLanes(self):
        
        idx = 0
        marker_poses = Marker()
        marker_poses.header.frame_id = "map"
        marker_poses.header.stamp = rospy.Time.now()
        marker_poses.id = idx
        marker_poses.action = Marker.ADD
        marker_poses.type = Marker.POINTS
        marker_poses.ns = "links"
        marker_poses.scale.x = 1
        marker_poses.scale.y = 1
        marker_poses.color.r = 0
        marker_poses.color.g = 0
        marker_poses.color.b = 1
        marker_poses.color.a = 1
        marker_poses.pose.orientation.w = 1

        print("self.__all_same_direction_lane_points size: ", len(self.__all_same_direction_lane_points))
        
        for idx, pt in enumerate(self.__all_same_direction_lane_points):
            point = Point()
            point.x = pt.x - np.array(self.__origin_coord)[0]
            point.y = pt.y - np.array(self.__origin_coord)[1]
            point.z = 1
            # print(point)
            marker_poses.points.append(point)
            
        
        self.lane_pub.publish(marker_poses)
        

        
    def PublishRoutingLinks(self):
        idx = 0
        for link in self.__link_dict:
            link_id = link
            link_pts = self.__link_dict[link]

            color = self.__color_list[idx % len(self.__color_list)]
            marker = Marker()
            marker.header.frame_id = "map"
            marker.header.stamp = rospy.Time.now()
            marker.id = idx
            marker.action = Marker.ADD
            marker.type = Marker.LINE_STRIP
            marker.ns = "links"
            marker.scale.x = 1
            marker.color.r = color[0]
            marker.color.g = color[1]
            marker.color.b = color[2]
            marker.color.a = 1
            marker.pose.orientation.w = 1

            for link_pt in link_pts:
                point = Point()
                point.x = link_pt[0]
                point.y = link_pt[1]
                point.z = 1
                marker.points.append(point)

            self.link_pub.publish(marker)
            sleep(0.005)

            idx += 1


if __name__ == '__main__':
    rospy.init_node('lane_loc_vis')

    vis = Visualizer("/media/songhaoran/data/lane_loc_data/YR-ALX-7_20220612_015444/splitted_bag/4/result/ground_truths.bin",
                     "/media/songhaoran/data/lane_loc_data/YR-ALX-7_20220612_015444/splitted_bag/4/result/sd_link.geojson",
                     "/media/songhaoran/data/lane_loc_data/semantic-map.bin")
    
    vis.PubSameDirectionLanes()
    print("publish same direction lane boundary points finished")
    vis.PublishLocalizationPoses()
    print("publish pose finished")
    vis.PublishRoutingLinks()
    print("publish link finished")

    vis.SaveProcessedGt(
        "/media/songhaoran/data/lane_loc_data/YR-ALX-7_20220612_015444/splitted_bag/4/result/ground_truths_full.bin")

    # LoadGt("/media/songhaoran/data/lane_loc_data/YR-ALX-7_20220612_015444/splitted_bag/4/result/ground_truths_full.bin")
