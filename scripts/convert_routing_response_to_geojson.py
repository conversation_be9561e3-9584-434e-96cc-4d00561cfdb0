import numpy as np
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from geojson import Point, Feature, FeatureCollection, dump


def convert(rr_txt, out):

    proj = LocalUtmTransformer(107.633242, 29.988549)
    file = open(rr_txt).readlines()
    wps = []

    import os
    out_path = out
    features = []

    idx = 0
    for line in file:
        idx += 1

        # if idx % (int(len(file)/10)) == 0:
            # print(idx, "/", len(file))

        line = line.split()[1:]

        for i in range(int(len(line)/2)):
            lat = float(line[2*i])
            lon = float(line[2*i+1])
            pt = [lat, lon]
            # print(pt)

            utm_x, utm_y = proj.LlToLocalUtm(lat=lat, lon=lon)

            point = Point((utm_x, utm_y))
            features.append(
                Feature(geometry=point, properties={"timestamp": 1}))

        feature_collection = FeatureCollection(features)

        with open(out_path, 'w') as f:
            dump(feature_collection, f)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "in output")
        sys.exit()

    convert(sys.argv[1], sys.argv[2])
