import matplotlib.pyplot as plt
from turtle import color
import geojson
from geojson import Point, Feature, FeatureCollection, dump
from datetime import datetime
import geopandas as gpd
import osmnx as ox

from leuvenmapmatching.matcher.distance import DistanceMatcher
from leuvenmapmatching.map.inmem import InMemMap
from leuvenmapmatching import visualization as mmviz

import transbigdata as tbd
import pandas as pd
from leuvenmapmatching.matcher.distance import DistanceMatcher
from leuvenmapmatching.matcher.newsonkrumm import NewsonKrummMatcher

from leuvenmapmatching.map.inmem import InMemMap
from leuvenmapmatching import visualization as mmviz


# 先用 scripts/geojson_tools.py 生成localization pose或者gnss pose的 geojson文件
# 关于如何获取pose在link上的具体匹配点，可以参考：https://github.com/wannesm/LeuvenMapMatching/issues/13
# 关于如何获取pose匹配好的subgraph： https://github.com/wannesm/LeuvenMapMatching/issues/12
# def LoadDualDirectionMap(path):
#     map = InMemMap(name='pNEUMA', use_latlon=False)

#     gj = geojson.load(open(path))
#     features = gj["features"]

#     for feature in features:
#         s = feature['properties']['SnodeID']
#         e = feature['properties']['EnodeID']
#         sp = feature['geometry']['coordinates'][0]
#         ep = feature['geometry']['coordinates'][-1]

#         map.add_node(s, (sp[0], sp[1]))
#         map.add_node(e, (ep[0], ep[1]))

#         # 这个部分用来解决link方向不连续的问题
#         map.add_edge(s, e)
#         map.add_edge(e, s)

#     return map

def LoadMap(path):
    map = InMemMap(name='pNEUMA', use_latlon=False)

    gj = geojson.load(open(path))
    features = gj["features"]

    edge_dict = {}
    edge_connection_graph = {}
    for feature in features:
        id = feature['properties']['ID']
        s = feature['properties']['SnodeID']
        e = feature['properties']['EnodeID']
        sp = feature['geometry']['coordinates'][0]
        ep = feature['geometry']['coordinates'][-1]

        map.add_node(s, (sp[0], sp[1]))
        map.add_node(e, (ep[0], ep[1]))
        map.add_edge(s, e)

        edge_dict[s, e] = id
        
        if s in edge_connection_graph:
            edge_connection_graph[s].append(id)
        else:
            edge_connection_graph[s] = []
      
        if e in edge_connection_graph:
            edge_connection_graph[e].append(id)
        else:
            edge_connection_graph[e] = []
                  
    return map, edge_dict, edge_connection_graph


def LoadGt(path):

    file = open(path).readlines()

    positions = []
    for idx, line in enumerate(file):

        if idx % 10 != 0:
            continue

        line = line.split()
        x = float(line[1])
        y = float(line[2])
        # y = float(line[2]) + 50 # for easier visualization

        if abs(x) > 1e6 or abs(y) > 1e6:
            continue

        positions.append([x, y])

    print("pose len: ", len(positions))
    return positions


def run_matching(map_path, pose_path, output):
    sd_map, edge_dict, edge_connection_graph = LoadMap(map_path)
    positions = LoadGt(pose_path)
    path = positions

    # matcher = DistanceMatcher(sd_map,
    #                           max_dist=200,
    #                           max_dist_init=200,
    #                           min_prob_norm=0.0001,
    #                           non_emitting_length_factor=0.95,
    #                           obs_noise=50,
    #                           obs_noise_ne=50,
    #                           dist_noise=50,
    #                           max_lattice_width=10,
    #                           non_emitting_states=True)

    # 这是HMM老祖宗的那个实现，做了很好的工程修改
    matcher = NewsonKrummMatcher(sd_map,
                                 max_dist=30,
                                 max_dist_init=50,
                                 min_prob_norm=0.0001,
                                 non_emitting_length_factor=0.95,
                                 obs_noise=50,
                                 obs_noise_ne=50,
                                 dist_noise=50,
                                 max_lattice_width=10,
                                 non_emitting_states=True)

    states, _ = matcher.match(path, unique=True)
    edges = matcher.path_pred

    print("States\n------")
    print(states)
    print("size: ", len(states))
    print("edges\n------")
    print(edges)
    print("size: ", len(edges))
    print("")
    matcher.print_lattice_stats()
    # print(matcher.path_pred)

    import os
    
    # open and read the file after the appending:
    ofile = os.path.join(output, "links.txt")
    print("output: ", output)
    print("writing to: ", ofile)
    f = open(ofile, "w")
    
    visited_graph = set()
    for idx, edge in enumerate(edges):
        print("index: ", idx, " start: ",
              edge[0], " end: ", edge[1], " edge_id: ", edge_dict[edge[0], edge[1]])
        start = edge[0]
        end = edge[1]
        edge_id = edge_dict[edge[0], edge[1]]
        f.write(edge_id + "\n")
        visited_graph.add(edge_id)
        
        for connected_edge_id in edge_connection_graph[start]:
            if connected_edge_id not in visited_graph:
                f.write(connected_edge_id + "\n")

        for connected_edge_id in edge_connection_graph[end]:
            if connected_edge_id not in visited_graph:
                f.write(connected_edge_id + "\n")
                       
    f.close()
    
    now = datetime.now()
    current_time = now.strftime("%H:%M:%S")

    image_path = os.path.join(output, "matching_result_" + current_time+".png")
    print(image_path)
    mmviz.plot_map(sd_map, matcher=matcher, zoom_path=True,
                   show_labels=False, show_matching=True,  show_graph=True,  # 这个选项可以用来控制是否显示全部link
                   filename="matching_result_" + current_time+".png")


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "map, poses, output")
        sys.exit()

    run_matching(sys.argv[1], sys.argv[2], sys.argv[3])
