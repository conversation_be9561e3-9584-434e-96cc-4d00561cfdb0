#!/usr/bin/python3.8

import rosbag
import sys
import struct

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto/")
from drivers.sensor_image_pb2 import CompressedImage
# from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from lock_on_road.lock_on_road_pb2 import LaneIndexEstimationResult, LockOnRoadResult
from routing.navinfo_routing_pb2 import SDRoutingResponse
from map.sd_map_pb2 import SDMapOnboard
from map.routing_pb2 import RoutingRequest
import cv2
import os
import numpy as np
from perception.deeproute_perception_ras_map_pb2 import RASMap
# fmt: on

# __lock_on_map_topic = "/localization/lane_mask"
__routing_mask_topic = "/localization/routing_mask"
__lane_mask_topic = "/localization/lane_mask"
__ras_map_topic = "/perception/ras_map"
__camera_1_topic = "/sensors/camera/camera_1_raw_data/compressed_proto"


def proto_compressed_to_cv_mat(proto_data):
    format = proto_data.format
    data = proto_data.data

    data_array = np.frombuffer(data, dtype=np.uint8)
    img_decode = cv2.imdecode(data_array, cv2.IMREAD_UNCHANGED)

    return img_decode


def strip_header(string_data):
    if len(string_data) < 4:
        return string_data
    if (
        string_data[0] != 36
        or string_data[1] != 36
        or string_data[2] != 36
        or string_data[3] != 36
    ):
        # without header
        return string_data
    else:
        # with header
        prefix_size = 4
        header_size_field_size = 4
        info_size = prefix_size + header_size_field_size
        header_size = struct.unpack(
            "I", string_data[prefix_size : prefix_size + header_size_field_size]
        )[0]
        return string_data[info_size + header_size :]


def ReadMessages(bag_file, out_file):

    cur_routing_mask_time = 0
    cur_ras_map_time = 0
    prev_time = 0
    prev_rasmap_time = 0
    with rosbag.Bag(bag_file) as bag:
        for topic, msg, t in bag.read_messages(
            topics=[__routing_mask_topic, __lane_mask_topic, __ras_map_topic], raw=True
        ):
            data = strip_header(msg[1][4:])

            if topic == __ras_map_topic:

                rasmap = RASMap()
                rasmap.ParseFromString(data)
                rasmap_time = rasmap.time_measurement / 1e6
                # print(rasmap_time)

                time_diff = rasmap_time - prev_rasmap_time
                prev_rasmap_time = rasmap_time

                cur_ras_map_time = rasmap_time

            elif topic == __routing_mask_topic:
                compressed_image = CompressedImage()
                compressed_image.ParseFromString(data)
                cur_time = compressed_image.header.timestamp_sec

                diff = cur_time - prev_time

                if diff < 0:
                    print("Error: ", cur_time, prev_time)

                prev_time = cur_time
                cur_routing_mask_time = cur_time

                routing_mask_img = proto_compressed_to_cv_mat(compressed_image)

                out_file_path = os.path.join(out_file, f"{cur_time}_routing_mask.png")
                cv2.imwrite(out_file_path, routing_mask_img)

            elif topic == __lane_mask_topic:
                compressed_image = CompressedImage()
                compressed_image.ParseFromString(data)
                cur_time = compressed_image.header.timestamp_sec
                diff = cur_time - prev_time

                if diff < 0:
                    print("Error: ", cur_time, prev_time)

                prev_time = cur_time
                cur_lane_mask_time = cur_time
                print(
                    f"cur_lane_mask_time {cur_lane_mask_time} - ras map time {cur_ras_map_time}: ",
                    cur_lane_mask_time - cur_ras_map_time,
                )

                lane_mask_img = proto_compressed_to_cv_mat(compressed_image)
                out_file_path = os.path.join(out_file, f"{cur_time}_lane_mask.png")
                cv2.imwrite(out_file_path, lane_mask_img)


if __name__ == "__main__":
    bag_path = "/home/<USER>/Downloads/ddrouting/2024-03-08-13-47-24.bag"
    ReadMessages(sys.argv[1], sys.argv[2])
