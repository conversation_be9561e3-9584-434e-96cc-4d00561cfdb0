import geojson
import geopandas
from matplotlib.pyplot import axis


import shapely.wkt
import shapely.ops


def reverse_geom(geom):
    def _reverse(x, y, z=None):
        if z:
            return x[::-1], y[::-1], z[::-1]
        return x[::-1], y[::-1]

    return shapely.ops.transform(_reverse, geom)

def convert_double_direction_geojson(in_path, out_path):
    map = geopandas.read_file(in_path)

    edge_num = len(map)
    print("edge num:", edge_num)
    print(map)
    
    # max_elements = map.max(axis=0) # wrong result
    # print(max_elements)
    max_edge_id = map['ID'].astype('float64').max()
    
    dest = geopandas.GeoDataFrame(
        columns=['MapID', 'ID', 'SnodeID', 'EnodeID', 'geometry'])

    map_id = 10086
    reverse_edge_row_index = edge_num
    for row_index, row in map.iterrows():
            
        dest.loc[row_index] = [map_id, row['ID'],
                                 row['SnodeID'], row['EnodeID'], row['geometry']]
        
        dest.loc[reverse_edge_row_index] = [map_id, str(int(max_edge_id)),
                                     row['EnodeID'], row['SnodeID'], reverse_geom(row['geometry'])]
        
        reverse_edge_row_index += 1
        max_edge_id += 1

    print("---------------------------------")
    print(dest[0:2])

    dest.to_file(out_path, driver="GeoJSON")
    print("write to: ", out_path)

if __name__ == '__main__':
    assert(geojson.__version__ == "2.5.0")
    in_path = "/home/<USER>/map_matching/data/sz_full/local_sd.geojson"
    out_path = "/home/<USER>/map_matching/data/sz_full/local_sd_double_edge.geojson"
    convert_double_direction_geojson(in_path, out_path)