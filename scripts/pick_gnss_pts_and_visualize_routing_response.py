from select import select
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from matplotlib.patches import Rectangle
from matplotlib.text import Text
from matplotlib.image import AxesImage
import numpy as np
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
from matplotlib.figure import Figure
import cv2 as cv
from PIL import Image
from numpy.random import rand

import rospy
from std_msgs.msg import String
np.random.seed(********)

import os
from extract_combined_poses_from_a_folder_of_bags import process_folder
        
# fmt: off
import sys
# sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
sys.path.insert(0, "/opt/deeproute/lock_on_road/include/proto")
from common.geometry_pb2 import PointLLH
from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from routing.routing_pb2 import RoutingResponse
# from map.routing_pb2 import RoutingRequest
# fmt: on


MapEngineBin = "/home/<USER>/map_matching/map-engine/build/routing/create_routing_response"


def create_image_from_latlon_txt(f):
    file = open(f).readlines()
    lats = []
    lons = []
    pts = []
    for line in file:
        line = line.split()
        lon = float(line[2])
        lat = float(line[1])
        lons.append(lon)
        lats.append(lat)
        pts.append([lat, lon])

    return pts, lats, lons


def ReadGpsFromTxtRoutingResponse(path):
    gps_pts = []
    file = open(path).readlines()
    for line in file:
        line = line.split()[1:]

        pts_num = int(len(line)/2)
        for i in range(pts_num):
            gps = [float(line[2*i]), float(line[2*i+1])]
            gps_pts.append(gps)

    return gps_pts


class MockRoutingResponse(object):

    def __init__(self):
        self.pts_ = None
        self.lats_ = None
        self.lons_ = None

        self.selected_indices_ = []
        self.selected_waypoints_ = []
        self.routing_response_gps_pts_ = []
        
        self.out_path_ = None
        
    def MakePlot(self):
        # self.fig_ = plt.figure
        self.fig_, ax = plt.subplots()

        ax.plot(self.lats_, self.lons_, '.',   picker=20, color='green')
        ax.plot(self.lats_[0], self.lons_[0], '8', color='red')
        ax.plot(self.lats_[-1], self.lons_[-1], 's', color="blue")
        
        self.highlight_, = ax.plot([], [], '*', color='yellow')
        self.routing_response_, = ax.plot([], [], '-', color='red')
        self.cid = plt.connect('pick_event', self.OnPick)
        # self.fig_.canvas.mpl_connect('press_even', self.OnPress)
        plt.show()

    def OnPick(self, event=None):
        ind = event.ind

        if(len(self.selected_waypoints_) == 13):
            print("3 points!")

            gps_arg = "'"
            gps_arg += str(self.selected_waypoints_[0][1]) + \
                " " + str(self.selected_waypoints_[0][0])
            for end_pt in self.selected_waypoints_[1:]:
                gps_arg += " "+str(end_pt[1]) + " " + str(end_pt[0])
            gps_arg += "'"

            self.routing_response_gps_pts_ = CallRoutingResponseMapEngine(
                MapEngineBin, gps_arg, self.out_path_)
            print("set RR finished.")

        if len(ind) > 1:
            datax, datay = event.artist.get_data()
            datax, datay = [datax[i] for i in ind], [datay[i] for i in ind]
            msx, msy = event.mouseevent.xdata, event.mouseevent.ydata
            dist = np.sqrt((np.array(datax)-msx)**2+(np.array(datay)-msy)**2)
            ind = [ind[np.argmin(dist)]]
            s = event.artist.get_gid()

            print(self.lats_[ind[0]], self.lons_[ind[0]])

            lat = self.lats_[ind[0]]
            lon = self.lons_[ind[0]]

            if(ind[0] not in self.selected_indices_):
                self.selected_indices_.append(ind[0])
                self.selected_waypoints_.append([lat, lon])

            # self.highlight.set_data(lat, lon)
            self.highlight_.set_data(np.array(self.selected_waypoints_)[
                                     :, 0], np.array(self.selected_waypoints_)[:, 1])

            if len(self.routing_response_gps_pts_):
                self.routing_response_.set_data(np.array(self.routing_response_gps_pts_)[
                                                :, 1], np.array(self.routing_response_gps_pts_)[:, 0])

            self.fig_.canvas.draw_idle()

    def ReadLatLonTxt(self, path):
        file = open(path).readlines()
        lats = []
        lons = []
        pts = []
        for line in file:
            line = line.split()
            lon = float(line[2])
            lat = float(line[1])
            lons.append(lon)
            lats.append(lat)
            pts.append([lat, lon])

        # return pts, lats, lons
        self.pts_ = pts
        self.lats_ = lats
        self.lons_ = lons

        self.out_path_ = os.path.join(os.path.dirname(path), "manually_picked_routing_response.txt")
        
    def ReadBagFolder(self, folder_path, routing_response_result_path):
        
        process_folder(folder_path, routing_response_result_path)
        gnss_poses = os.path.join(routing_response_result_path, "gnss_poses_longlat.txt")
        self.ReadLatLonTxt(gnss_poses)
        

def CallRoutingResponseMapEngine(bin_path, way_pts_arg, out_path):
    import os
    command = bin_path + " --latlon_arg " + way_pts_arg + " --out_path " + out_path
    print(command)
    os.system(command)

    gps_pts = ReadGpsFromTxtRoutingResponse(out_path)
    return gps_pts


if __name__ == "__main__":

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag_folder_path, routing_respoinse_path")
        sys.exit()

    mock = MockRoutingResponse()
    mock.ReadBagFolder(sys.argv[1], sys.argv[2])
    # mock.ReadLatLonTxt(sys.argv[1])
    mock.MakePlot()
