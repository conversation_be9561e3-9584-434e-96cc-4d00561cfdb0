#!/usr/bin/python3.8
from google.protobuf import text_format
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, Feature, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj
import os

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
from map.routing_pb2 import RoutingRequest
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on


def convert(wps_txt):

    f = open(wps_txt, "r")
    rr = RoutingRequest()
    rr = text_format.Parse(f.read(), rr)

    import os
    out_path = os.path.join(os.path.dirname(
        wps_txt), "way_points_gcj02.geojson")
    out_path2 = os.path.join(os.path.dirname(
        wps_txt), "way_points_wgs84.geojson")

    features = []

    request_info = rr.request_info
    features.append(
        Feature(geometry=Point((request_info.start_point.lon, request_info.start_point.lat)), properties={"ID": 0}))

    proj = GcjToLocalUtmTransformer(
        origin_longitude=107.633242, origin_latitude=29.988549)

    idx = 1
    for end_pt in request_info.end_points:
        features.append(
            Feature(geometry=Point((end_pt.lon, end_pt.lat)), properties={"ID": idx}))

        # wgs_lon, wgs_lat = proj.gcj02_to_wgs84(end_pt.lon, end_pt.lat)

        idx += 1

    feature_collection = FeatureCollection(features)

    with open(out_path, 'w') as f:
        dump(feature_collection, f)

        

if __name__ == '__main__':

    import sys
    if len(sys.argv) != 2:
        print(sys.argv[0], "wps_file_path")
        sys.exit()

    convert(sys.argv[1])
