#!/usr/bin/python3.8
import geojson
import numpy as np
import pprint
import rosbag
import rospy
import sys
from sklearn.neighbors import KDTree
from geojson import Point, Feature, LineString, MultiPoint, FeatureCollection, dump
from siwei_geojson_to_dr_coor import LocalUtmTransformer, GcjToLocalUtmTransformer
from pygcj.pygcj import GCJProj
import os

# fmt: off
sys.path.insert(0, "/opt/deeproute/common/common-protocol/include/proto")
# from semantic_map.map_pb2 import Map
from drivers.gnss.ins_pb2 import Ins
from drivers.gnss.ins_pb2 import SensorsIns
from drivers.gnss.gnss_pb2 import Gnss
from routing.routing_pb2 import RoutingResponse
from lock_on_road.lock_on_road_debug_pb2 import LockOnRoadSinglePoseDebugInfo, LockOnRoadDebugInfo
from routing.navinfo_routing_pb2 import SDRoutingResponse
# fmt: on


class BagWalker(object):
    __local_responses = []
    __localization_poses = []
    __gnss_poses = []
    __debug_messages = []
    __localization_topic = "/localization/pose"
    __planning_topic = "/planner/routing_response"
    __debug_topic = "/localization/lock_on_road_debug"
    __gnss_topic = "/sensors/gnss/pose"
    __rr_topic = "/map/routing/internal/response"
    __bag_path = None
    __routing_responses = []

    def __init__(self, path):
        # self.region_lat = region_lat
        # self.region_lon = region_lon
        # self.__utm_transformer = LocalUtmTransformer(region_lon, region_lat)
        self.ReadMessages(path)

    def ReadMessages(self, path):
        topic_list = [self.__gnss_topic, self.__debug_topic, self.__rr_topic]

        print("Processing: ", path)

        idx = 0
        with rosbag.Bag(path) as bag:
            for topic, msg, t in bag.read_messages(topics=topic_list, raw=True):
                idx += 1

                # if idx % 10 != 0:
                # continue

                # print(topic)
                # skip first 4 bytes
                data = msg[1][4:]
                if topic == self.__gnss_topic:
                    gnss = SensorsIns()
                    gnss.ParseFromString(data)
                    self.__gnss_poses.append(gnss)
                elif topic == self.__planning_topic:
                    routing_response = RoutingResponse()
                    routing_response.ParseFromString(data)
                    self.__local_responses.append(routing_response)
                elif topic == self.__debug_topic:
                    lock_on_road_debug = LockOnRoadDebugInfo()
                    lock_on_road_debug.ParseFromString(data)
                    self.__debug_messages.append(lock_on_road_debug)
                elif topic == self.__rr_topic:
                    rr = SDRoutingResponse()
                    rr.ParseFromString(data)
                    self.__routing_responses.append(rr)

        print("Loaded: ", len(self.__debug_messages), " debug messages")
        print("Loaded: ", len(self.__gnss_poses), " gnss poses")
        print("Loaded: ", len(self.__routing_responses), " routing responses")

    def GetAllRoutingResponses(self):
        return self.__local_responses

    def GetAllLocalizationPoses(self, skip_step=10):
        if not skip_step:
            return self.__localization_poses
        skipped = []
        for idx, pose in enumerate(self.__localization_poses):
            if idx % skip_step == 0:
                skipped.append(pose)

        return skipped

    def GetAllGnssPoses(self):
        return self.__gnss_poses
    
    def WriteRRToFile(self, root_path):

        for rr in self.__routing_responses:
            temp_path = root_path

            path = os.path.join(temp_path, rr.request_id + "_rr.cfg")
                    
            f = open(path, 'w')
            f.write(str(rr))
            f.close()

    def WriteLockOnRoadDebugToGeoJson(self, path):
        features = []

        path = os.path.join(path, "lock_on_road_debug.geojson")
        matched_points = []
        for debugs in self.__debug_messages:
            debug = debugs.pose_debug_info[-1]
            
            print(debug)
            # if not hasattr(debug, "time"):
                # continue

            # print(debug)
            
            start = Point((debug.position.x, debug.position.y))
            target = Point((debug.matched_position.x,
                           debug.matched_position.y))
            line = LineString([(debug.position.x, debug.position.y),
                               (debug.matched_position.x,
                                debug.matched_position.y)])
            # features.append(Feature(geometry=start, properties={"marker-color": "#fff"}))
            # features.append(Feature(geometry=target, properties={"marker-color": "#fff"}))
            # features.append(Feature(geometry=line, properties={"marker-color": "#fff"}))
            features.append(Feature(geometry=start, properties={
                            "timestamp": debug.time}))
            # features.append(Feature(geometry=target, properties={
            #                 "fillColor": "#228B22", "lineColor": "#228B22"}))
            features.append(Feature(geometry=line, properties={
                            "fillColor": "#228B22", "lineColor": "#228B22"}))
            matched_points.append(
                [debug.matched_position.x, debug.matched_position.y])

        # points = MultiPoint([pt[0], pt[1]] for pt in matched_points)
        # features.append(Feature(geometry=points, properties={
        #                 "fillColor": "#228B22", "lineColor": "#228B22"}))

        feature_collection = FeatureCollection(features)
        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WritePosesUtmToGeoJson(self, path):
        assert(len(self.__localization_poses))
        features = []

        path = os.path.join(path, "localization_poses_utm.geojson")
        for ins in self.__localization_poses:
            point = Point((ins.position.x, ins.position.y))
            features.append(
                Feature(geometry=point, properties={"timestamp": ins.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WritePosesUtmToTxt(self, path):
        assert(len(self.__localization_poses))

        path = os.path.join(path, "localization_poses_utm.txt")
        f = open(path, "a")
        for ins in self.__localization_poses:
            f.write(str(ins.measurement_time) + " " +
                    str(ins.position.x) + " " +
                    str(ins.position.y) + " " +
                    str(ins.position.z) + " " +
                    str(ins.linear_velocity_enu.x) + " " +
                    str(ins.linear_velocity_enu.y) + " " +
                    str(ins.linear_velocity_enu.z) + " " +
                    str(ins.angular_velocity_enu.x) + " " +
                    str(ins.angular_velocity_enu.y) + " " +
                    str(ins.angular_velocity_enu.z) + " " +
                    str(ins.euler_angles.x) + " " +
                    str(ins.euler_angles.y) + " " +
                    str(ins.euler_angles.z) + " " +
                    "\n")

        f.close()

    def WritePosesLatLongToTxt(self, path):
        assert(len(self.__localization_poses))

        path = os.path.join(path, "localization_poses_latlong.txt")
        f = open(path, "a")
        for ins in self.__localization_poses:
            f.write(str(ins.measurement_time) + " " +
                    str(ins.position_llh.lat) + " " +
                    str(ins.position_llh.lon) + "\n")

        f.close()

    def WriteGnssMeasurementsUtmToTxt(self, path):
        assert(len(self.__gnss_poses))

        path = os.path.join(path, "gnss_poses_utm.txt")
        f = open(path, "a")
        for gnss in self.__gnss_poses:
            # print(gnss)

            utm = self.__utm_transformer.LlToLocalUtm(
                lat=gnss.vehicle_frame_position_llh.lat, lon=gnss.vehicle_frame_position_llh.lon)

            f.write(str(gnss.measurement_time) + " " +
                    str(utm[0]) + " " +
                    str(utm[1]) + " " +
                    str(gnss.vehicle_frame_position_llh.height)
                    + "\n")

        f.close()

    def WriteGnssMeasurementsUtmToGeojson(self, path):
        if(len(self.__gnss_poses) == 0):
            return

        features = []

        path = os.path.join(path, "gnss_poses_utm.geojson")
        for gnss in self.__gnss_poses:

            utm = self.__utm_transformer.LlToLocalUtm(
                lat=gnss.imu_frame_position_llh.lat, lon=gnss.imu_frame_position_llh.lon)

            point = Point((utm[0], utm[1]))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WriteGnssMeasurementsLatLongToTxt(self, path):
        if(len(self.__gnss_poses) == 0):
            return False

        path = os.path.join(path, "gnss_poses_longlat.txt")
        f = open(path, "a")
        for gnss in self.__gnss_poses:
            # print(gnss)
            # f.write(str(gnss.measurement_time) + " " +
            # str(gnss.vehicle_frame_position_llh.lon) + " " +
            # str(gnss.vehicle_frame_position_llh.lat) + " " +
            # str(gnss.vehicle_frame_position_llh.height) + "\n")
            f.write(str(gnss.measurement_time) + " " +
                    str(gnss.imu_frame_position_llh.lon) + " " +
                    str(gnss.imu_frame_position_llh.lat) + " " +
                    str(gnss.imu_frame_position_llh.height) + "\n")
        f.close()

    def WriteGnssLatLonToGeoJson(self, path):
        assert(len(self.__gnss_poses))
        features = []

        path = os.path.join(path, "gnss_poses_latlon.geojson")
        for gnss in self.__gnss_poses:

            # print(gnss)
            point = Point((gnss.imu_frame_position_llh.lat,
                          gnss.imu_frame_position_llh.lon))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WriteGnssLonLatToGeoJson(self, path):
        assert(len(self.__gnss_poses))
        features = []

        path = os.path.join(path, "gnss_poses_lonlat.geojson")
        for gnss in self.__gnss_poses:

            # print(gnss)
            point = Point((gnss.imu_frame_position_llh.lon,
                          gnss.imu_frame_position_llh.lat))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)

    def WriteGnssLonLatGCJ02ToGeoJson(self, path):
        if(len(self.__gnss_poses) == 0):
            return
        features = []

        gcj = GcjToLocalUtmTransformer(self.region_lon, self.region_lat)

        path = os.path.join(path, "gnss_poses_lonlat.geojson")
        for gnss in self.__gnss_poses:

            gcj_lat, gcj_lon = gcj.WgsToGcj(
                lon=gnss.imu_frame_position_llh.lon, lat=gnss.imu_frame_position_llh.lat)
            point = Point((gcj_lon, gcj_lat))
            features.append(
                Feature(geometry=point, properties={"timestamp": gnss.measurement_time}))

        feature_collection = FeatureCollection(features)

        with open(path, 'w') as f:
            dump(feature_collection, f)


def GetRoadIdsFromRoutingResponse(response):
    ids = []
    for road in response.road:
        ids.append(road.id)
    return ids


def LoadLaneDictFromSemanticMap(path):
    file = open(path, 'rb')
    map = Map()
    map.ParseFromString(file.read())

    lane_dict = {}
    for lane in map.lane:
        lane_dict[str(lane.id)] = [point for point in lane.centerline.point]

    print("Total semantic map lane num: ", len(lane_dict))
    return lane_dict


def GetSumPointsFromSemanticMapAndLaneIds(lane_dict, lane_ids):
    total_center_pts = []
    success = 0
    failed = 0
    for lane_id in lane_ids:
        if lane_id in lane_dict:
            total_center_pts += lane_dict[lane_id]
            success += 1
        else:
            failed += 1
            print(lane_id, " doesnt exist in the lane dict.")
    print("Successful: ", success)
    print("Failed: ", failed)
    return total_center_pts


def GetCenterLineByLaneId(lane_dict, id):
    return lane_dict[id]


def ConvertPointsToGeoJson(points):
    assert(len(points))
    features = []

    for point in points:
        point = Point((point.x, point.y))
        features.append(
            Feature(geometry=point, properties={"country": "China"}))

    print("Added ", len(features), " points!")
    feature_collection = FeatureCollection(features)

    with open('routing_response_lane_center_points.geojson', 'w') as f:
        dump(feature_collection, f)


def WriteFeaturesToFile(features, path):
    feature_collection = FeatureCollection(features)
    # pprint.pprint(feature_collection)
    with open(path, 'w') as f:
        dump(feature_collection, f)


if __name__ == '__main__':

    import sys
    if len(sys.argv) == 1:
        print(sys.argv[0], "bag output")
        sys.exit()

    if not os.path.exists(sys.argv[2]):
        os.mkdir(sys.argv[2])

    bw = BagWalker(sys.argv[1])

    # save localization poses to geo json file
    # bw.WriteGnssMeasurementsUtmToTxt(sys.argv[2])
    # bw.WriteGnssMeasurementsUtmToGeojson(sys.argv[2])
    # bw.WriteGnssMeasurementsLatLongToTxt(sys.argv[2])
    # bw.WriteGnssLatLonToGeoJson(sys.argv[2])
    # bw.WritePosesUtmToTxt(sys.argv[2])
    # bw.WritePosesUtmToGeoJson(sys.argv[2])
    # bw.WritePosesLatLongToTxt(sys.argv[2])
    
    bw.WriteRRToFile(sys.argv[2])

    # # get all routing responses, find all lanes corresponding to the 1st routing response
    # # and save lane center line points
    # all_routing_responses = bw.GetAllRoutingResponses()
    # # get corresponding lane ids of the 1st routing response
    # planned_road_ids = GetRoadIdsFromRoutingResponse(all_routing_responses[0])
    # # get all lanes from semantic map
    # lane_dict = LoadLaneDictFromSemanticMap(
    #     "/media/songhaoran/data/lane_loc_data/semantic-map.bin")
    # # get planned lane center points
    # sum_pts = GetSumPointsFromSemanticMapAndLaneIds(
    #     lane_dict, planned_road_ids)
    # # save all routing response center points
    # ConvertPointsToGeoJson(sum_pts)

    # gjt = GeoJsonTools(
    #     "/home/<USER>/lane-based-localization-benchmark/data/siwei_sd_small.geojson")
    # feature = gjt.GetFeatureById('1001050')
    # all_links = gjt.FindAdjascentLinksByPoses(sum_pts)
    # print("num of links found: ", len(all_links))
    # WriteFeaturesToFile(all_links, "queried_links.geojson")
